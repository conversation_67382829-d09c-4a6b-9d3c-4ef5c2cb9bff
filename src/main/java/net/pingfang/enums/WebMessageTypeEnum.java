package net.pingfang.enums;

import lombok.Getter;

/**
 * 发送给web的消息类型
 */
@Getter
public enum WebMessageTypeEnum {
     PLC(1, "PLC"),
     CTN(2, "箱"),
     PLATE_NUMBER(3, "车牌"),
     TOP_PLATE(4, "车顶号"),

    ;

    private Integer value;
    private String desc;

    WebMessageTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDescByValue(Integer value){
        if (value == null){
            return null;
        }

        for (WebMessageTypeEnum imgTypeEnum : WebMessageTypeEnum.values()) {
            if (imgTypeEnum.getValue().equals(value)) {
                return imgTypeEnum.getDesc();
            }
        }
        return null;
    }
}
