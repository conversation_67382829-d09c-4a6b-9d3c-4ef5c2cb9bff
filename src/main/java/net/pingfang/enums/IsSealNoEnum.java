package net.pingfang.enums;

import lombok.Getter;

/**
 * 是否有铅封
 * <AUTHOR>
 * @since 2023-10-09 10:49
 */
@Getter
public enum IsSealNoEnum {

    YES(3, "有铅封","true"),
    NO(2, "无铅封","false"),
    UNKNOWN(1, "未知",""),
    ;

    private Integer code;
    private String codeDesc;
    private String codeDescOne;


    IsSealNoEnum(Integer code, String codeDesc,String codeDescOne) {
        this.code = code;
        this.codeDesc = codeDesc;
        this.codeDescOne = codeDescOne;
    }

    public static String getCodeDescOneByCode(Integer code) {
        for (IsSealNoEnum isSealNoEnum : IsSealNoEnum.values()) {
            if (isSealNoEnum.getCode().equals(code)) {
                return isSealNoEnum.getCodeDescOne();
            }
        }
        return null;
    }

    public static String getCodeDescByCode(Integer code) {
        for (IsSealNoEnum isSealNoEnum : IsSealNoEnum.values()) {
            if (isSealNoEnum.getCode().equals(code)) {
                return isSealNoEnum.getCodeDesc();
            }
        }
        return null;
    }

    public static Integer getCodeByBool(Boolean desc) {
        if(null != desc){
            if(desc){
                return IsSealNoEnum.YES.getCode();
            }else{
                return IsSealNoEnum.NO.getCode();
            }
        }
        return IsSealNoEnum.UNKNOWN.getCode();
    }

}
