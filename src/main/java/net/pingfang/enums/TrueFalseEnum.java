package net.pingfang.enums;

import lombok.Getter;

/**
 * 1-是0-否
 */
@Getter
public enum TrueFalseEnum {
    YES(1, "是", "true", "yse"),
    NO(0, "否", "false", "no"),
    ;

    private Integer value;
    private String desc;
    private String descExtOne;
    private String descExtTwo;

    TrueFalseEnum(Integer value, String desc, String descExtOne, String descExtTwo) {
        this.value = value;
        this.desc = desc;
        this.descExtOne = descExtOne;
        this.descExtTwo = descExtTwo;
    }

    public static String getDescByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (TrueFalseEnum trueFalseEnum : TrueFalseEnum.values()) {
            if (trueFalseEnum.getValue().equals(value)) {
                return trueFalseEnum.getDesc();
            }
        }
        return null;
    }

    public static Integer getValueByDesc(String desc) {
        for (TrueFalseEnum trueFalseEnum : TrueFalseEnum.values()) {
            if (trueFalseEnum.getDesc().equalsIgnoreCase(desc) || trueFalseEnum.getDescExtOne().equalsIgnoreCase(desc) || trueFalseEnum.getDescExtTwo().equalsIgnoreCase(desc)) {
                return trueFalseEnum.getValue();
            }
        }
        return null;
    }

    public static Integer getValueByBool(Boolean desc) {
        if (desc) {
            return TrueFalseEnum.YES.getValue();
        }
        return TrueFalseEnum.NO.getValue();
    }
}
