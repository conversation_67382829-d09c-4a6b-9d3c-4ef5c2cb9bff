package net.pingfang.enums;

import lombok.Getter;

/**
 * 作业类型（1-装船；2-卸船；10-未知；11-其他）
 * <AUTHOR>
 * @Date 2023/7/17 15:42
 * @Description:
*/
@Getter
public enum WorkTypeEnum {

    LOAD(1, 0,"装船"),
    DISCHARGE(2, 1,"装船"),
    HATCH_COVER_OPENING(3,3, "舱盖开舱"),
    HATCH_COVER_CLOSING(4, 4,"舱盖关舱"),
    CABIN_OVERTURN(5, 5, "舱内翻倒"),
    OUT_OF_LANE_OVERTURN(6, 6,"车道外翻倒"),
    OUT_OF_LANE_AND_IN_LANE_BOX_TRANSFER(7, 7,"车道外内倒箱"),
    LANE_INSIDE_AND_OUTSIDE_BOX_TRANSFER(8, 8,"车道内外倒箱"),
    LOAD_BRIDGE(9, 9, "寄桥(装)"),
    UNKNOWN(10, 2,"未知"),
    OTHER(11, 10,"其他"),
    ;

    private Integer value;
    // 发送给理货系统value（应用组对应的code）
    private Integer netValue;
    private String desc;


    WorkTypeEnum(Integer value,Integer netValue,String desc) {
        this.value = value;
        this.netValue = netValue;
        this.desc = desc;
    }

    public static Integer getNetValueByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (WorkTypeEnum workTypeEnum : WorkTypeEnum.values()) {
            if (workTypeEnum.getValue().equals(value)) {
                return workTypeEnum.getNetValue();
            }
        }
        return null;
    }

    public static String getDescByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (WorkTypeEnum workTypeEnum : WorkTypeEnum.values()) {
            if (workTypeEnum.getValue().equals(value)) {
                return workTypeEnum.getDesc();
            }
        }
        return null;
    }

    public static Integer getValueByDesc(String desc) {
        for (WorkTypeEnum workTypeEnum : WorkTypeEnum.values()) {
            if (workTypeEnum.getDesc().equalsIgnoreCase(desc)) {
                return workTypeEnum.getValue();
            }
        }
        return null;
    }
}
