package net.pingfang.enums;

import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * 箱门朝向
 * <AUTHOR>
 * @since 2023-10-09 11:02
 */
@Getter
public enum DoorDirEnum {
    F(1, "前","F"),
    A(2, "后", "A"),
    ;

    private Integer value;
    private String desc;
    private String enDesc;


    DoorDirEnum(Integer value, String desc, String enDesc) {
        this.value = value;
        this.desc = desc;
        this.enDesc = enDesc;
    }

    public static String getDescByValue(Integer value){
        if (value == null){
            return null;
        }
        for (DoorDirEnum doorDirEnum : DoorDirEnum.values()) {
            if (doorDirEnum.getValue().equals(value)) {
                return doorDirEnum.getDesc();
            }
        }
        return null;
    }

    public static String getEndescByValue(Integer value){
        if (value == null){
            return null;
        }
        for (DoorDirEnum doorDirEnum : DoorDirEnum.values()) {
            if (doorDirEnum.getValue().equals(value)) {
                return doorDirEnum.getEnDesc();
            }
        }
        return null;
    }

    public static Integer getValueByDesc(String desc){
        if (StringUtils.isEmpty(desc)){
            return null;
        }
        for (DoorDirEnum doorDirEnum : DoorDirEnum.values()) {
            if (doorDirEnum.getDesc().equalsIgnoreCase(desc) || doorDirEnum.getEnDesc().equalsIgnoreCase(desc)) {
                return doorDirEnum.getValue();
            }
        }
        return null;
    }
}
