package net.pingfang.enums;

import lombok.Getter;

/**
 * 图片类型(1-箱,2-残损，3-车顶号，4-车号，5-,全景)
 */
@Getter
public enum ImgTypeEnum {
     CONTAINER(1, "箱"),
     DAMAGE(2, "残损"),
     TOP_PLATE(3, "车顶号"),
     PLATE(4, "车号"),
     BAY(5, "全景"),
     LOCK(6, "上锁"),
     UNLOCK(7, "解锁"),
    ;

    private Integer value;
    private String desc;

    ImgTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDescByValue(Integer value){
        if (value == null){
            return null;
        }

        for (ImgTypeEnum imgTypeEnum : ImgTypeEnum.values()) {
            if (imgTypeEnum.getValue().equals(value)) {
                return imgTypeEnum.getDesc();
            }
        }
        return null;
    }
}
