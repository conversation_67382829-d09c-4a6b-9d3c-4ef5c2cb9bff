package net.pingfang.core.flow;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.constant.CommonConstant;
import net.pingfang.constant.MdcConstant;
import net.pingfang.core.DllPath;
import net.pingfang.core.algorithm.ailib.ContainerAlgorithmService;
import net.pingfang.core.algorithm.ailib.TopPlateAlgorithmService;
import net.pingfang.core.algorithm.ailib.model.*;
import net.pingfang.core.algorithm.plate.PlateThreeAlgorithmService;
import net.pingfang.core.algorithm.plate.model.PlateThreeResult;
import net.pingfang.core.hardware.camera.hikvision.HikvisionService;
import net.pingfang.core.hardware.camera.hikvision.HikvisionServiceFactory;
import net.pingfang.core.hardware.plc.Plc;
import net.pingfang.enums.*;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.GpsDto;
import net.pingfang.model.dto.MessageDTO;
import net.pingfang.model.entity.Camera;
import net.pingfang.model.entity.Img;
import net.pingfang.model.entity.Lane;
import net.pingfang.model.entity.Record;
import net.pingfang.model.vo.business.ContainerMessageVO;
import net.pingfang.model.vo.business.RecognizeConfigVO;
import net.pingfang.model.vo.business.TopPlateMessageVO;
import net.pingfang.repository.CameraRepository;
import net.pingfang.repository.ImgRepository;
import net.pingfang.repository.RecognitionRepository;
import net.pingfang.repository.RecognizeConfigRepository;
import net.pingfang.service.EcsService;
import net.pingfang.service.IdentifyService;
import net.pingfang.service.external.gkpt.GkptService;
import net.pingfang.service.external.gkpt.dto.SendIdentifyReqDTO;
import net.pingfang.model.dto.EcsIdentifyInfoDTO;
import net.pingfang.util.*;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.File;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 核心控制流程
 *
 * @author: CM
 * @date: 2023/5/31
 */
@Component
@Slf4j
public class CoreFlow {

    //开启识别的高度
    @Value("${identity.height:5000}")
    public Integer identityHeight;

    //发送等待处理箱号和车顶号时间
    @Value("${external.api.waitDealTime}")
    public Long waitDealTime;

    @Autowired
    private HikvisionServiceFactory hikvisionServiceFactory;

    @Autowired
    private ContainerAlgorithmService containerAlgorithmService;

    @Autowired
    private TopPlateAlgorithmService topPlateAlgorithmService;

    @Autowired
    private IdentifyService identifyService;

    @Autowired
    private RecognitionRepository recognitionRepository;

    @Autowired
    private ImgRepository imgRepository;

    @Autowired
    private RecognizeConfigRepository recognizeConfigRepository;

    @Autowired
    private WebSocketUtil webSocketUtil;

    @Autowired
    private PathConfigUtil pathConfigUtil;

    @Autowired
    private ExternalConfigUtil externalConfigUtil;

    @Autowired
    private CameraRepository cameraRepository;
    @Autowired
    private DllPath dllPath;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private RestTemplateUtil restTemplateUtil;
    @Autowired
    private GkptService gkptService;

    @Autowired
    private EcsService ecsService;

    private static String submitCtnNo = null;

    private static String submitSeqNo = null;

    @Value("${ecs.api.sync:false}")
    private String ecsSyncEnabled;

    @Value("${algorithm.flag}")
    public Boolean algorithmFlag;

    @Value("${algorithm.ifCheckTopPlateOnlyVehicleLane}")
    public Boolean ifCheckTopPlateOnlyVehicleLane;

    /**
     * 记录每次作业的最高位
     */
    public static final ConcurrentHashMap<String,Integer> seqHeightMap = new ConcurrentHashMap<>();

    //车顶号识别结果集
    private List<RecTopPlate> topPlateList = new ArrayList<>();

    public static String gpsData = null;

    public static PlateThreeResult plateThreeResult = null;

    public static ExecutorService executors = Executors.newCachedThreadPool();


    @Value("${picture.path:D:/pfkj/img/}")
    public String fileDirPath;

    @Value("${algorithm.ifCheckTopPlateByPanorama}")
    public Boolean ifCheckTopPlateByPanorama;
    @Value("${backBox.locationChange}")
    public Integer backBoxLocationChange;
    @Value("${gps.url}")
    public String gpsUrl;
    @Value("${gps.imei}")
    private String imei;


    //iso 长度
    private static final Integer ISO_LENGTH = 4;

    /**
     * 开闭锁状态
     */
    private volatile boolean isLock = false;

    /**
     * 是否到达识别高度
     */
    private volatile boolean reachRecgHigh = false;

    /**
     * 识别中
     */
    private volatile boolean isProcessing = false;

    private List<GpsDto> gpsDtoList;

    private static volatile boolean isSending = false;

    public static Map<String, List<RecognizeConfigVO>> rcMap = null;

    /**
     * 判断是否处于识别流程中
     */
    public boolean checkIsProcessing() {
        return isProcessing;
    }

    /**
     * 结束识别流程
     */
    public void stop() {
        isLock = false;
    }

    public static final ConcurrentHashMap<String,Boolean> ifDealTopPlate = new ConcurrentHashMap<>();

    public static final ConcurrentHashMap<String,Boolean> ifDealCtnNum = new ConcurrentHashMap<>();



    /**
     * 初始化算法
     */
    @PostConstruct
    public void initAlgorithm() {
        if (algorithmFlag){
            log.info("即将进行算法初始化！");

            // 箱号算法服务初始化
            containerAlgorithmService.init();
            // 车顶号识别算法初始化
            topPlateAlgorithmService.init();
            // 开启车牌识别
           // detectPlateNumber();
        }else {
            log.info("不进行算法初始化！");
        }

    }

    /**
     * 车牌识别持续轮询进行
     */
    public void detectPlateNumber() {
        List<Camera> cameraList = cameraRepository.queryByType(CameraTypeEnum.GUN.getValue());
        if (CollectionUtils.isEmpty(cameraList)) {
            log.info("未获取到任何枪机,无法启动视频流检测,请检查数据库是否有配置车牌识别枪机");
            return;
        }

        executors.submit(() -> {
            Timer timer = new Timer();
            for (int index = 0; index < cameraList.size(); index++) {
                PlateThreeAlgorithmService ps = new PlateThreeAlgorithmService(dllPath.getPlateThree());
                ps.initThreePlateSdk();
                ps.addCamera(cameraList.get(index).getIp());
                ps.addCallback(cameraList.get(index).getIp(), fileDirPath, redisUtil, webSocketUtil);
                int finalIndex = index;
                timer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        try {
                            ps.softTrigger(cameraList.get(finalIndex).getIp());
                        } catch (Throwable e) {
                            //print the error log
                            log.error("软触发异常:{}", ExceptionUtil.getStackTrace(e));
                        }
                    }
                }, 5000L, 1500L);
            }
        });

    }

    /**
     * 此类为业务流程控制的入口,重要节点的流程控制均在此类中
     *
     * @param flowType 1-上锁, 2-升高到识别点  3,下降低于识别点, 4.解锁
     */
    public void startDetect(Integer containerType, Integer workType, String seqNo, String passTime, int flowType, Lane lane, Plc lastPlc) {
        if (seqNo == null) return;

        if (rcMap == null) {
            List<RecognizeConfigVO> rclist = recognizeConfigRepository.queryAll();
            if (CollectionUtils.isEmpty(rclist)) {
                log.info("未获取到任何识别配置,无法进入到识别流程");
                return;
            }
            rcMap = rclist.stream().collect(Collectors.groupingBy(item -> item.getType() + "_" + item.getLaneId()));
        }

        // 分三个步骤, 上锁|识别|解锁

        //吊具上锁
        if (flowType == 1) {
            lockFlow(containerType, workType, seqNo, passTime, lane);
            return;
        }

        // ========== 空中识别代码（已注释） ==========
        /*
        // 吊具高度到达识别高度
        if (flowType == 2) {
            reachRecgHigh = true;
            startDetectFlow(containerType,workType,seqNo, lane);
        }
        // 吊具高度低于识别高度
        if (flowType == 3) {
            //设置低于识别高度，将会停止视频识别流
            this.reachRecgHigh = false;
            this.isProcessing = false;
        }
        */
        log.info("{}空中识别已禁用 - flowType:{}", seqNo, flowType);

        // 解锁处理
        if (flowType == 4) {
            //解锁流程
            unLockFlow(workType, seqNo, lane ,lastPlc);
        }
    }

    /**
     * 开启解锁流程
     */
    private void unLockFlow(Integer workType, String seqNo, Lane lane, Plc lastPlc) {
        // 为了防止一直处于识别高度以上,这里解锁时主动断开识别流程
        this.reachRecgHigh = false;
        this.isLock = false;
        this.isProcessing = false;

        //解锁抓拍，全景相机
        startSnapFlowUnlock(lane.getId(),seqNo);
        String unlockTime = DateUtils.getTime();
        if (!StringUtils.isEmpty(seqNo)) {
            // 更新
            Record record = new Record();
            record.setSeqNo(seqNo);
            //作为解锁车道
            record.setLaneNumB(lane.getLane());
            record.setWorkType(workType);
            record.setUnlockTime(unlockTime);
            recognitionRepository.updateBySeqNo(record);
        }
        // 抓拍车顶号
        snapTopPlate(lane,seqNo,"unLock");

    }
    /**
     * 开启上锁流程
     */
    private void lockFlow(Integer containerType, Integer workType, String seqNo, String passTime, Lane lane) {
        this.isLock = true;
        this.isProcessing = false;
        String lockTime = DateUtils.getTime();
        // 没有seqNo的记录时，先存一条新的识别记录到数据库，否则仅更新而已
        Record oldRecord = recognitionRepository.queryBySeqNo(seqNo);
        if (oldRecord == null) {
            Record record = new Record();
            record.setId(SnowFlakeUtil.getDefaultSnowFlakeId());
            record.setSeqNo(seqNo);
            record.setContainerType(containerType);
            record.setWorkType(workType);
            record.setLockTime(lockTime);
            //作为上锁车道
            record.setLaneNumA(lane.getLane());
            record.setPassTime(DateUtil.parseLocalDateTime(passTime, DateUtil.TYPE_THREE));
            recognitionRepository.add(record);
        }else {
            oldRecord.setContainerType(containerType);
            oldRecord.setWorkType(workType);
            oldRecord.setLockTime(lockTime);
            //作为上锁车道
            oldRecord.setLaneNumA(lane.getLane());
            oldRecord.setPassTime(DateUtil.parseLocalDateTime(passTime, DateUtil.TYPE_THREE));
            recognitionRepository.update(oldRecord);
        }
        // 上锁抓拍
        startSnapFlowLock(lane.getId(),seqNo);

        // 调用识别预置位
        // 获取车道识别配置信息,包括摄像头的具体信息
        Integer type = ContainerTypeEnum.getRecognizeTypeByValue(containerType);
        List<RecognizeConfigVO> rcList = rcMap.get(type + "_" + lane.getId());
        if (!CollectionUtils.isEmpty(rcList)) {
            setPosition(rcList,seqNo);
        }

        // 抓拍车顶号
        snapTopPlate(lane, seqNo,"lock");

    }

    /**
     * 抓拍并识别车顶号
     */
    private void snapTopPlate(Lane lane,String seqNo,String lockType) {
        List<RecognizeConfigVO> rcList = rcMap.get(RecognizeConfigTypeEnum.PLATE_TOP.getValue() + "_" + lane.getId());

        if (CollectionUtils.isEmpty(rcList)) {
            log.info("未查询到任何车顶号相机配置,无法抓拍车顶号");
            return;
        }
        executors.submit(() -> {
            for (RecognizeConfigVO item : rcList) {
                try {
                    log.info("{},{}开始抓拍车顶号", seqNo, item.getIp());
                    // 相机注册
                    HikvisionService service = hikvisionServiceFactory.getService(item.getIp());
                    boolean registerResult = service.register(item.getIp(), item.getControlPort() == null ? 8000 : item.getControlPort(), item.getUsername(), item.getPassword());
                    if (!registerResult) {
                        continue;
                    }
                    // 跳转预置位
                    if (item.getPresetLocation() != null && item.getPresetLocation() != 0) {
                        boolean setPositionResult = service.setPosition(item.getChannel() == null ? 1 : item.getChannel(), 39, item.getPresetLocation());
                    } else {
                        log.info("相机:{}未设置预置位", item.getName());
                    }
                    // 抓拍
                    // 抓拍图片
                    String packagePath = pathConfigUtil.getRmgName() + "/" + DateUtil.getDate(DateUtil.TYPE_EIGHT) + "/" + seqNo + "/" + seqNo + "_" + item.getCode() + lockType + ".jpg";
                    log.info("图片路径：{}",packagePath);
                    String filePath = this.fileDirPath + packagePath;
                    log.info("图片目录：{}",filePath);
                    File file = new File(filePath);
                    if (!file.getParentFile().exists()) {
                        //创建上级目录
                        file.getParentFile().mkdirs();
                    }
                    boolean snapResult = service.snapMemory(item.getChannel() == null ? 1 : item.getChannel(), file);
                    if (!snapResult) {
                        continue;
                    }
                    // 保存到数据库中
                    Img img = new Img();
                    img.setId(SnowFlakeUtil.getDefaultSnowFlakeId());
                    img.setSeqNo(seqNo);
                    img.setImgUrl(packagePath.replace(this.fileDirPath, ""));
                    img.setImgType(ImgTypeEnum.TOP_PLATE.getValue());
                    img.setCameraName(item.getName());
                    img.setCameraCode(item.getCode());
                    imgRepository.insert(img);

                    //当车道带有集卡车道特定的关键字时，车顶号拍摄相机的图片才进行车顶号识别
                    if(!ifCheckTopPlateOnlyVehicleLane || (!StringUtils.isEmpty(lane.getLane()) && SafeWorkTypeEnum.CAR_CAR.getLockLane().equals(LaneUtils.normalizeLane(lane.getLane())))){
                        // 检测
                        DetectResults detectResults = topPlateAlgorithmService.detPicture(filePath);
//                        log.info("识别任务:{},摄像头:{}获取车顶号算法检测结果：{}，图片路径为：{}", seqNo, item.getName(), JsonUtil.toJson(detectResults), filePath);

                        if (detectResults == null || detectResults.det_num == 0) {
                            log.info("识别任务:{},摄像头:{}未获取到车顶号算法检测结果", seqNo, item.getName());
                            continue;
                        }

                        // 识别车顶号
                        RecTopPlate recTopPlate = topPlateAlgorithmService.recPicture(filePath, detectResults);
                        if (recTopPlate != null && !StringUtils.isEmpty(recTopPlate.getTopPlate())) {
                            log.info("识别任务:{},摄像头:{}获取到车顶号算法检测结果{},可信度{}", seqNo, item.getName(), recTopPlate, recTopPlate.getTrust());
                            topPlateList.add(recTopPlate);

                            // 移除单个摄像机的车顶号实时推送逻辑，改为在最终结果确定后统一推送
                        } else {
                            log.info("识别结果recTopPlate为空：{}", recTopPlate);
                        }
                    }else {
                        log.info("已开启仅在处于拖车车道时进行车顶号识别，且当前所处车道不是拖车车道，不进行车顶号识别！");
                    }

                } catch (Throwable ex) {
                    log.error("{}车顶号抓拍识别异常:{}", seqNo, ExceptionUtil.getStackTrace(ex));
                }
            }

            //在这里处理车顶号集合
            if ("unLock".equals(lockType) && !CollectionUtils.isEmpty(topPlateList)){
                log.info("最终的车顶号集合为：{}",topPlateList);
                RecTopPlate bestTopPlate = topPlateList.stream().sorted(Comparator.comparing(RecTopPlate::getTrust).reversed()).collect(Collectors.toList()).get(0);
                topPlateList.clear();
                log.info("可信度最高的车顶号为：{}",bestTopPlate);
                recognitionRepository.updateTopPlate(bestTopPlate.getTopPlate(),seqNo);

                // 车顶号最终结果确定后，推送到ECS系统（只推送一次）
                if(TrueFalseEnum.YES.getDescExtOne().equals(ecsSyncEnabled)) {
                    Record currentRecord = recognitionRepository.queryBySeqNo(seqNo);
                    if(currentRecord != null) {
                        sendToEcs(currentRecord);
                        log.info("车顶号识别任务:{},已推送最终结果到ECS系统 - 车顶号: {}", seqNo, bestTopPlate.getTopPlate());
                    }
                }

                // websocket发送前台
                TopPlateMessageVO topPlateMessageVO = new TopPlateMessageVO();
                topPlateMessageVO.setTopPlateA(bestTopPlate.getTopPlate());
                MessageDTO messageDTO = new MessageDTO();
                messageDTO.setTime(DateUtil.getDate(DateUtil.TYPE_THREE));
                messageDTO.setObject(topPlateMessageVO);
                messageDTO.setType(WebMessageTypeEnum.TOP_PLATE.getValue());
                webSocketUtil.sendMessageToWeb(messageDTO);
            }
            if ("unLock".equals(lockType)) ifDealTopPlate.put(seqNo,true);

        });
    }

    /**
     * 设置识别相机预置位
     */
    private void setPosition(List<RecognizeConfigVO> recognizeConfigList,String seqNo) {
        if (CollectionUtils.isEmpty(recognizeConfigList)) {
            log.info("跳转预置位相机列表为空");
            return;
        }
        // 处理抓拍相机
        recognizeConfigList.forEach(item -> {
            executors.submit(() -> {
                try {
                    log.info("{},{}开始跳转识别预置位", seqNo, item.getIp());
                    // 相机注册
                    HikvisionService service = hikvisionServiceFactory.getService(item.getIp());
                    boolean registerResult = service.register(item.getIp(), item.getControlPort() == null ? 8000 : item.getControlPort(), item.getUsername(), item.getPassword());
                    if (!registerResult) {
                        return;
                    }
                    // 跳转预置位
                    if (item.getPresetLocation() != null && item.getPresetLocation() != 0) {
                        boolean setPositionResult = service.setPosition(item.getChannel() == null ? 1 : item.getChannel(), 39, item.getPresetLocation());
                    } else {
                        log.info("相机:{}未设置预置位", item.getName());
                    }
                } catch (Throwable ex) {
                    log.error("{}相机跳转预置位流程处理异常:{}", seqNo, ExceptionUtil.getStackTrace(ex));
                }
            });
        });
    }

    /**
     * 上锁抓拍
     */
    public void startSnapFlowLock(Long laneId,String seqNo) {
        //车道上锁配置
        List<RecognizeConfigVO> rcList = rcMap.get(RecognizeConfigTypeEnum.LOCK.getValue() + "_" + laneId);
        if (CollectionUtils.isEmpty(rcList)) {
            log.info("{}未获取到车道上锁（上锁抓拍）配置信息,无法启动视频流检测及抓拍,请检查数据库是否有车道配置数据", seqNo);
            return;
        }
        log.info("{}上锁抓拍", seqNo);

        // 处理抓拍相机
        rcList.forEach(item -> {
            executors.submit(() -> {
                try {
                    log.info("识别任务{}中, 相机{},开始处理抓拍逻辑", seqNo, item.getName());
                    // 相机注册
                    HikvisionService service = hikvisionServiceFactory.getService(item.getIp());
                    boolean registerResult = service.register(item.getIp(), item.getControlPort() == null ? 8000 : item.getControlPort(), item.getUsername(), item.getPassword());
                    if (!registerResult) {
                        isProcessing = false;
                        return;
                    }
                    // 跳转预置位
                    if (item.getPresetLocation() != null && item.getPresetLocation() != 0) {
                        boolean setPositionResult = service.setPosition(item.getChannel() == null ? 1 : item.getChannel(), 39, item.getPresetLocation());
                    } else {
                        log.info("相机:{}未设置预置位", item.getName());
                    }                    // 拍图留存
                    // 抓拍图片
                    String packagePath = pathConfigUtil.getRmgName() + "/" + DateUtil.getDate(DateUtil.TYPE_EIGHT) + "/" + seqNo + "/" + seqNo + "_" + item.getCode() + "lock.jpg";

                    String filePath = this.fileDirPath + packagePath;
                    File file = new File(filePath);
                    if (!file.getParentFile().exists()) {
                        //创建上级目录
                        file.getParentFile().mkdirs();
                    }
                    boolean snapResult = service.snapMemory(item.getChannel() == null ? 1 : item.getChannel(), file);
                    if (!snapResult) {
                        return;
                    }
                    // 保存到数据库中
                    Img img = new Img();
                    img.setId(SnowFlakeUtil.getDefaultSnowFlakeId());
                    img.setSeqNo(seqNo);
                    img.setImgUrl(packagePath.replace(this.fileDirPath, ""));
                    img.setImgType(ImgTypeEnum.LOCK.getValue());
                    img.setCameraName(item.getName());
                    img.setCameraCode(item.getCode());
                    imgRepository.insert(img);

                    //根据配置开关，决定是否是否使用这张照片（全景图片）进行车顶号识别
                    if (ifCheckTopPlateByPanorama){
                        checkTopPlate(filePath,seqNo,item.getName());
                    }

                    // 生成缩略图  _compress.jpg
                    String imgTbUrl = img.getImgUrl().replace(CommonConstant.FILE_JPG, CommonConstant.FILE_SLT_JGP);
                    File fileTb = new File(fileDirPath + imgTbUrl);
                    File fileBig = new File(fileDirPath + img.getImgUrl());
                    ThumbnailUtil.compress(fileBig, fileTb, pathConfigUtil.getSize());
                } catch (Throwable ex) {
                    log.error("相机抓拍流程处理异常:{}", ExceptionUtil.getStackTrace(ex));
                }

            });
        });
    }

    /**
     * 对全景相机照片进行车顶号识别方法
     *
     * @title: checkTopPlate
     * @author: cb
     * @date: 2024-09-05 17:11
     */
    private void checkTopPlate(String filePath,String seqNo,String name){
        // 检测
        DetectResults detectResults = topPlateAlgorithmService.detPicture(filePath);
        log.info("识别任务:{},摄像头:{}获取全景相机照片的车顶号算法检测结果：{}", seqNo, name, JsonUtil.toJson(detectResults));

        if (detectResults == null || detectResults.det_num == 0) {
            log.info("识别任务:{},摄像头:{}未获取到车顶号算法检测结果", seqNo, name);
        }

        // 识别
        RecTopPlate recTopPlate = topPlateAlgorithmService.recPicture(filePath, detectResults);
        if (recTopPlate != null && StringUtils.isEmpty(recTopPlate.getTopPlate())) {
            topPlateList.add(recTopPlate);
        }
    }

    /**
     * 解锁抓拍
     */
    public void startSnapFlowUnlock(Long laneId,String seqNo) {

        List<RecognizeConfigVO> recognizeConfigList = rcMap.get(RecognizeConfigTypeEnum.UNLOCK.getValue() + "_" + laneId);
        if (!CollectionUtils.isEmpty(recognizeConfigList)) {
            //解锁抓拍
            log.info("{}解锁抓拍", seqNo);
        } else {
            log.info("未获取到任何车道配置信息,无法启动全景抓拍,请检查数据库是否有车道配置数据");
        }

        if (CollectionUtils.isEmpty(recognizeConfigList)) {
            log.info("抓拍相机列表为空");
            return;
        }

        // 处理抓拍相机
        recognizeConfigList.forEach(item -> {
            executors.submit(() -> {
                try {
                    log.info("识别任务{}中, 相机{},开始处理抓拍逻辑", seqNo, item.getIp());
                    // 相机注册
                    HikvisionService service = hikvisionServiceFactory.getService(item.getIp());
                    boolean registerResult = service.register(item.getIp(), item.getControlPort() == null ? 8000 : item.getControlPort(), item.getUsername(), item.getPassword());
                    if (!registerResult) {
                        return;
                    }
                    // 跳转预置位
                    if (item.getPresetLocation() != null && item.getPresetLocation() != 0) {
                        boolean setPositionResult = service.setPosition(item.getChannel() == null ? 1 : item.getChannel(), 39, item.getPresetLocation());
                    } else {
                        log.info("相机:{}未设置预置位", item.getName());
                    }                    // 拍图留存
                    // 抓拍图片
                    String packagePath = pathConfigUtil.getRmgName() + "/" + DateUtil.getDate(DateUtil.TYPE_EIGHT) + "/" + seqNo + "/" + seqNo + "_" + item.getCode() + "unlock.jpg";

                    String filePath = this.fileDirPath + packagePath;
                    File file = new File(filePath);
                    if (!file.getParentFile().exists()) {
                        //创建上级目录
                        file.getParentFile().mkdirs();
                    }
                    boolean snapResult = service.snapMemory(item.getChannel() == null ? 1 : item.getChannel(), file);
                    if (!snapResult) {
                        return;
                    }
                    // 保存到数据库中
                    Img img = new Img();
                    img.setId(SnowFlakeUtil.getDefaultSnowFlakeId());
                    img.setSeqNo(seqNo);
                    img.setImgUrl(packagePath.replace(this.fileDirPath, ""));
                    img.setImgType(ImgTypeEnum.UNLOCK.getValue());
                    img.setCameraName(item.getName());
                    img.setCameraCode(item.getCode());
                    imgRepository.insert(img);

                    //根据配置开关，决定是否是否使用这张照片（全景图片）进行车顶号识别
                    if (ifCheckTopPlateByPanorama){
                        checkTopPlate(filePath,seqNo,item.getName());
                    }

                    // 生成缩略图  _compress.jpg
                    String imgTbUrl = img.getImgUrl().replace(CommonConstant.FILE_JPG, CommonConstant.FILE_SLT_JGP);
                    File fileTb = new File(fileDirPath + imgTbUrl);
                    File fileBig = new File(fileDirPath + img.getImgUrl());
                    ThumbnailUtil.compress(fileBig, fileTb, pathConfigUtil.getSize());
                } catch (Throwable ex) {
                    log.error("相机抓拍流程处理异常:{}", ExceptionUtil.getStackTrace(ex));
                }

            });
        });
    }

    /**
     * 开启集装箱识别
     */
    public synchronized void startDetectFlow(Integer containerType,Integer workType,String seqNo, Lane lane) {
        Integer type = ContainerTypeEnum.getRecognizeTypeByValue(containerType);
        List<RecognizeConfigVO> rcList = rcMap.get(type + "_" + lane.getId());
        if (CollectionUtils.isEmpty(rcList)) {
            log.info("{}未获取到任何车道识别配置信息,无法开启识别检测,请检查数据库是否有车道配置数据", seqNo);
            return;
        }
        isProcessing = true;

        // 识别的每个摄像头开启一个线程
        Set<Future<RecContainerResult>> futureSet = new HashSet<>();
        rcList.forEach(item -> {
            Future<RecContainerResult> future = executors.submit(() -> {
                try {
                    String traceId = UUID.randomUUID().toString();
                    MDC.put(MdcConstant.traceId, traceId);
                    log.info("识别任务:{},摄像头:{}视频流识别已开启", seqNo, item.getName());
                    // 摄像头持续抓拍识别
                    // 注册摄像头
                    HikvisionService service = hikvisionServiceFactory.getService(item.getIp());
                    boolean registerResult = service.register(item.getIp(), item.getControlPort() == null ? 8000 : item.getControlPort(), item.getUsername(), item.getPassword());
                    if (!registerResult) {
                        log.info("相机{}注册失败,视频流识别关闭", item.getName());
                        return null;
                    }
                    log.debug("识别任务:{},摄像头:{}算法初始化", seqNo, item.getName());
                    // 开始时间
                    long startTime = System.currentTimeMillis();
                    // 最好的结果集
                    RecContainer bestResult = null;
                    Boolean bestIfTrusty = true;
                    // 最好的一张图片
                    String imgUrl = null;
                    // 所有的图片
                    List<String> imagePathList = new ArrayList<>();
                    // 识别次数,用来给图片命名
                    long index = 0;
                    // 识别耗时
                    Long recognizeTimeConsuming = 0L;
                    // 抓拍耗时
                    Long snapTimeConsuming = 0L;
                    // 完整的ISO
                    String bestIso = null;

                    // 设置roi
                    int left = 0;
                    int right = 1920;
                    int top = 0;
                    int bottom = 1080;
                    String roiRect = item.getRoiRect();
                    if (!StringUtils.isEmpty(roiRect)) {
                        String[] split = roiRect.split(",");
                        if (split.length == 4) {
                            left = Integer.parseInt(split[0]);
                            right = Integer.parseInt(split[1]);
                            top = Integer.parseInt(split[2]);
                            bottom = Integer.parseInt(split[3]);
                        }
                    }
                    log.debug("{}ROI区域：{}", seqNo, roiRect);
                    Rectangle rectangleRoi = new Rectangle(left, top, right, top, right, bottom, left, bottom);
// ---------------------------------------------------------------循环视频流识别开始线---------------------------------------------------------------------------------------
                    while (isLock && reachRecgHigh) {
                        log.debug("识别任务:{},摄像头:{}循环抓拍中", seqNo, item.getName());
                        // 收到解锁信号,或者超时后停止
                        // 不设超时时间
                        if (System.currentTimeMillis() - startTime > 300 * 1000) {
                            break;
                        }
                        index++;

                        // 抓拍图片
                        String packagePath = pathConfigUtil.getRmgName() + "/" + DateUtil.getDate(DateUtil.TYPE_EIGHT) + "/" + seqNo + "/" + seqNo + "_" + item.getCode() + index + ".jpg";

                        //本次循环拍的照片
                        String filePath = this.fileDirPath + packagePath;
                        imagePathList.add(filePath);
                        File file = new File(filePath);
                        if (!file.getParentFile().exists()) {
                            //创建上级目录
                            file.getParentFile().mkdirs();
                        }

                        // 抓拍延时 200ms
                        Thread.sleep(200);

                        long snapStart = System.currentTimeMillis();
                        boolean snapResult = service.snapMemory(item.getChannel() == null ? 1 : item.getChannel(), file);
                        long snapEnd = System.currentTimeMillis();
                        //计算抓拍耗时
                        if (snapTimeConsuming == 0L) {
                            snapTimeConsuming = snapEnd - snapStart;
                        } else {
                            snapTimeConsuming = ((snapEnd - snapStart) + snapTimeConsuming) / 2;
                        }
                        log.debug("识别任务:{},摄像头:{}抓拍图片结果：{}", seqNo, item.getName(), snapResult);
                        //未抓拍到图片 再次进行抓拍
                        if (!snapResult) {
                            log.info("识别任务:{},摄像头:{}未抓拍到图片 再次进行抓拍", seqNo, item.getName());
                            continue;
                        }
                        // 第一张图片先存下来
                        if (imgUrl == null) {
                            imgUrl = filePath;
                        }
                        long recStart = System.currentTimeMillis();

                        // 调算法检测接口
                        DetectResults detectResults = containerAlgorithmService.detPicture(filePath);
                        log.debug("识别任务:{},摄像头:{}获取算法检测结果：{}", seqNo, item.getName(), JsonUtil.toJson(detectResults));

                        if (detectResults == null || detectResults.det_num == 0) {
//                            log.info("识别任务:{},摄像头:{}未获取到算法检测结果{}", seqNo, item.getName(),filePath);
                            continue;
                        }

                        RectDet spreader = null;
                        for (DetectResult detectResult : detectResults.getDr()) {
                            if (detectResult.getDet_type() == 11) {
                                spreader = detectResult.getDet_rect();
                                log.debug("{}检测到吊具,吊具位置为:{}", seqNo, JsonUtil.toJson(spreader));
                                break;
                            }
                        }
                        List<DetectResult> detectResultList = new ArrayList<>();
                        for (DetectResult detectResult : detectResults.getDr()) {
                            //可信度小于0的过滤掉
                            if (detectResult.getDet_fr() <= 0) {
//                                log.info("{},摄像头:{},检测结果{}可信度：{}，小于等于0，重新抓拍识别{}", seqNo, item.getName(),detectResult.getDet_rect(),detectResult.getDet_fr(),filePath);
                                continue;
                            }
                            // 0-3是指箱号  非箱号过滤掉
                            if (detectResult.getDet_type() != 0 && detectResult.getDet_type() != 1 && detectResult.getDet_type() != 2 && detectResult.getDet_type() != 3) {
//                                log.info("{}det_type不是0-3,不是箱号类型的，检测框：{}，det_type:{},重新抓拍识别{}", seqNo,detectResult.getDet_rect(),detectResult.getDet_type(),filePath);
                                continue;
                            }
                            // 这里还要判断下箱号检测区域是否在吊具下方
                            if (spreader != null && detectResult.getDet_rect() != null) {
                                int spreaderBottom = spreader.getBottom();
                                int spreaderRight = spreader.getRight();
                                int spreaderLeft = spreader.getLeft();
                                int containerTop = detectResult.getDet_rect().getTop();
                                int containerBottom = detectResult.getDet_rect().getBottom();
                                int containerRight = detectResult.getDet_rect().getRight();
                                int containerLeft = detectResult.getDet_rect().getLeft();
                                if ((spreaderBottom != 0 && spreaderBottom > containerBottom)
                                        || (spreaderRight != 0 && spreaderRight < containerLeft)
                                        || (spreaderLeft > 0 && spreaderLeft > containerRight)

                                ) {
                                    log.info("{}检测结果:{},不处于吊具:{}下方,过滤掉{}", seqNo,detectResult.getDet_rect(),spreader,filePath);
                                    continue;
                                }else {
//                                    log.info("{}检测结果:{},处于吊具:{}下方,继续流程{}", seqNo,detectResult.getDet_rect(),spreader,filePath);
                                }
                                //有吊具时,要求箱号检测区域在吊具下方500的范围内.
                                if (containerBottom > (spreaderBottom + 500)){
                                    log.info("{}箱号检测区域{}不在下方500的范围:{}内,过滤掉{}", seqNo,containerBottom,spreaderBottom + 500,filePath);
                                    continue;
                                }
                            }else {
//                                log.info("{}检测结果:{},没有吊具，继续流程{}", seqNo,detectResult.getDet_rect(),filePath);

                            }

                            detectResultList.add(detectResult);
                        }
                        // 过滤后如果没有合适的检测结果,那么就直接跳过
                        if (CollectionUtils.isEmpty(detectResultList)) {
                            log.info("{},过滤后如果没有合适的检测结果,过滤掉{}", seqNo,filePath);
                            continue;
                        }

                        detectResults.setDet_num(detectResultList.size());
                        detectResults.setDr(detectResultList.toArray(new DetectResult[]{}));

                        // 调算法识别接口
                        List<RecContainer> containerList = containerAlgorithmService.recPicture(filePath, detectResults);
                        log.debug("识别任务:{},摄像头:{}调算法识别接口：{}", seqNo, item.getIp(), JsonUtil.toJson(containerList));
                        long recEnd = System.currentTimeMillis();
                        if (recognizeTimeConsuming == 0L) {
                            recognizeTimeConsuming = recEnd - recStart;
                        } else {
                            recognizeTimeConsuming = ((recEnd - recStart) + recognizeTimeConsuming) / 2;
                        }

                        // 如果没结果,就删掉图片,作废数据
                        if (CollectionUtils.isEmpty(containerList)) {
//                            log.info("识别任务:{},摄像头:{}算法识别没结果,过滤掉{}", seqNo, item.getName(),filePath);
                            continue;
                        }

                        // 移除单个摄像机的实时推送逻辑，改为在数据整合完成后统一推送
                        //目前为止，数据只有两种情况：1、识别到吊具且箱号区域在吊具下。2、没识别到吊具。都是有效数据。
                        // 第一个有结果的数据先存下来（多条识别数据的情况下有限保存箱号可信度高的数据）
                        RecContainer recContainer = containerList.get(0);
                        if (containerList.size()>=2){
                            if (spreader!=null){
                                //有吊具时,取吊具下最近的通过校验的那个 by chenbo
                                for (int i = 1; i < containerList.size(); i++) {
                                    if (containerList.get(i).getDetRect().getBottom() < recContainer.getDetRect().getBottom() && containerList.get(i).getBcheck()){
                                        recContainer = containerList.get(i);

                                    }
                                }
                            }else {
                                //没有吊具时,取可信度最高且通过了校验的一个
                                for (int i = 1; i < containerList.size(); i++) {
                                    if (containerList.get(i).getIdConf() > recContainer.getIdConf() && containerList.get(i).getBcheck()){
                                        recContainer = containerList.get(i);
                                    }
                                }
                            }
                            log.debug("识别任务:{},相机:{},检测到多个结果,取吊具下最近的通过校验箱号或没有吊具可信度最高的一个-->箱号:{},ISO:{},箱号可信度:{}",seqNo, item.getName(), recContainer.getCtnNo(), recContainer.getIso(), recContainer.getIdConf());
                        }

                        //吊具不为空，则取吊具下的结果，因此可确定箱号在吊具下 by chenbo
                        if (spreader!=null){
                            recContainer.setIsUnderSpreader(true);
                        }else {
                            recContainer.setIsUnderSpreader(false);
                        }

                        // 判断iso是否完整,如果当前结果的iso完整,则优先取当前结果的,如果当前结果的不完整,则取缓存的完整数据
                        if (StringUtils.isEmpty(recContainer.getIso()) || recContainer.getIso().length() != ISO_LENGTH) {
                            if (bestIso != null && bestIso.length() == ISO_LENGTH) {
                                // 这里做个iso修正的操作
                                recContainer.setIso(bestIso);
                                recContainer.setIsIsoCheck(true);
                            }
                        } else {
                            if (bestIso == null || bestIso.length() != ISO_LENGTH) {
                                bestIso = recContainer.getIso();
                            }
                        }

                        // 缓存为空，则默认保存当前的
                        if (bestResult == null) {
                            log.info("识别任务:{},相机:{},当前最好结果-->箱号:{},ISO:{},箱号可信度:{}", seqNo, item.getName(), recContainer.getCtnNo(), recContainer.getIso(), recContainer.getIdConf());
                            bestResult = recContainer;
                            imgUrl = filePath;
                            continue;
                        }

                        // 校验箱号是否通过校验  算法已经校验过了,这里直接用 by chenbo
                        // 当前的箱号没有通过校验，直接丢掉
                        if (!recContainer.getBcheck()) {
                            continue;
                        }
                        // 如果缓存的结果没有通过标准位校验，那么直接用通过标准位校验的替换
                        if (!bestResult.getBcheck()) {
                            log.info("识别任务:{},相机:{},当前最好结果-->箱号:{},ISO:{},箱号可信度:{}",seqNo, item.getName(), recContainer.getCtnNo(), recContainer.getIso(), recContainer.getIdConf());
                            bestResult = recContainer;
                            imgUrl = filePath;
                            continue;
                        }
                        // 如果缓存的数据处于吊具下方,当前结果不处于吊具下方,则优先取吊具下方的结果
                        if (Boolean.TRUE.equals(bestResult.getIsUnderSpreader()) && !Boolean.TRUE.equals(recContainer.getIsUnderSpreader())) {
                            continue;
                        }


                        //箱号不相同,看缓存是否可靠,可靠则继续,不可靠且当前的在吊具下且通过了校验,则重新缓存,可靠性默认为true,并continue
                        if (bestResult.getCtnNo()!=null && recContainer.getCtnNo() != null
                                && !bestResult.getCtnNo().equals(recContainer.getCtnNo()) && !bestIfTrusty && recContainer.getIsUnderSpreader() && recContainer.getBcheck()){
                            log.info("相机{},缓存的数据不可靠,重新缓存,旧:{},新:{}",item.getName(),bestResult,recContainer);
                            bestResult = recContainer;
                            imgUrl = filePath;
                            bestIfTrusty = true;
                            continue;
                        }

                        //当前箱号等于缓存的箱号且位置不同时,认为缓存可靠,continue
                        if (bestResult.getCtnNo()!=null && recContainer.getCtnNo() != null
                                && bestResult.getCtnNo().equals(recContainer.getCtnNo())
                                && ((Math.abs(bestResult.getDetRect().getBottom()-recContainer.getDetRect().getBottom()) > backBoxLocationChange)
                                ||(Math.abs(bestResult.getDetRect().getRight()-recContainer.getDetRect().getRight()) > backBoxLocationChange))
                        ){
//                            log.info("箱号相同,位置不同,bestResult:{},recContainer:{}",bestResult.getDetRect(),recContainer.getDetRect());
                            if (spreader!=null || recContainer.getIdConf() > bestResult.getIdConf()){
//                                log.info("识别任务:{},相机:{},当前最好结果-->箱号:{},ISO:{},箱号可信度:{}",seqNo, item.getName(), recContainer.getCtnNo(), recContainer.getIso(), recContainer.getIdConf());
                                bestResult = recContainer;
                                imgUrl = filePath;
                            }
                            bestIfTrusty = true;
                            continue;
                        }

                        //箱号相同,位置也相同(变动小于临界值),说明不可靠,打上标记,并在下一个吊具下通过了校验的不同箱号来时直接替换
                        if (bestResult.getCtnNo()!=null && recContainer.getCtnNo() != null
                                && bestResult.getCtnNo().equals(recContainer.getCtnNo())
                                && ((Math.abs(bestResult.getDetRect().getBottom()-recContainer.getDetRect().getBottom()) <= backBoxLocationChange)
                                ||(Math.abs(bestResult.getDetRect().getRight()-recContainer.getDetRect().getRight()) <= backBoxLocationChange))
                        ){
//                            log.info("箱号相同,位置也相同,bestResult:{},recContainer:{}",bestResult.getDetRect(),recContainer.getDetRect());
                            if (spreader!=null || recContainer.getIdConf() > bestResult.getIdConf()){
//                                log.info("识别任务:{},相机:{},当前最好结果-->箱号:{},ISO:{},箱号可信度:{}",seqNo, item.getName(), recContainer.getCtnNo(), recContainer.getIso(), recContainer.getIdConf());
                                bestResult = recContainer;
                                imgUrl = filePath;
                            }
                            bestIfTrusty = false;
                            continue;
                        }

                        //如果缓存的数据为默认保存的数据且可信度小于当前，那么直接通过标准校验的替换
                        if (bestResult.getIdConf() <= recContainer.getIdConf()) {
                            log.info("识别任务:{},相机:{},当前最好结果-->箱号:{},ISO:{},箱号可信度:{}",seqNo, item.getName(), recContainer.getCtnNo(), recContainer.getIso(), recContainer.getIdConf());
                            bestResult = recContainer;
                            imgUrl = filePath;
                        }

                    }
// ---------------------------------------------------------------循环视频流识别结束线---------------------------------------------------------------------------------------
                    // 3. 把数据放到数据池中
                    // 这里再处理下iso为空的问题
                    if (bestResult != null && (StringUtils.isEmpty(bestResult.getIso()) || bestResult.getIso().length() != ISO_LENGTH)) {
                        if (bestIso != null && bestIso.length() == ISO_LENGTH) {
                            // 这里做个iso修正的操作
                            bestResult.setIso(bestIso);
                            bestResult.setIsIsoCheck(true);
                        }
                    }

                    log.info("识别任务:{},摄像头:{},视频流识别已结束,最好的识别结果为---箱号：{},iso:{},图片:{}", seqNo, item.getName(), (bestResult == null ? null : bestResult.getCtnNo()), (bestResult == null ? null : bestResult.getIso()), imgUrl);
                    RecContainerResult recContainerResult = new RecContainerResult();
                    List<RecContainer> recContainerList = new ArrayList<>();
                    recContainerList.add(bestResult);
                    recContainerResult.setRecContainerList(recContainerList);

                    // 删除掉除了最好的一张以外的其他图片
                    String finalImgUrl = imgUrl;
                    imagePathList = imagePathList.stream().filter(image -> !image.equals(finalImgUrl)).collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(imagePathList)) {
                        for (String path : imagePathList) {
                            File file = new File(path);
                            if (file.exists()) {
                                file.delete();
                            }
                        }
                    }

                    //识别图片
                    List<Img> imgList = new ArrayList<>();
                    Img img = new Img();
                    if (imgUrl != null) {
                        img.setImgUrl(imgUrl.replace(this.fileDirPath, ""));
                    }

                    img.setSeqNo(seqNo);
                    img.setCameraName(item.getName());
                    img.setCameraCode(item.getCode());
                    if (bestResult != null && bestResult.getDetRect() != null) {
                        RectDet detRect = bestResult.getDetRect();
                        // 图片识别范围组装到一个字段里面
                        img.setDectRect(detRect.getLeft() + "," + detRect.getRight() + "," + detRect.getTop() + "," + detRect.getBottom());
                    }
                    img.setId(SnowFlakeUtil.getDefaultSnowFlakeId());
                    img.setImgType(ImgTypeEnum.CONTAINER.getValue());
                    imgList.add(img);
                    //箱号图片
                    recContainerResult.setImgList(imgList);
                    //抓拍耗时
                    recContainerResult.setSnapTimeConsuming(snapTimeConsuming);
                    //识别耗时
                    recContainerResult.setRecognizeTimeConsuming(snapTimeConsuming);
                    return recContainerResult;

                } catch (Throwable exception) {
                    log.info("摄像头:{}识别线程处理异常:{}", item.getIp(), ExceptionUtil.getStackTrace(exception));
                }
                log.info("摄像头:{}视频流识别已关闭", item.getIp());
                isProcessing = false;
                return null;
            });
            futureSet.add(future);
        });

        //给5秒时间用于等待相机的线程放future到set里,并整合所有数据
        long dealAllCameraDataStart = System.currentTimeMillis();
        while ((System.currentTimeMillis() - dealAllCameraDataStart) < 5 * 1000){
            if (rcList.size()==futureSet.size()){
                // 处理识别结果
                executors.submit(() -> {
                    try {
                        // 将所有摄像头的识别数据先整合再一起,然后再结合PLC的箱型进行数据清理
                        RecContainerResult totalResult = null;
                        for (Future<RecContainerResult> future : futureSet) {
                            try {
                                // 设置初始数据
                                RecContainerResult rcr = future.get();
                                if (totalResult == null) {
                                    totalResult = rcr;
                                    continue;
                                }

                                if (rcr == null){
                                    continue;
                                }

                                // 整合识别数据
                                List<RecContainer> recContainerList = totalResult.getRecContainerList();
                                if (recContainerList == null) {
                                    recContainerList = new ArrayList<>();
                                }
                                if (rcr.getRecContainerList() != null) {
                                    recContainerList.addAll(rcr.getRecContainerList());
                                }

                                // 整合图片数据
                                List<Img> imgList = totalResult.getImgList();
                                if (imgList == null) {
                                    imgList = new ArrayList<>();
                                }
                                if (rcr.getImgList() != null) {
                                    imgList.addAll(rcr.getImgList());
                                }

                                // 整合识别耗时数据
                                Long recognizeTimeConsuming = totalResult.getRecognizeTimeConsuming() == null ? 0L : totalResult.getRecognizeTimeConsuming();
                                Long recognizeTimeConsumingAdd = rcr.getRecognizeTimeConsuming() == null ? 0L : rcr.getRecognizeTimeConsuming();
                                totalResult.setRecognizeTimeConsuming((recognizeTimeConsuming + recognizeTimeConsumingAdd) / 2);

                            } catch (Throwable exception) {
                                log.error("获取识别结果异常:{}", ExceptionUtil.getStackTrace(exception));
                            }
                        }
                        log.info("识别任务:{},整合后的识别结果:{}", seqNo, JsonUtil.toJson(totalResult));

                        // 防止一个结果都没有
                        totalResult = totalResult == null ? new RecContainerResult() : totalResult;
                        // 设置seqNo
                        totalResult.setSeqNo(seqNo);

                        // 数据清洗
                        // 1.如果没有识别数据,那么根据箱尺寸封装空数据
                        List<RecContainer> recContainerList = new ArrayList<>();
                        if (CollectionUtils.isEmpty(totalResult.getRecContainerList())) {

                            if (ContainerTypeEnum.SL.getValue().equals(containerType) || ContainerTypeEnum.SS.getValue().equals(containerType)) {
                                RecContainer recContainer = new RecContainer();
                                recContainer.setCtnNo("");
                                recContainerList.add(recContainer);

                            }
                            if (ContainerTypeEnum.TD.getValue().equals(containerType)) {
                                RecContainer recContainer = new RecContainer();
                                recContainer.setCtnNo("");
                                recContainerList.add(recContainer);
                                recContainer = new RecContainer();
                                recContainer.setCtnNo("");
                                recContainerList.add(recContainer);

                            }
                            totalResult.setRecContainerList(recContainerList);
                        } else {
                            // 2.如果有识别到数据,那么对比识别结果,去掉重复的数据,也过滤掉未识别到的空数据
                            for (RecContainer rcr : totalResult.getRecContainerList()) {
                                if (rcr == null) {
                                    continue;
                                }
                                if (StringUtils.isEmpty(rcr.getCtnNo())) {
                                    continue;
                                }

                                // 要根据箱号校验结果和位置决定取哪个数据
                                if (!ContainerNumberUtil.verifyCntrCode(rcr.getCtnNo())) {
                                    continue;
                                }
                                recContainerList.add(rcr);
                            }
                            // 根据吊具类型补全下数据
                            if (ContainerTypeEnum.SL.getValue().equals(containerType) || ContainerTypeEnum.SS.getValue().equals(containerType)) {
                                //数据补充
                                if (CollectionUtils.isEmpty(recContainerList)) {
                                    RecContainer recContainer = new RecContainer();
                                    recContainer.setCtnNo("");
                                    recContainerList.add(recContainer);
                                } else {
                                    //优先取吊具下通过校验的数据,其次用全部数据,从里面筛选重复最多且可信度最高的那条
                                    RecContainer currentRecContainer = null;
                                    //筛选出吊具下的,通过了校验的,按可信度降序排列得到吊具List
                                    List<RecContainer> checkedAndUnderSpreaderList = recContainerList.stream()
                                            .filter(container ->
                                                    !StringUtils.isEmpty(container.getCtnNo())
                                                            && container.getBcheck()
                                                            && container.getIsUnderSpreader())
                                            .collect(Collectors.toList());
                                    //先检查吊具list
                                    if (!CollectionUtils.isEmpty(checkedAndUnderSpreaderList)){
                                        currentRecContainer = getCurrentRecContainer(checkedAndUnderSpreaderList);
                                    }else if (!CollectionUtils.isEmpty(recContainerList)){
                                        //再检查总list
                                        currentRecContainer = getCurrentRecContainer(recContainerList);
                                    }

                                    List<String> isoList = recContainerList.stream().map(RecContainer::getIso).filter(item -> !StringUtils.isEmpty(item) && item.length() == 4).collect(Collectors.toList());
                                    // 这个重置集合很重要
                                    recContainerList = new ArrayList<>();
                                    if (currentRecContainer != null && StringUtils.isEmpty(currentRecContainer.getIso())) {
                                        // 判断下ISO是否为空,如果为空,用其他数据补充
                                        if (!isoList.isEmpty()) {
                                            currentRecContainer.setIso(isoList.get(0));
                                        }
                                    }
                                    recContainerList.add(currentRecContainer == null ? new RecContainer() : currentRecContainer);
                                }
                            } else if (ContainerTypeEnum.TD.getValue().equals(containerType)) {
                                // 要根据不同箱号找出可信度最高的两箱号
                                Map<String, RecContainer> bestMap = recContainerList.stream().collect(
                                        Collectors.groupingBy(RecContainer::getCtnNo,
                                                Collectors.collectingAndThen(Collectors.reducing((c1, c2) -> c1.getIdConf() > c2.getIdConf() ? c1 : c2),
                                                        Optional::get)));
                                recContainerList = new ArrayList<>(bestMap.values());

                                if (CollectionUtils.isEmpty(recContainerList) || recContainerList.size() == 0) {
                                    RecContainer recContainer = new RecContainer();
                                    recContainer.setCtnNo("");
                                    recContainerList.add(recContainer);
                                    recContainer = new RecContainer();
                                    recContainer.setCtnNo("");
                                    recContainerList.add(recContainer);
                                }
                                if (recContainerList.size() == 1) {
                                    RecContainer recContainer = new RecContainer();
                                    recContainer.setCtnNo("");
                                    recContainerList.add(recContainer);
                                }
                            }
                        }
                        log.info("识别任务:{},逻辑处理后最终的识别结果:{}", seqNo, JsonUtil.toJson(recContainerList));


                        // 保存结果到数据库
                        Record record = recognitionRepository.queryBySeqNo(seqNo);
                        if (record == null) {

                            record = new Record();
                            record.setId(SnowFlakeUtil.getDefaultSnowFlakeId());
                            record.setSeqNo(seqNo);
                            record.setContainerType(containerType);
                            record.setWorkType(workType);
                            //3秒内已到达识别高度时，视为正常上锁，在此给定上锁时间
                            record.setLockTime(DateUtils.getTime());
                            recognitionRepository.add(record);
                        }
                        record.setRecognizeTimeConsuming(totalResult.getRecognizeTimeConsuming());
                        record.setSnapTimeConsuming(totalResult.getSnapTimeConsuming());


                        // 添加车牌
                        if (plateThreeResult != null && plateThreeResult.getPlateNumber() != null && System.currentTimeMillis() - plateThreeResult.getTime() < 10 * 60 * 1000) {
                            record.setPlateNumberA(plateThreeResult.getPlateNumber());
                            //添加车牌图片到img表。
                            log.info("添加车牌图片到img表");
                            Img plateNumberImg = new Img();
                            plateNumberImg.setImgUrl(plateThreeResult.getPlatePath());
                            plateNumberImg.setImgType(4);
                            plateNumberImg.setDectRect("plateNumber");
                            plateNumberImg.setId(SnowFlakeUtil.getDefaultSnowFlakeId());
                            plateNumberImg.setSeqNo(seqNo);
                            plateNumberImg.setCreateTime(LocalDateTime.now());
                            imgRepository.insert(plateNumberImg);
                        }

                        //添加作业高度，来自plc中的数据，需添加字段。
                        log.info("添加作业高度，来自seqHeightMap中的数据："+seqHeightMap.get(seqNo));
                        record.setHeight((float) (seqHeightMap.get(seqNo) == null ? 0 : seqHeightMap.get(seqNo)));

                        ContainerMessageVO containerMessageVO = new ContainerMessageVO();

                        // 封装数据
                        if (!CollectionUtils.isEmpty(recContainerList)) {
                            if (recContainerList.size() == 1) {
                                record.setCtnNoA(recContainerList.get(0).getCtnNo());
                                record.setIsoNoA(recContainerList.get(0).getIso());
                                record.setSealA(null != recContainerList.get(0).getIsSeal() ? IsSealNoEnum
                                        .getCodeByBool(recContainerList.get(0).getIsSeal()) : null);
                                record.setSealB(null);
                                record.setSealC(null);
                                record.setSealD(null);
                                record.setCtnCheckA(recContainerList.get(0).getBcheck());
                                record.setCtnCheckB(null);
                                record.setCtnCheckC(null);
                                record.setCtnCheckD(null);
                                containerMessageVO.setFirstCtnNo(recContainerList.get(0).getCtnNo());
                                containerMessageVO.setFirstIso(recContainerList.get(0).getIso());

                            }
                            if (recContainerList.size() == 2) {
                                record.setCtnNoA(recContainerList.get(0).getCtnNo());
                                record.setIsoNoA(recContainerList.get(0).getIso());
                                record.setCtnCheckA(recContainerList.get(0).getBcheck());
                                record.setSealA(null != recContainerList.get(0).getIsSeal() ? IsSealNoEnum.getCodeByBool(recContainerList.get(0).getIsSeal()) : null);
                                containerMessageVO.setFirstCtnNo(recContainerList.get(0).getCtnNo());
                                containerMessageVO.setFirstIso(recContainerList.get(0).getIso());

                                record.setCtnNoB(recContainerList.get(1).getCtnNo());
                                record.setIsoNoB(recContainerList.get(1).getIso());
                                record.setCtnCheckB(recContainerList.get(1).getBcheck());
                                record.setSealB(null != recContainerList.get(1).getIsSeal() ? IsSealNoEnum.getCodeByBool(recContainerList.get(1).getIsSeal()) : null);
                                containerMessageVO.setSecondCtnNo(recContainerList.get(1).getCtnNo());
                                containerMessageVO.setSecondIso(recContainerList.get(1).getIso());

                                record.setSealC(null);
                                record.setSealD(null);

                                record.setCtnCheckC(null);
                                record.setCtnCheckD(null);
                            }
                            record.setRecTime(DateUtils.getTime());
                            record.setCtnNum(recContainerList.size());

                            record.setPlateCheckA(null);
                            record.setPlateCheckB(null);
                        }
                        recognitionRepository.update(record);
                        log.info("识别任务:{},保存到数据库的内容:{}", seqNo, JsonUtil.toJson(record));
                        ifDealCtnNum.put(seqNo,true);


                        // 存储图片信息
                        List<Img> addImgs = new ArrayList<>();
                        List<Img> imgList = totalResult.getImgList();
                        if (!CollectionUtils.isEmpty(imgList) && imgList.size() > 0) {
                            File fileBig;
                            File fileTb;
                            for (Img img : imgList) {
                                if (null == img || StringUtils.isEmpty(img.getImgUrl())) {
                                    continue;
                                }
                                fileBig = new File(fileDirPath + img.getImgUrl());
                                if (!fileBig.exists()) {
                                    continue;
                                }
                                addImgs.add(img);

                                // 生成缩略图  _compress.jpg
                                String imgTbUrl = img.getImgUrl().replace(CommonConstant.FILE_JPG, CommonConstant.FILE_SLT_JGP);
                                fileTb = new File(fileDirPath + imgTbUrl);
                                ThumbnailUtil.compress(fileBig, fileTb, pathConfigUtil.getSize());
                            }
                        }
                        imgRepository.insertBatch(addImgs);


                        // websocket发送前台
                        MessageDTO messageDTO = new MessageDTO();
                        messageDTO.setTime(DateUtil.getDate(DateUtil.TYPE_THREE));
                        messageDTO.setObject(containerMessageVO);
                        messageDTO.setType(WebMessageTypeEnum.CTN.getValue());
                        webSocketUtil.sendMessageToWeb(messageDTO);

                        // 上传记录给平台
                        identifyService.workRecord(record);

                        // 数据整合完成后，推送最终结果到ECS系统（只推送一次）
                        if(TrueFalseEnum.YES.getDescExtOne().equals(ecsSyncEnabled)) {
                            sendToEcs(record);
                            log.info("识别任务:{},已推送最终整合结果到ECS系统", seqNo);
                        }
                    } catch (Throwable ex) {
                        log.error("获取识别结果异常:{}", ExceptionUtil.getStackTrace(ex));
                    }
                });
//                log.info("已达到整合条件,结束循环");
                break;
            }
        }
    }

    /**
     * 根据RecContainer集合,找出里面重复最多且可信度最高的那条记录
     *
     * @title: getCurrentRecContainer
     * @author: cb
     * @date: 2024-08-30 9:26
     */
    public RecContainer getCurrentRecContainer(List<RecContainer> recContainerList){
        Map<String, Long> countMap = recContainerList.stream().collect(Collectors.groupingBy(RecContainer::getCtnNo, Collectors.counting()));
        log.info("每个箱号的重复次数:"+countMap);

        //先找到其中一个重复次数最多的箱号
        Optional<Map.Entry<String, Long>> maxCountOptional = countMap.entrySet().stream()
                .max((e1, e2) -> (int) (e1.getValue() - e2.getValue()));
        log.info("其中一个重复次数最多的箱号:"+maxCountOptional.get());

        ConcurrentSkipListSet<String> removeSet = new ConcurrentSkipListSet<>();

        //找到所有做了统计的箱号作为keySet
        Set<String> keySet = countMap.keySet();
        //得到重复次数最多的那个(或那些:多个箱号重复次数相同,都是最多)箱号
        for (String key : keySet){
            if (countMap.get(key) < maxCountOptional.get().getValue()) removeSet.add(key);
        }
        log.info("重复次数最多的那些箱号:"+keySet);

        return recContainerList.stream()
                //过滤留下满足该条件的箱号：不在"待移除set"中的箱号
                .filter(container -> !removeSet.contains(container.getCtnNo()))
                .sorted(Comparator.comparing(RecContainer::getIdConf).reversed())
                .collect(Collectors.toList()).get(0);
    }

    /**
     * 发送铁科
     *
     * @param seqNo
     * @param lane
     * @param lockTime
     * @param unLockTime
     */
    public void sendTieke(boolean isLock,String seqNo,Lane lane,String lockTime,String unLockTime, Record recordSync) {
    	 log.info("锁状态：isLock 为true 上锁，false 解锁。结果：{}",isLock);
    	 if(!isLock && StringUtils.isEmpty(recordSync.getCtnNoA())) {
    		return;
    	 }

    	 if(isLock && !StringUtils.isEmpty(submitSeqNo) && submitSeqNo.equals(seqNo)) {
    		 return;
    	 }

    	 if(!StringUtils.isEmpty(submitCtnNo) && submitCtnNo.equals(recordSync.getCtnNoA())) {
    		 return;
    	 }
    	 submitSeqNo = seqNo;
    	 submitCtnNo = recordSync.getCtnNoA();
    	 log.info("同步第三方接口seqNo:"+seqNo);
         SendIdentifyReqDTO identifyReqDTO = new SendIdentifyReqDTO();
         identifyReqDTO.setSeqNo(seqNo);
         identifyReqDTO.setLane(recordSync.getLaneNumA());
         identifyReqDTO.setTruckNo(recordSync.getTopPlateA());
         Boolean lockLaneIsCar = SafeWorkTypeEnum.CAR_CAR.getLockLane().equals(LaneUtils.normalizeLane(recordSync.getLaneNumA() == null ? "" : recordSync.getLaneNumA()));
         Boolean unlockLaneIsCar = SafeWorkTypeEnum.CAR_CAR.getLockLane().equals(LaneUtils.normalizeLane(lane.getLane() == null ? "" : lane.getLane()));
         //上锁车道含有关键字或当前车道含有"车道"关键字时，给标识（应当带车顶号）为1
        if (lockLaneIsCar || unlockLaneIsCar){
            identifyReqDTO.setTruckNoFlag("1");
        }else {
            identifyReqDTO.setTruckNoFlag("0");
        }
         identifyReqDTO.setCtnNo(recordSync.getCtnNoA());
         identifyReqDTO.setIso(recordSync.getIsoNoA());
         identifyReqDTO.setPassTime(recordSync.getPassTime());
         identifyReqDTO.setLockTime(StringUtils.isEmpty(lockTime) ? null : DateConversion.stringToLocalDateTime(lockTime));
         identifyReqDTO.setUnLockTime(StringUtils.isEmpty(unLockTime) ? null : DateConversion.stringToLocalDateTime(unLockTime));
         try {
        	 Result<String> result = gkptService.sendIdentify(identifyReqDTO);
             log.info("同步第三方接口出参：{};入参：{}",result, JsonUtil.toJson(identifyReqDTO));
		} catch (Exception e) {
	         log.info("调用第三方接口失败：{}",e.getMessage());
		}
    }

    /**
     * 获取最佳箱号识别结果
     * @param containerList
     * @return
     */
    private RecContainer getBestContainer(List<RecContainer> containerList) {
        if (CollectionUtils.isEmpty(containerList)) {
            return null;
        }
        return containerList.stream()
                .filter(RecContainer::getBcheck)
                .sorted(Comparator.comparing(RecContainer::getIdConf).reversed())
                .collect(Collectors.toList()).get(0);
    }

    /**
     * 实时推送识别结果到ECS系统
     * @param record 识别记录
     */
    private void sendToEcs(Record record) {
        if (record == null) {
            return;
        }

        // 使用线程池异步发送
        executors.submit(() -> {
            try {
                // 构建推送数据
                EcsIdentifyInfoDTO identifyInfo = new EcsIdentifyInfoDTO();

                // 设置门呀号
                identifyInfo.setCraneNo(pathConfigUtil.getRmgName());

                // 设置识别类型
                // 1=拖车，2=集装箱，3=火车车厢
                // 检查是否为火车车道
                boolean isTrainLane = false;
                if (!StringUtils.isEmpty(record.getLaneNumA()) &&
                    "火车".equals(LaneUtils.normalizeLane(record.getLaneNumA()))) {
                    isTrainLane = true;
                }

                // 创建新的对象，只包含必要的字段
                EcsIdentifyInfoDTO cleanIdentifyInfo = new EcsIdentifyInfoDTO();
                // 只保留门呀号和识别类型
                cleanIdentifyInfo.setCraneNo(pathConfigUtil.getRmgName());

                // 获取关联的图片URL
                List<String> imgUrls = getImageUrlsBySeqNo(record.getSeqNo());
                if (imgUrls != null && !imgUrls.isEmpty()) {
                    cleanIdentifyInfo.setImgUrls(imgUrls);
                    log.debug("识别结果图片URL已添加 - seqNo: {}, 图片数量: {}", record.getSeqNo(), imgUrls.size());
                }

                if (isTrainLane && !StringUtils.isEmpty(record.getCtnNoA())) {
                    // 火车车道且有箱号，识别类型为火车车厢
                    cleanIdentifyInfo.setIdentifyType(3);
                    // 只传递火车车厢编号
                    cleanIdentifyInfo.setIdentifyCarriageNo(record.getCtnNoA());

                    log.info("检测到火车车道数据，设置识别类型为火车车厢");
                } else if (!StringUtils.isEmpty(record.getTopPlateA())) {
                    // 有车顶号，识别类型为拖车
                    cleanIdentifyInfo.setIdentifyType(1);
                    // 只传递拖车号
                    cleanIdentifyInfo.setIdentifyTruckNo(record.getTopPlateA());

                    log.info("检测到拖车数据，设置识别类型为拖车");
                } else if (!StringUtils.isEmpty(record.getCtnNoA())) {
                    // 有箱号，识别类型为集装箱
                    cleanIdentifyInfo.setIdentifyType(2);
                    // 只传递箱号和ISO
                    cleanIdentifyInfo.setIdentifyCtnNo(record.getCtnNoA());
                    cleanIdentifyInfo.setIdentifyCtnNoIso(record.getIsoNoA());

                    log.info("检测到集装箱数据，设置识别类型为集装箱");
                }

                // 只有当识别类型有效时才推送
                if (cleanIdentifyInfo.getIdentifyType() != null) {
                    log.info("实时推送识别结果到ECS系统，数据：{}", JsonUtil.toJson(cleanIdentifyInfo));
                    ecsService.sendIdentifyInfo(cleanIdentifyInfo);
                    log.info("实时推送识别结果到ECS系统完成");
                }
            } catch (Exception e) {
                log.error("实时推送识别结果到ECS系统异常：{}", e.getMessage(), e);
            }
        });
    }

    /**
     * 根据seqNo获取关联的图片URL列表
     * @param seqNo 序列号
     * @return 图片URL列表
     */
    private List<String> getImageUrlsBySeqNo(String seqNo) {
        try {
            if (seqNo == null || seqNo.trim().isEmpty()) {
                return null;
            }

            // 从数据库获取图片信息
            List<Img> imgList = imgRepository.queryBySeqNo(seqNo);
            if (imgList == null || imgList.isEmpty()) {
                return null;
            }

            List<String> imgUrls = new ArrayList<>();
            for (Img img : imgList) {
                if (img.getImgUrl() != null && !img.getImgUrl().trim().isEmpty()) {
                    // 转换为相对URL
                    String relativeUrl = convertToRelativeUrl(img.getImgUrl());
                    if (relativeUrl != null) {
                        imgUrls.add(relativeUrl);
                    }
                }
            }

            return imgUrls.isEmpty() ? null : imgUrls;
        } catch (Exception e) {
            log.error("获取识别结果图片URL异常 - seqNo: {}, 错误: {}", seqNo, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将绝对路径转换为前端访问URL
     * @param imgUrl 图片URL（可能是绝对路径或相对路径）
     * @return 前端可直接访问的完整URL，如果转换失败则返回null
     */
    private String convertToRelativeUrl(String imgUrl) {
        try {
            if (imgUrl == null || imgUrl.trim().isEmpty()) {
                return null;
            }

            // 如果已经是完整URL（包含http://或https://），直接返回
            if (imgUrl.startsWith("http://") || imgUrl.startsWith("https://")) {
                return imgUrl;
            }

            // 获取基础图片目录
            String baseDir = pathConfigUtil.getLocalPath();
            if (baseDir == null || baseDir.trim().isEmpty()) {
                baseDir = "D:/pfkj/img";
            }

            // 标准化路径分隔符
            String normalizedImgUrl = imgUrl.replace("\\", "/");
            String normalizedBaseDir = baseDir.replace("\\", "/");

            // 确保基础目录以/结尾
            if (!normalizedBaseDir.endsWith("/")) {
                normalizedBaseDir += "/";
            }

            String relativePath;
            // 如果图片URL以基础目录开头，则去掉基础目录前缀
            if (normalizedImgUrl.startsWith(normalizedBaseDir)) {
                relativePath = normalizedImgUrl.substring(normalizedBaseDir.length());
            } else if (normalizedImgUrl.startsWith("/")) {
                // 如果是以/开头的相对路径，去掉开头的/
                relativePath = normalizedImgUrl.substring(1);
            } else {
                // 直接使用原始路径
                relativePath = normalizedImgUrl;
            }

            // 构建完整的前端访问URL
            return buildFrontendAccessUrl(relativePath);

        } catch (Exception e) {
            log.error("转换图片路径为相对URL失败 - 路径: {}, 错误: {}", imgUrl, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建前端访问URL
     * @param relativePath 相对路径
     * @return 前端可直接访问的完整URL
     */
    private String buildFrontendAccessUrl(String relativePath) {
        try {
            if (relativePath == null || relativePath.trim().isEmpty()) {
                return null;
            }

            // 获取基础URL和URL前缀配置
            String baseUrl = getFrontendBaseUrl();
            String urlPrefix = getImageUrlPrefix();

            // 构建完整URL
            StringBuilder urlBuilder = new StringBuilder();

            // 添加基础URL
            if (baseUrl != null && !baseUrl.trim().isEmpty()) {
                urlBuilder.append(baseUrl);
                if (!baseUrl.endsWith("/")) {
                    urlBuilder.append("/");
                }
            }

            // 添加URL前缀
            if (urlPrefix != null && !urlPrefix.trim().isEmpty()) {
                if (urlPrefix.startsWith("/")) {
                    urlPrefix = urlPrefix.substring(1);
                }
                urlBuilder.append(urlPrefix);
                if (!urlPrefix.endsWith("/")) {
                    urlBuilder.append("/");
                }
            }

            // 添加相对路径
            if (relativePath.startsWith("/")) {
                relativePath = relativePath.substring(1);
            }

            // 转换门吊号格式：TRMG01 -> rmg1, TRMG02 -> rmg2
            relativePath = convertRmgNameInPath(relativePath);

            urlBuilder.append(relativePath);

            return urlBuilder.toString();
        } catch (Exception e) {
            log.error("构建前端访问URL失败 - 相对路径: {}, 错误: {}", relativePath, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换路径中的门吊号格式
     * @param path 原始路径
     * @return 转换后的路径
     */
    private String convertRmgNameInPath(String path) {
        try {
            if (path == null || path.trim().isEmpty()) {
                return path;
            }

            // 不进行门吊号格式转换，保持原始的TRMG01格式
            // 因为Nginx配置和文件存储都使用TRMG01格式
            return path;
        } catch (Exception e) {
            log.error("转换路径中门吊号格式失败 - 路径: {}, 错误: {}", path, e.getMessage(), e);
            return path;
        }
    }

    /**
     * 获取前端基础URL配置
     * @return 前端基础URL，如果未配置则返回默认值
     */
    private String getFrontendBaseUrl() {
        try {
            // 优先使用专门的前端图片服务器配置
            if (pathConfigUtil != null && pathConfigUtil.getFrontend() != null) {
                String frontendBaseUrl = pathConfigUtil.getFrontend().getBaseUrl();
                if (frontendBaseUrl != null && !frontendBaseUrl.trim().isEmpty()) {
                    // 去掉末尾的斜杠
                    if (frontendBaseUrl.endsWith("/")) {
                        frontendBaseUrl = frontendBaseUrl.substring(0, frontendBaseUrl.length() - 1);
                    }
                    return frontendBaseUrl;
                }
            }

            // 如果前端配置为空，回退到IIS地址
            String iisUrl = pathConfigUtil.getIisUrl();
            if (iisUrl != null && !iisUrl.trim().isEmpty()) {
                // 去掉末尾的斜杠
                if (iisUrl.endsWith("/")) {
                    iisUrl = iisUrl.substring(0, iisUrl.length() - 1);
                }
                return iisUrl;
            }

            return "http://localhost:8080"; // 默认值
        } catch (Exception e) {
            log.error("获取前端基础URL配置异常", e);
            return "http://localhost:8080";
        }
    }

    /**
     * 获取图片URL前缀配置
     * @return URL前缀，如果未配置则返回默认值
     */
    private String getImageUrlPrefix() {
        try {
            // 优先使用专门的前端图片URL前缀配置
            if (pathConfigUtil != null && pathConfigUtil.getFrontend() != null) {
                String urlPrefix = pathConfigUtil.getFrontend().getUrlPrefix();
                if (urlPrefix != null && !urlPrefix.trim().isEmpty()) {
                    return urlPrefix.trim();
                }
            }

            // 默认使用img作为URL前缀
            return "img";
        } catch (Exception e) {
            log.error("获取图片URL前缀配置异常", e);
            return "img";
        }
    }

}
