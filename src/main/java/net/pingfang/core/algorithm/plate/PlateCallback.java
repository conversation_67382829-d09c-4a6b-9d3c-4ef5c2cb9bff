package net.pingfang.core.algorithm.plate;

import com.sun.jna.platform.win32.WinDef;
import com.sun.jna.win32.StdCallLibrary;
import org.springframework.stereotype.Component;

/**
 * XXX
 *
 * @author: CM
 * @date: 2024/4/18
 */
@Component
public interface PlateCallback extends StdCallLibrary.StdCallCallback {

    void invoke(PlateThree.LprResult pLPRR, PlateThree.ImgInfo pVehImg, PlateThree.PlateImgInfo pPlateImg, WinDef.LPVOID pParam);

}
