package net.pingfang.core.algorithm.plate;

import com.sun.jna.Library;
import com.sun.jna.platform.win32.WinDef.LPVOID;
import net.pingfang.core.SmartStructure;

/**
 * XXX
 *
 * @author: CM
 * @date: 2024/4/18
 */
public interface PlateThree extends Library {

    //1、添加相机
    //参数：
    //        【in】szIP：相机IP地址。
    //返回值：
    //见返回值说明
     int  add_camera(String szIP);





    //2、删除相机
    //    参数：
    //            【in】szIP：相机IP地址。
    //    返回值：
    //    见返回值说明
     int  del_camera(String szIP);


    //3、播放视频
    //参数：
    //            【in】hPlayWnd：窗口句柄。
    //            【in】szIP：相机IP地址。
    //    返回值：
    //    见返回值说明
     int  play_video(LPVOID hPlayWnd, String szIP);


    //4、停止视频
    // 参数：
    //            【in】szIP：相机IP地址。
    //    返回值：
    //    见返回值说明
     int  stop_video(String szIP);


    //5、注册识别结果回调函数
    //    参数：
    //            【in】szIP：相机IP地址。
    //            【in】LPRResult_CB：回调函数。具体见LPRResult_CALLBACK
    //【in】pParam：用户参数。
    //    返回值：
    //    见返回值说明
    //
    //    void (CALLBACK *LPRResult_CALLBACK)(LPRResult* pLPRR, ImgInfo* pVehImg, PlateImgInfo* pPlateImg, LPVOID pParam);
    //    参数：
    //            【out】pLPRR：车牌识别结果。
    //            【out】pVehImg：车辆图片信息。具体见车头大图图片信息ImgInfo结构体定义。
    //            【out】pPlateImg：车牌图片信息。具体见车牌小图图片信息PlateImgInfo结构体定义。
    //            【out】pParam：用户参数。
    //    返回值：
    //    无
     int reg_lpr_callback(String szIP, LPRResult_CALLBACK LPRResult_CB, LPVOID pParam);


    //6、软触发
    //参数：
    //            【in】szIP：相机IP地址。
    //    返回值：
    //    见返回值说明
     int  soft_trigger(String szIP);


    public final static int  MAX_IMG_DATA_LEN =1024*1024*2;

    public final static int  MAX_PLATE_IMG_DATA_LEN=512*1024;
    /**
     * 车牌识别结果
     */
    public static class LprResult extends SmartStructure {
        /**
         * 抓拍相机IP
         */
        public byte[] ip = new byte[16];

        /**
         * 通道号
         */
        public int lane;

        /**
         * 抓拍时间
         */
        public byte[] time = new byte[32];

        /**
         * 车牌号码
         */
        public byte[] plate = new byte[16];

        /**
         * 车牌颜色
         */
        public byte[] color_plate = new byte[8];

        /**
         * 识别结果的可信度
         */
        public float r_plate;

        /**
         * 国家代码（三位）
         */
        public byte[] country_code = new byte[4];

        /**
         * 0:识别成功，1识别失败
         */
        public int nFlag;
    }

    /**
     * 车头大图图片信息
     */
    public static class ImgInfo extends SmartStructure {
        /**
         * 接收图片成功，其他失败
         */
        public int nFlag;
        /**
         * 图片宽度
         */
        public int nWidth;
        /**
         * 图片高度
         */
        public int nHeight;
        /**
         * 图片类型，0：jpg；1：bmp
         */
        public int nImgDataType;
        /**
         * 图片数据实际长度
         */
        public int nImgDataLen;
        /**
         * 图片高度图片数据
         */
        public byte[] pImgData=new byte[MAX_IMG_DATA_LEN];;
    }

    /**
     * 车牌小图图片信息
     */
    public static class PlateImgInfo extends SmartStructure {
        /**
         * 接收图片成功，其他失败
         */
        public int nFlag;
        /**
         * 图片宽度
         */
        public int nWidth;
        /**
         * 图片高度
         */
        public int nHeight;
        /**
         * 图片类型，0：jpg；1：bmp
         */
        public int nImgDataType;
        /**
         * 图片数据实际长度
         */
        public int nImgDataLen;
        /**
         * 图片高度图片数据
         */
        public byte[] pPlateImgData=new byte[MAX_PLATE_IMG_DATA_LEN];;
    }

}
