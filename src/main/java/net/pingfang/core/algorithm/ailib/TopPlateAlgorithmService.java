package net.pingfang.core.algorithm.ailib;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.core.DllFactory;
import net.pingfang.core.algorithm.ailib.model.DetectResult;
import net.pingfang.core.algorithm.ailib.model.DetectResults;
import net.pingfang.core.algorithm.ailib.model.PlateResult;
import net.pingfang.core.algorithm.ailib.model.RecTopPlate;
import net.pingfang.enums.PlateNumberEnum;
import net.pingfang.util.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/5/30
 */
@Component
@Slf4j
public class TopPlateAlgorithmService {
    @Autowired
    private DllFactory dllFactory;

    private TopPlate topPlate = null;

    /**
     * 初始化
     */
    public void init() {
        topPlate = dllFactory.getTopPlateSdk();
        log.debug("车顶号算法服务初始化完成");
    }

    public DetectResults detPicture(String filePath) {
        long startTime = System.currentTimeMillis();

        DetectResults detectResults = new DetectResults();
        int i = topPlate.det_img_file(filePath, detectResults);
        long endTime = System.currentTimeMillis();
        log.debug("车顶号识别:图片:{}调用完毕：{},检测耗时:{}ms,图片检测结果{}", filePath, i, (endTime - startTime), JsonUtil.toJson(detectResults));
        return detectResults;
    }

    public RecTopPlate recPicture(String filePath, DetectResults detectResults) {
        RecTopPlate recTopPlate = new RecTopPlate();
        if (detectResults == null || detectResults.getDet_num() == 0) {
            log.debug("未检测到数据,不进行识别");
            return recTopPlate;
        }

        //根据可信度排序
        List<DetectResult> detectResultList = Arrays.stream(detectResults.dr)
                .sorted(Comparator.comparing(DetectResult::getDet_fr).reversed())
                .collect(Collectors.toList());
        String subCode;
        String subPlate = "";
        //依次识别，直到拿到不为空的车顶号即返回
        for (DetectResult detectResult : detectResultList) {
            PlateResult plateResult = new PlateResult();
            int ifSuccess = topPlate.recg_plate_img_file(filePath, detectResult, plateResult);

            String plateNumber = new String(plateResult.getPlate(), StandardCharsets.UTF_8).trim();

            if (!StringUtils.isEmpty(plateNumber)) {
                //第一个字符或第四个字符是0，通过校验，进行中文替换
                if (plateNumber.length()>=4 && plateNumber.startsWith("0") && plateNumber.startsWith("0",3)){
                    if (plateNumber.length() > 4){
                        subPlate = plateNumber.substring(4);
                    }
                    subCode = plateNumber.substring(0, 4);
                    plateNumber = PlateNumberEnum.getWordByCode(subCode) + subPlate;
                }else {
                    continue;
                }

                //车顶号不为空才进行设置并直接返回
                if (!Float.isNaN(plateResult.getTrust()) && plateResult.getTrust() > 0) {
                    //目前车顶号可信度不能用，先用识别区域的可信度，后面算法更新这点后再换掉
//                    recTopPlate.setTrust(plateResult.getTrust());//车顶号可信度
                    recTopPlate.setTrust(detectResult.getDet_fr());//识别区域可信度
                } else {
                    recTopPlate.setTrust(detectResult.getDet_fr());
                }
                recTopPlate.setTopPlate(plateNumber);
                break;
            }
        }
        return recTopPlate;
    }

}
