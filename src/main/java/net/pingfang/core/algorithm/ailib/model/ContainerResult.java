package net.pingfang.core.algorithm.ailib.model;

import net.pingfang.core.SmartStructure;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 单箱号结果
 *
 * @author: CM
 * @date: 2023/5/29
 */
@Data
@NoArgsConstructor
public class ContainerResult extends SmartStructure {

    public byte[] id = new byte[16]; 			//箱号
    public byte[] iso = new byte[16];			//iso代码
    public RectDet det_rect;		//箱号区域
    public float trust;			//整个结果可信度
    public float idConf;			//箱号识别结果置信度
    public float isoConf;			//ISO识别结果置信度
    public boolean bcheck;			//是否通过校验
    public byte[] szNote = new byte[64];			//备注信息（用于存放部分无法合成的字符识别结果）

}
