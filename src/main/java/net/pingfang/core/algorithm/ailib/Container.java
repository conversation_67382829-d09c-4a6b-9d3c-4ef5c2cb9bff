package net.pingfang.core.algorithm.ailib;

import com.sun.jna.Library;
import net.pingfang.core.algorithm.ailib.model.ContainerResults;
import net.pingfang.core.algorithm.ailib.model.DetectResults;
import net.pingfang.core.algorithm.ailib.model.ImgData;
import net.pingfang.core.algorithm.ailib.model.TagVersion;

/**
 * 箱号识别
 *
 * @author: CM
 * @date: 2023/5/29
 */
public interface Container extends Library {


    /**
     * 检测识别初始化
     *
     * @return 返回初始化结果
     */
    int InitDetector();

    /**
     * 检测图片文件/检测图片数据（原接口）
     *
     * @param img_path 图片文件路径
     * @param pdrs     箱号检测结果
     * @return
     */
    int det_img_file(String img_path, DetectResults pdrs);

    /**
     * 吊具摄像头下的箱号检测（文件）
     *
     * @param img_path 图片文件路径
     * @param cameraID 相机ID
     * @param pdrs     箱号检测结果
     * @return
     */

    int det_img_spreader_file(String img_path, int cameraID, DetectResults pdrs);

    /**
     * 检测图片文件/检测图片数据（原接口）
     *
     * @param pImgData 图片数据结构
     * @param pdrs     箱号检测结果
     * @return
     */
    int det_img(ImgData pImgData, DetectResults pdrs);

    /**
     * 吊具摄像头下的箱号检测（图片数据）
     *
     * @param pImgData 图片数据结构
     * @param cameraID 相机ID
     * @param pdrs     箱号检测结果
     * @return
     */
    int det_img_spreader(ImgData pImgData, int cameraID, DetectResults pdrs);

    /**
     * 识别图片文件/识别图片数据（原接口）
     *
     * @param filePath 图片文件路径
     * @param pdrs     第二步的检测结果
     * @param pcrs     箱号识别结果，具体见新箱号识别结果定义
     * @return
     */
    int RecContainerFile(String filePath, DetectResults pdrs, ContainerResults pcrs);

    /**
     * 吊具摄像头下的箱号识别（文件）
     *
     * @param filePath 图片文件路径
     * @param cameraID 相机ID
     * @param pdrs     第二步的检测结果
     * @param pcrs     箱号识别结果，具体见新箱号识别结果定义
     * @return
     */
    int RecContainerSpreaderFile(String filePath, int cameraID, DetectResults pdrs, ContainerResults pcrs);

    /**
     * 识别图片文件/识别图片数据（原接口）
     * @param pImgData 图片数据结构
     * @param pdrs 第二步的检测结果
     * @param pcrs 箱号识别结果，具体见新箱号识别结果定义
     * @return
     */
    int RecContainerImg(ImgData pImgData, DetectResults pdrs, ContainerResults pcrs);

    /**
     * 吊具摄像头下的箱号识别（图片数据）
     * @param pImgData 图片数据结构
     * @param cameraID 相机ID
     * @param pdrs 第二步的检测结果
     * @param pcrs 箱号识别结果，具体见新箱号识别结果定义
     * @return
     */
    int RecContainerSpreaderImg(ImgData pImgData, int cameraID, DetectResults pdrs, ContainerResults pcrs);


     int GetAlgVersion(TagVersion version);


}
