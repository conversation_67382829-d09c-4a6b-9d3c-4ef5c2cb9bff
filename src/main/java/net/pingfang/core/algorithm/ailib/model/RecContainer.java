package net.pingfang.core.algorithm.ailib.model;

import lombok.Data;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/5/31
 */
@Data
public class RecContainer {
    /**
     * 箱号
     */
    public String ctnNo;
    /**
     * iso代码
     */
    public String iso;
    /**
     * iso是否被校正,即与当前箱号不在一个识别结果集中
     */
    public Boolean isIsoCheck;
    /**
     * 箱号区域
     */
    public RectDet detRect;
    /**
     * 整个结果可信度
     */
    public float trust;
    /**
     * 箱号识别结果置信度
     */
    public float idConf;
    /**
     * ISO识别结果置信度
     */
    public float isoConf;
    /**
     * 是否通过校验
     */
    public Boolean bcheck;
    /**
     * 备注信息（用于存放部分无法合成的字符识别结果）
     */
    public String szNote;
    /**
     * 图片路径
     */
    public String imgPath;

    /**
     * 铅封有无
     */
    public Boolean isSeal;

    /**
     * 是否处于吊具下方
     */
    public Boolean isUnderSpreader;


}
