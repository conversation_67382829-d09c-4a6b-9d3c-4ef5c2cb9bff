package net.pingfang.core.algorithm.ailib.model;

import net.pingfang.core.SmartStructure;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * 图片数据
 *
 * @author: CM
 * @date: 2023/5/29
 */
@Data
@NoArgsConstructor
public class ImgData extends SmartStructure {
    public String imgInfo; //实时采集为相机的ip,录像采集为录像文件的完整路径。
    //int nFlag;//实时图片为0，录像采集图片为1
    public int width;
    public int heigth;
    public int imgType;//0:jpg,1:bmp, 2:cv::Mat,3:bitmap
    public int dataLen;
    public byte[] imgData = new byte[54 + 3840 * 2160 * 3];

}
