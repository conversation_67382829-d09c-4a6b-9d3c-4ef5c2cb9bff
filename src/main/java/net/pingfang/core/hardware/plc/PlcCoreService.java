package net.pingfang.core.hardware.plc;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.config.ProjectConfig;
import net.pingfang.core.flow.CoreFlow;
import net.pingfang.core.hardware.rtsp.RtspService;
import net.pingfang.enums.WebMessageTypeEnum;
import net.pingfang.model.dto.MessageDTO;
import net.pingfang.model.entity.Lane;
import net.pingfang.repository.LaneRepository;
import net.pingfang.service.ThreadPoolManagerService;
import net.pingfang.service.ContainerSurfaceService;
import net.pingfang.util.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;


/**
 * PLC相关方法
 *
 * @author: CM
 * @date: 2023/5/31
 */
@Component
@Slf4j
public class PlcCoreService {

    @Resource
    private CoreFlow coreFlow;
    @Resource
    private PathConfigUtil pathConfigUtil;
    @Resource
    private ProjectConfig projectConfig;
    @Resource
    private WebSocketUtil webSocketUtil;
    @Resource
    private ExternalConfigUtil externalConfigUtil;
    @Resource
    private RestTemplateUtil restTemplateUtil;
    @Resource
    private LaneRepository laneRepository;
    @Resource
    private RtspService rtspService;
    @Resource
    private net.pingfang.config.RtspConfig rtspConfig;
    @Resource
    private net.pingfang.config.ContainerSurfaceConfig containerSurfaceConfig;
    @Resource
    private ContainerSurfaceService containerSurfaceService;
    @Resource
    private ThreadPoolManagerService threadPoolManager;

    @PostConstruct
    private void validateConfig() {
        log.info("=== RTSP配置验证开始 ===");
        log.info("rtspConfig对象: {}", rtspConfig);
        log.info("recognition对象: {}", rtspConfig.getRecognition());
        log.info("最低高度配置值: {}", rtspConfig.getRecognition().getMinHeight());
        log.info("最高高度配置值: {}", rtspConfig.getRecognition().getMaxHeight());

        if (!rtspConfig.getRecognition().isValid()) {
            log.warn("RTSP高度配置不合理 - 最低高度:{}, 最高高度:{}, 将使用默认值",
                    rtspConfig.getRecognition().getMinHeight(),
                    rtspConfig.getRecognition().getMaxHeight());
        } else {
            log.info("RTSP高度配置 - 识别范围: {}~{}米",
                    rtspConfig.getRecognition().getMinHeight(),
                    rtspConfig.getRecognition().getMaxHeight());
        }
        log.info("=== RTSP配置验证结束 ===");
    }
    //开启识别的高度
    @Value("${identity.height:5000}")
    public Integer identityHeight;
    //装卸plcX的分界线
    @Value("${identity.line:5000}")
    public Integer identityLane;

    //plc变化幅度临界值
    @Value("${plc.changeValue:20000}")
    public Integer changeValue;

    // RTSP一次性启动保护机制
    private boolean rtspStartedInCurrentCycle = false; // 当前周期是否已启动过RTSP
    private Float lastHeightWhenStarted = null; // 启动时的高度，用于判断新周期

    // 箱面识别一次性启动保护机制
    private boolean containerSurfaceStartedInCurrentCycle = false; // 当前周期是否已启动过箱面识别
    private Float lastHeightWhenContainerSurfaceStarted = null; // 箱面识别启动时的高度
    private boolean containerSurfaceReportedInCurrentCycle = false; // 当前周期是否已上报过ECS

    @Value("${plc.initPlcYMultiple}")
    public Integer initPlcYMultiple;
    @Value("${plc.initPlcXMultiple}")
    public Integer initPlcXMultiple;

    /**
     * 初始化	PLC标志
     */
    @Value("${plc.initPlcFlag}")
    public Boolean initPlcFlag;

    /**
     * 吊机是否存在Y坐标
     */
    @Value("${plc.ifExistY}")
    public Boolean ifExistY;

    /**
     * 吊机不存在Y值时，上锁流程后lockAndStartDetectTime秒开启识别流程
     */
    @Value("${plc.lockAndStartDetectTime}")
    public Integer lockAndStartDetectTime;

    /**
     * 唯一作业编号
     */

    private static volatile String passTime = null;

    /**
     * 任务ID（用于RTSP预识别）
     */
    public static volatile String taskId = "0";

    /**
     * 上一吊plc
     */
    // TODO 谢小惠
    private static volatile Plc lastPlc = null;

    /**
     * 用来控制2s获取一次PLC暂存数据
     */
    private static volatile Long tempTime = 0L;

    /**
     * 上锁时间,用在两个地方,1-刚上锁时记录上锁持续时间是否到达启动识别流程, 2-识别过程中或者解锁后都设置成0L,避免识别中一直在反复开启识别流程
     */
    private static volatile Long tempLockTime = 0L;

    private Integer workType;

    /**
     * 识别数据类型(1-拖车2-集装箱3-火车车厢4-箱门朝向)
     */
    private Integer identifyType;

    /**
     * 箱门朝向: 0-朝向左，1-朝向右
     */
    private Integer ctnDoorOrientation;

    /**
     * 解锁状态
     */
    private static boolean unlockState = false;

    /**
     * 处理PLC数据
     */
    public synchronized void dealPlcData(Plc plc) {
        if (preCheckPLCData(plc)) return;


        //查询当前plcX所属的车道
        Lane lane = laneRepository.queryByPlcX(plc.getPlcY());
        plc.setLane(lane == null ? "未知" : lane.getLane());
        //websocket发送给前台
        MessageDTO messageDTO = new MessageDTO();
        messageDTO.setTime(DateUtil.getDate(DateUtil.TYPE_THREE));
        messageDTO.setObject(plc);
        messageDTO.setType(WebMessageTypeEnum.PLC.getValue());
        webSocketUtil.sendMessageToWeb(messageDTO);

        if (lane == null) {
            log.info("PLC Y:{} , 未知车道,无法进入识别逻辑",plc.getPlcY());
            return;
        }

        // 分离的启动和结束逻辑
        if (shouldStartRtsp(plc)) {
            startRtspIfNeeded(plc);
        }

        if (shouldEndRtsp(plc)) {
            endRtspIfNeeded(plc);
        }

        // 箱面识别逻辑 (9.5~10.5米范围)
        // 检查并重置箱面识别周期
        checkAndResetContainerSurfaceCycle(plc);

        // 详细记录相面识别判断过程
//        logContainerSurfaceRecognitionCheck(plc);

        if (shouldStartContainerSurfaceRecognition(plc)) {
            startContainerSurfaceRecognition(plc);
        }

        //每2秒，更新一次lastPlc
        long currentTimeMillis = System.currentTimeMillis();
        if (currentTimeMillis - tempTime >= 2 * 1000) {
            lastPlc = plc;
            tempTime = currentTimeMillis;
        }
    }

    private boolean preCheckPLCData(Plc plc) {
        if (plc == null || plc.getLock() == null) {
        log.info("PLC数据为null或者开闭锁信号为null,当前PLC数据丢弃");
                    return true;
        }

        if(plc.getPlcX() > 60000) {
        	plc.setPlcX(plc.getPlcX()-65535);
        }

        //确定上锁状态
        int type = getLockType(plc);
        if (type == 0) {
            log.info("PLC数据开闭锁变化异常,当前PLC数据丢弃");
            return true;
        }

        //plcX在设定的范围内时通知第三方已复位
//        if (externalConfigUtil.getRestorationSync().equals(TrueFalseEnum.YES.getDescExtOne()) && externalConfigUtil.getRestorationEnd() >= plc.getPlcX() && plc.getPlcX() >= externalConfigUtil.getRestorationStart()){
//            restoration();
//        }
        log.debug("原plcX坐标："+ plc.getPlcX()+"原plcY坐标："+ plc.getPlcY());

        //刚启动时，初始化一个lastPlc，如果不初始化，则2秒后会把当前plc更新给lastPlc
        if (initPlcFlag){
            lastPlc = new Plc();
            lastPlc.setPlcX(initPlcXMultiple*65535 + plc.getPlcX());
            lastPlc.setPlcY(initPlcYMultiple*65535 + plc.getPlcY());
            lastPlc.setLock(plc.getLock());
            initPlcFlag = false;
            log.info("plcX坐标："+ plc.getPlcX()+"plcY坐标："+ plc.getPlcY());
            log.info("lastPlcX坐标："+lastPlc.getPlcX()+",lastPlcY坐标："+lastPlc.getPlcY());
        }
        //plc值在65535和0时会突变,通过下面的方式修正为有效数据，changeValue代表突变的范围。
        if(lastPlc!=null ){
            int y = (int) Math.floor((double) (lastPlc.getPlcY() - plc.getPlcY() - changeValue) /65535)+1;
            plc.setPlcY(y >0 ? plc.getPlcY() + (y)*65535 : plc.getPlcY());
            int x = (int) Math.floor((double) (lastPlc.getPlcX() - plc.getPlcX() - changeValue) /65535)+1;
            plc.setPlcX(x >0 ? plc.getPlcX() + (x)*65535 : plc.getPlcX());

            log.debug("lastPlcX坐标："+lastPlc.getPlcX()+",lastPlcY坐标："+lastPlc.getPlcY()+"plcX坐标："+ plc.getPlcX()+"plcY坐标："+ plc.getPlcY());
        }
        return false;
    }



    /**
     * 检查是否应该启动RTSP预识别（仅下降方向，一次性启动）
     * 只在吊具从上到下（下降）时启动，从下到上（上升）时不启动
     * 在一次下降流程中只启动一次，避免重复启动
     * @param plc PLC数据
     * @return true-应该启动，false-不应该启动
     */
    private boolean shouldStartRtsp(Plc plc) {
        // 前置条件：RTSP功能启用 且 当前未在识别中
        if (!projectConfig.getIfRtspDetect() || RtspService.isCapturing) {
            return false;
        }

        // 检查是否需要重置周期（吊具上升到高位时重置）
        checkAndResetRtspCycle(plc);

        // 一次性启动保护：如果当前周期已启动过，则不再启动
        if (rtspStartedInCurrentCycle) {
            log.debug("RTSP当前周期已启动过，跳过重复启动检查");
            return false;
        }

        log.debug("RTSP功能已启用且未在识别中，开始检查启动条件");

        // 启动条件检查
        boolean lockCondition = plc.getLock() == 0; // 开锁状态
        boolean taskStatusCondition = plc.getTaskStatus() != null && plc.getTaskStatus() == 1;
        boolean workTypeCondition = plc.getWorkType() != null &&
                                  plc.getWorkType() >= 1 && plc.getWorkType() <= 8;
        // 箱面高度条件：5.0~7.5米范围内才抓拍
        boolean containerHeightCondition = checkContainerHeightCondition(plc);

        // 位置差异检查
        boolean positionDifferenceCondition = checkPositionDifference(plc);

        // 新增：检查吊具运动方向（仅下降时启动）
        boolean movementDirectionCondition = checkMovementDirection(plc);

        // 临时强制设置为true（根据原代码）
        taskStatusCondition = true;
        workTypeCondition = true;

        boolean shouldStart = lockCondition && taskStatusCondition && workTypeCondition &&
                            containerHeightCondition && positionDifferenceCondition && movementDirectionCondition;

        // 如果满足启动条件，标记当前周期已启动
        if (shouldStart) {
            rtspStartedInCurrentCycle = true;
            lastHeightWhenStarted = plc.getSpreaderFromContainerHeight();
            log.info("RTSP预识别启动成功，标记当前周期已启动 - 启动高度:{}米", lastHeightWhenStarted);
        } else {
//            if (!movementDirectionCondition) {
//                log.info("❌ RTSP预识别未启动 - 吊具未在下降（可能在上升或静止）");
//            } else if (!containerHeightCondition) {
//                log.info("❌ RTSP预识别未启动 - 高度不在范围内，当前高度:{}, 配置范围:{}~{}米",
//                        plc.getSpreaderFromContainerHeight(),
//                        rtspConfig.getRecognition().getMinHeight(),
//                        rtspConfig.getRecognition().getMaxHeight());
//            } else {
//                log.info("❌ RTSP预识别未启动 - 其他条件不满足");
//            }
        }

        return shouldStart;
    }

    /**
     * 检查箱面高度条件
     * @param plc PLC数据
     * @return true-满足箱面高度条件，false-不满足
     */
    private boolean checkContainerHeightCondition(Plc plc) {
        if (plc.getSpreaderFromContainerHeight() == null) {
            log.debug("箱面高度检查 - 高度数据为空");
            return false;
        }

        float height = plc.getSpreaderFromContainerHeight();

        // 排除无效数据
        if (height == 999.0f || height <= 0) {
            log.debug("箱面高度检查 - 无效高度数据: {}", height);
            return false;
        }

        // 获取配置的高度范围
        float minHeight = rtspConfig.getRecognition().getMinHeight();
        float maxHeight = rtspConfig.getRecognition().getMaxHeight();

        // 检查是否在配置的高度范围内
        boolean condition = height >= minHeight && height <= maxHeight;

        log.debug("箱面高度检查 - 当前高度:{}, 条件({}~{}米):{}",
                height, minHeight, maxHeight, condition);

        return condition;
    }

    /**
     * 检查高度是否过低（用于结束条件）
     * @param plc PLC数据
     * @return true-高度低于最低识别高度，false-高度不低于最低识别高度
     */
    private boolean checkHeightTooLow(Plc plc) {
        if (plc.getSpreaderFromContainerHeight() == null) {
            log.info("高度过低检查 - 高度数据为空，视为过低");
            return true; // 没有高度数据时，为安全起见视为过低
        }

        float height = plc.getSpreaderFromContainerHeight();

        // 排除无效数据
        if (height == 999.0f || height <= 0) {
            log.info("高度过低检查 - 无效高度数据: {}，视为过低", height);
            return true; // 无效数据时，为安全起见视为过低
        }

        // 获取配置的最低识别高度
        float minHeight = rtspConfig.getRecognition().getMinHeight();

        // 检查是否低于最低识别高度
        boolean tooLow = height < minHeight;

        log.info("高度过低检查 - 当前高度:{}, 配置最低高度:{}, 是否过低:{}", height, minHeight, tooLow);

        return tooLow;
    }

    /**
     * 检查位置差异条件
     * @param plc PLC数据
     * @return true-满足位置差异条件，false-不满足
     */
    private boolean checkPositionDifference(Plc plc) {
        if (plc.getTrolleyStartPosition() == null) {
            return false;
        }

        float positionDifference = Math.abs(plc.getPlcY() - plc.getTrolleyStartPosition());
        boolean condition = positionDifference >= 0.5f;

        log.debug("位置差异检查 - 当前位置:{}, 起始位置:{}, 差异:{}, 条件(>=0.5):{}",
                plc.getPlcY(), plc.getTrolleyStartPosition(), positionDifference, condition);

        return condition;
    }

    /**
     * 检查吊具运动方向（仅下降时启动）
     * @param plc PLC数据
     * @return true-吊具正在下降，false-吊具正在上升或静止
     */
    private boolean checkMovementDirection(Plc plc) {
        // 如果没有历史数据，无法判断方向，默认不启动
        if (lastPlc == null || lastPlc.getSpreaderFromContainerHeight() == null ||
            plc.getSpreaderFromContainerHeight() == null) {
            log.debug("运动方向检查 - 缺少历史高度数据，默认不启动");
            return false;
        }

        float lastHeight = lastPlc.getSpreaderFromContainerHeight();
        float currentHeight = plc.getSpreaderFromContainerHeight();

        // 排除无效数据
        if (lastHeight == 999.0f || currentHeight == 999.0f ||
            lastHeight <= 0 || currentHeight <= 0) {
            log.debug("运动方向检查 - 高度数据无效，默认不启动");
            return false;
        }

        // 计算高度变化
        float heightChange = currentHeight - lastHeight;

        // 判断运动方向：高度减少表示下降
        boolean isDescending = heightChange < -0.1f; // 下降超过0.1米才认为是下降

        log.debug("运动方向检查 - 上次高度:{}, 当前高度:{}, 高度变化:{}, 是否下降:{}",
                lastHeight, currentHeight, heightChange, isDescending);

        return isDescending;
    }

    /**
     * 检查并重置RTSP周期
     * 当吊具上升到高位时，重置启动标志，准备下一次下降启动
     * @param plc PLC数据
     */
    private void checkAndResetRtspCycle(Plc plc) {
        if (plc.getSpreaderFromContainerHeight() == null) {
            return;
        }

        float currentHeight = plc.getSpreaderFromContainerHeight();

        // 排除无效数据
        if (currentHeight == 999.0f || currentHeight <= 0) {
            return;
        }

        // 重置条件：吊具上升到高位（超过最大识别高度+1米）
        float resetHeight = rtspConfig.getRecognition().getMaxHeight() + 1.0f;

        if (rtspStartedInCurrentCycle && currentHeight > resetHeight) {
            rtspStartedInCurrentCycle = false;
            lastHeightWhenStarted = null;
            log.info("RTSP周期已重置 - 当前高度:{}米 > 重置高度:{}米，准备下一次下降启动",
                    currentHeight, resetHeight);
        }
    }

    /**
     * 检查是否应该结束RTSP预识别
     * @param plc PLC数据
     * @return true-应该结束，false-不应该结束
     */
    private boolean shouldEndRtsp(Plc plc) {
        // 前置条件：当前正在识别中
        if (!RtspService.isCapturing) {
            return false;
        }

        // 结束条件检查
        boolean lockEndCondition = plc.getLock() == 1; // 上锁状态
        boolean heightTooLowCondition = checkHeightTooLow(plc); // 高度低于5.0米
        boolean notPreEndCondition = !RtspService.preEnd; // 未准备结束

        boolean shouldEnd = (lockEndCondition || heightTooLowCondition) && notPreEndCondition;

        log.info("RTSP结束条件检查 - 上锁:{}, 高度过低:{}, 未准备结束:{}, 结果:{}",
                lockEndCondition, heightTooLowCondition, notPreEndCondition, shouldEnd);

        return shouldEnd;
    }

    /**
     * 启动RTSP预识别
     * @param plc PLC数据
     */
    private void startRtspIfNeeded(Plc plc) {
        try {
            rtspService.start();
            log.info("RTSP预识别启动成功 - seqNo: {}", rtspService.getCurrentSeqNo());

            // 记录启动参数
            log.info("启动参数 - 任务状态:{}, 作业类型:{}, 小车坐标:{}, 箱面高度:{}",
                    plc.getTaskStatus(), plc.getWorkType(), plc.getPlcY(),
                    plc.getSpreaderFromContainerHeight());
        } catch (Exception e) {
            log.error("RTSP预识别启动失败", e);
        }
    }

    /**
     * 结束RTSP预识别
     * @param plc PLC数据
     */
    private void endRtspIfNeeded(Plc plc) {
        // 准备结束识别
        RtspService.preEnd = true;
        String currentSeqNo = rtspService.getCurrentSeqNo() != null ? rtspService.getCurrentSeqNo() : "unknown";

        // 判断结束原因
        String endReason;
        if (plc.getLock() == 1) {
            endReason = "上锁";
        } else if (checkHeightTooLow(plc)) {
            float height = plc.getSpreaderFromContainerHeight() != null ? plc.getSpreaderFromContainerHeight() : -1;
            float minHeight = rtspConfig.getRecognition().getMinHeight();
            log.info("调试信息 - rtspConfig: {}, recognition: {}, minHeight: {}",
                    rtspConfig, rtspConfig.getRecognition(), minHeight);
            endReason = String.format("高度过低(当前:%.1f米,低于%.1f米)", height, minHeight);
        } else {
            endReason = "其他原因";
        }

        log.info("{}因{}准备结束RTSP预识别流程...", currentSeqNo, endReason);

        String taskDescription = String.format("RTSP结束延迟任务 - 延迟%d秒", rtspService.getRtspConfig().getEndDelayTime());
        threadPoolManager.submitPlcTask(() -> {
            try {
                Thread.sleep(rtspService.getRtspConfig().getEndDelayTime() * 1000);
            } catch (InterruptedException e) {
                log.warn("RTSP结束延迟被中断", e);
                return;
            }

            // 没有重新被开启识别，正常结束
            if (RtspService.preEnd) {
                log.info("RTSP预识别流程结束");
                rtspService.end();

                // 重置RTSP启动标志，允许下一次启动
                rtspStartedInCurrentCycle = false;
                lastHeightWhenStarted = null;
                log.info("RTSP周期已重置 - 预识别流程结束");
            }
        }, taskDescription);
    }

    /**
     * 检查是否应该启动箱面识别（配置高度范围，闭锁状态，仅上升时）
     * @param plc PLC数据
     * @return true-应该启动，false-不应该启动
     */
    private boolean shouldStartContainerSurfaceRecognition(Plc plc) {
        // 检查功能是否启用
        if (!containerSurfaceConfig.isEnabled()) {
            return false;
        }

        // 检查高度数据（使用相面高度）
        if (plc.getSpreaderFromContainerHeight() == null) {
            return false;
        }

        float height = plc.getSpreaderFromContainerHeight();

        // 排除无效数据
        if (height == 999.0f || height <= 0) {
            return false;
        }

        // 高度条件：使用配置文件中的范围
        float minHeight = containerSurfaceConfig.getRecognition().getMinHeight();
        float maxHeight = containerSurfaceConfig.getRecognition().getMaxHeight();
        boolean heightCondition = height >= minHeight && height <= maxHeight;

        // 一次性启动保护：如果当前周期已启动过，则不再启动
        if (containerSurfaceStartedInCurrentCycle) {
            log.debug("箱面识别当前周期已启动过，跳过重复启动检查");
            return false;
        }

        // 🆕 运动方向条件：仅上升时启动
        boolean movementDirectionCondition = checkUpwardMovement(plc);
        if (!movementDirectionCondition) {
            log.debug("箱面识别未启动 - 吊具未在上升（可能在下降或静止）");
            return false;
        }

        // 闭锁状态条件
        boolean lockCondition = plc.getLock() == 1; // 闭锁状态

        // 任务状态条件
        boolean taskStatusCondition = plc.getTaskStatus() != null && plc.getTaskStatus() == 1;

        // 作业类型条件（暂时允许所有类型，后续可根据需要调整）
        boolean workTypeCondition = plc.getWorkType() != null && plc.getWorkType() >= 1;

        boolean shouldStart = heightCondition && movementDirectionCondition && lockCondition && taskStatusCondition && workTypeCondition;

        log.debug("箱面识别启动条件检查 - 高度:{} (范围:{}~{}), 上升:{}, 闭锁:{}, 任务状态:{}, 作业类型:{}, 结果:{}",
                height, minHeight, maxHeight, movementDirectionCondition, lockCondition, taskStatusCondition, workTypeCondition, shouldStart);

        return shouldStart;
    }

    /**
     * 记录相面识别判断过程的详细日志
     * @param plc PLC数据
     */
    private void logContainerSurfaceRecognitionCheck(Plc plc) {
        try {
            // 基础信息
            String laneInfo = plc.getLane() != null ? plc.getLane() : "未知";
            Float height = plc.getSpreaderFromContainerHeight(); // 使用相面高度
            Float hoistHeight = plc.getHeight(); // 吊具高度
            Integer lock = plc.getLock();
            Short taskStatusShort = plc.getTaskStatus();
            Short workTypeShort = plc.getWorkType();
            Integer taskStatus = taskStatusShort != null ? taskStatusShort.intValue() : null;
            Integer workType = workTypeShort != null ? workTypeShort.intValue() : null;

            log.info("=== 相面识别判断开始 === 车道:{}, 相面高度:{}米, 吊具高度:{}米, 锁状态:{}, 任务状态:{}, 作业类型:{}",
                    laneInfo, height, hoistHeight, lock, taskStatus, workType);

            // 1. 功能启用检查
            boolean enabled = containerSurfaceConfig.isEnabled();
            log.info("1. 功能启用检查: {}", enabled ? "✅ 已启用" : "❌ 未启用");
            if (!enabled) {
                log.info("=== 相面识别判断结束 === 结果: 功能未启用，跳过");
                return;
            }

            // 2. 高度数据检查
            boolean heightValid = height != null && height != 999.0f && height > 0;
            log.info("2. 高度数据检查: {} (相面高度:{})", heightValid ? "✅ 有效" : "❌ 无效", height);
            if (!heightValid) {
                log.info("=== 相面识别判断结束 === 结果: 高度数据无效，跳过");
                return;
            }

            // 3. 高度范围检查
            float minHeight = containerSurfaceConfig.getRecognition().getMinHeight();
            float maxHeight = containerSurfaceConfig.getRecognition().getMaxHeight();
            boolean heightInRange = height >= minHeight && height <= maxHeight;
            log.info("3. 高度范围检查: {} (相面高度:{}米, 范围:{}~{}米)",
                    heightInRange ? "✅ 在范围内" : "❌ 超出范围", height, minHeight, maxHeight);

            // 4. 一次性启动保护检查
            boolean notStarted = !containerSurfaceStartedInCurrentCycle;
            log.info("4. 一次性启动检查: {} (当前周期{}启动过)",
                    notStarted ? "✅ 可以启动" : "❌ 已启动过", containerSurfaceStartedInCurrentCycle ? "已" : "未");

            // 5. 运动方向检查
            boolean upwardMovement = checkUpwardMovement(plc);
            log.info("5. 上升运动检查: {}", upwardMovement ? "✅ 正在上升" : "❌ 未上升");

            // 6. 闭锁状态检查
            boolean lockCondition = lock != null && lock == 1;
            log.info("6. 闭锁状态检查: {} (锁状态:{})",
                    lockCondition ? "✅ 已闭锁" : "❌ 未闭锁", lock);

            // 7. 任务状态检查
            boolean taskCondition = taskStatus != null && taskStatus == 1;
            log.info("7. 任务状态检查: {} (任务状态:{})",
                    taskCondition ? "✅ 有任务" : "❌ 无任务", taskStatus);

            // 8. 作业类型检查
            boolean workCondition = workType != null && workType >= 1;
            log.info("8. 作业类型检查: {} (作业类型:{})",
                    workCondition ? "✅ 符合条件" : "❌ 不符合", workType);

            // 综合判断结果
            boolean finalResult = heightInRange && notStarted && upwardMovement &&
                                lockCondition && taskCondition && workCondition;

            log.info("=== 相面识别判断结束 === 最终结果: {} {}",
                    finalResult ? "✅ 启动相面识别" : "❌ 不启动",
                    finalResult ? "" : "- 条件不满足");

        } catch (Exception e) {
            log.error("相面识别判断日志记录异常", e);
        }
    }

    /**
     * 检查吊具上升运动方向（仅上升时启动箱面识别）
     * @param plc PLC数据
     * @return true-吊具正在上升，false-吊具正在下降或静止
     */
    private boolean checkUpwardMovement(Plc plc) {
        // 如果没有历史数据，无法判断方向，默认不启动
        if (lastPlc == null || lastPlc.getSpreaderFromContainerHeight() == null ||
            plc.getSpreaderFromContainerHeight() == null) {
            log.info("   上升运动详情 - 缺少历史相面高度数据，无法判断运动方向");
            return false;
        }

        float lastHeight = lastPlc.getSpreaderFromContainerHeight();
        float currentHeight = plc.getSpreaderFromContainerHeight();

        // 排除无效数据
        if (lastHeight == 999.0f || currentHeight == 999.0f ||
            lastHeight <= 0 || currentHeight <= 0) {
            log.info("   上升运动详情 - 高度数据无效(上次:{}, 当前:{})", lastHeight, currentHeight);
            return false;
        }

        // 计算高度变化
        float heightChange = currentHeight - lastHeight;

        // 设置运动阈值，避免微小波动的影响
        float movementThreshold = 0.1f; // 10cm阈值

        boolean isUpward = heightChange > movementThreshold;

        log.info("   上升运动详情 - 上次高度:{}米, 当前高度:{}米, 高度变化:{}米, 阈值:{}米, 判断:{}",
                lastHeight, currentHeight, heightChange, movementThreshold, isUpward ? "上升" : "下降/静止");

        return isUpward;
    }

    /**
     * 启动箱面识别
     * @param plc PLC数据
     */
    private void startContainerSurfaceRecognition(Plc plc) {
        try {
            // 标记当前周期已启动
            containerSurfaceStartedInCurrentCycle = true;
            lastHeightWhenContainerSurfaceStarted = plc.getSpreaderFromContainerHeight();

            log.info("🚀 开始箱面识别 - 相面高度:{}米, 车道:{}, 上报状态:{}",
                    plc.getSpreaderFromContainerHeight(), plc.getLane(),
                    containerSurfaceReportedInCurrentCycle ? "已上报" : "未上报");

            // 异步执行箱面识别任务
            String taskDescription = String.format("箱面识别任务 - 相面高度%.1f米", plc.getSpreaderFromContainerHeight());
            threadPoolManager.submitCommonTask(() -> {
                performContainerSurfaceRecognition(plc);
            }, taskDescription);

        } catch (Exception e) {
            log.error("箱面识别启动失败", e);
            // 启动失败时重置标志，允许重试
            containerSurfaceStartedInCurrentCycle = false;
            lastHeightWhenContainerSurfaceStarted = null;
            containerSurfaceReportedInCurrentCycle = false; // 重置ECS上报标志
        }
    }

    /**
     * 执行箱面识别
     * @param plc PLC数据
     */
    private void performContainerSurfaceRecognition(Plc plc) {
        try {
            log.info("🔄 执行箱门朝向识别... - 当前上报状态: {}",
                    containerSurfaceReportedInCurrentCycle ? "已上报" : "未上报");

            // 设置任务超时控制（防止线程池阻塞）
            Future<Integer> future = CompletableFuture.supplyAsync(() -> {
                return containerSurfaceService.recognizeDoorOrientation(plc);
            });

            Integer doorOrientation;
            try {
                // 10秒超时保护（延长超时时间）
                doorOrientation = future.get(10, TimeUnit.SECONDS);
            } catch (TimeoutException e) {
                log.warn("箱门识别任务超时（10秒），默认朝向右");
                future.cancel(true);
                doorOrientation = 1; // 超时默认朝右
            }

            // 检查是否已经上报过ECS（避免重复上报）
            if (containerSurfaceReportedInCurrentCycle) {
                log.info("⚠️ 箱门朝向识别完成，但当前周期已上报过ECS，跳过重复上报");
                return; // 直接返回，不执行后续逻辑
            }

            // 根据识别结果决定是否上报ECS
            if (doorOrientation == null) {
                log.error("箱门朝向识别返回null，异常情况");
                return;
            }

            switch (doorOrientation) {
                case 0: // 识别成功，朝向左
                case 1: // 识别成功，朝向右
                    this.ctnDoorOrientation = doorOrientation;
                    this.identifyType = 4; // 箱门朝向识别类型

                    String orientationDesc = doorOrientation == 0 ? "朝向左" : "朝向右";
                    log.info("✅ 箱门朝向识别成功 - 朝向: {} ({})，准备上报ECS",
                            doorOrientation, orientationDesc);

                    // 上报ECS
                    boolean reportSuccess = containerSurfaceService.reportToECS(plc, doorOrientation, identifyType);
                    if (reportSuccess) {
                        log.info("ECS上报成功 - 朝向: {}", orientationDesc);
                        // 标记当前周期已上报
                        containerSurfaceReportedInCurrentCycle = true;
                    } else {
                        log.error("ECS上报失败 - 朝向: {}", orientationDesc);
                    }
                    break;

                case 2: // 识别失败，继续尝试
                    log.info("❌ 箱门朝向识别失败，继续尝试...");
                    // 不上报ECS，不重置周期，允许继续识别
                    return;

                case 3: // 超时，默认朝向右
                    this.ctnDoorOrientation = 1; // 默认朝向右
                    this.identifyType = 4;

                    log.info("⏰ 箱门朝向识别超时，默认朝向右，准备上报ECS");

                    // 上报ECS
                    boolean timeoutReportSuccess = containerSurfaceService.reportToECS(plc, 1, identifyType);
                    if (timeoutReportSuccess) {
                        log.info("ECS上报成功 - 朝向: 朝向右（超时默认）");
                        // 标记当前周期已上报
                        containerSurfaceReportedInCurrentCycle = true;
                    } else {
                        log.error("ECS上报失败 - 朝向: 朝向右（超时默认）");
                    }
                    break;

                default:
                    log.error("未知的识别结果: {}", doorOrientation);
                    break;
            }

            // 重置周期标志
            resetContainerSurfaceCycle();

        } catch (Exception e) {
            log.error("箱面识别执行异常", e);
            resetContainerSurfaceCycle();
        }
    }



    /**
     * 检查并重置箱面识别周期
     * 当吊具下降到低位时，重置启动标志，准备下一次上升识别
     * @param plc PLC数据
     */
    private void checkAndResetContainerSurfaceCycle(Plc plc) {
        if (plc.getSpreaderFromContainerHeight() == null) {
            return;
        }

        float currentHeight = plc.getSpreaderFromContainerHeight();

        // 排除无效数据
        if (currentHeight == 999.0f || currentHeight <= 0) {
            return;
        }

        // 重置条件：吊具下降到低位时重置（低于最小高度-1米）
        // 因为是上升触发，所以当吊具下降到低位时，说明本次作业周期结束，可以重置
        float minHeight = containerSurfaceConfig.getRecognition().getMinHeight();
        float resetLowHeight = minHeight - 1.0f;   // 8.5米

        if (containerSurfaceStartedInCurrentCycle && currentHeight < resetLowHeight) {
            containerSurfaceStartedInCurrentCycle = false;
            lastHeightWhenContainerSurfaceStarted = null;
            containerSurfaceReportedInCurrentCycle = false; // 重置ECS上报标志

            log.info("🔄 箱面识别周期已自动重置 - 吊具下降到低位(%.1f米 < %.1f米) - 准备下一次上升识别",
                    currentHeight, resetLowHeight);
        }
    }

    /**
     * 重置箱面识别周期
     */
    private void resetContainerSurfaceCycle() {
        containerSurfaceStartedInCurrentCycle = false;
        lastHeightWhenContainerSurfaceStarted = null;
        containerSurfaceReportedInCurrentCycle = false; // 重置ECS上报标志
        log.debug("箱面识别周期已重置");
    }


    public int getLockType(Plc plc) {
        if (lastPlc == null||lastPlc.getLock() == 1) {
            if (plc.getLock() == 1) {
                log.debug("PLC状态:上锁-->上锁 或 未知-->上锁");
                return 1;
            } else {
                log.debug("PLC状态:上锁-->解锁 或 未知-->解锁");
                return 2;
            }
        }
        if (lastPlc.getLock() == 0) {
            if (plc.getLock() == 1) {
                log.debug("PLC状态:解锁-->上锁");
                return 3;
            } else {
                log.debug("PLC状态:解锁-->解锁");
                return 4;
            }
        }
        return 0;
    }

    /**
     * 获取线程池状态（用于监控和调试）
     */
    public String getThreadPoolStatus() {
        return threadPoolManager.getPlcPoolStatus();
    }

}
