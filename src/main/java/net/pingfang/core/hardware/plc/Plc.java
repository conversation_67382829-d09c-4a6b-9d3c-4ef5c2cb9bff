package net.pingfang.core.hardware.plc;

import lombok.Data;

/**
 * Plc对象
 *
 * @author: CM
 * @date: 2023/5/31
 */
@Data
public class Plc {

    /**
     * plcX值(null-未知)
     */
    private int plcX;

    /**
     * plcY值(null-未知)
     */
    private int plcY;

    /**
     * 作业高度
     */
    private Float height;

    /**
     * 重量(null-未知)
     */
    private Integer weight;

    /**
     * 开闭锁状态(0-开锁,1-闭锁,null-未知)
     */
    private Integer lock;

    /**
     * 预置位类型
     */
    private Integer presetType;

    /**
     * 箱型(0-单吊长箱  1-单吊短箱   2-孖吊 两个20尺  3-双吊 两个40尺  4-三箱吊(岸侧两个20尺,海侧一个40尺) 5-三箱吊(岸侧一个40尺,海侧两个20尺) 6-四箱吊 7/null-未知)
     */
    private Integer containerType;

    /**
     * 大车移动方向((null-未知),0-未移动,1-向左,2-向右)
     */
    private Integer moveDir;

    /**
     * 海陆侧(null-未知, 0-陆侧, 1-海侧)
     */
    private Integer seaLand;
    /**
     * 当前所属车道
     */
    private String lane;

    /**
     * 数据推送时间 - 8字节
     */
    private Long pushTime;

    /**
     * 吊具上下伸速度 - 4字节 Float
     */
    private Float hoistSpeed;

    /**
     * 大车指令输出速度 - 4字节 Float
     */
    private Float gantryCommandSpeed;

    /**
     * 小车速度 - 4字节 Float
     */
    private Float trolleySpeed;

    /**
     * 电气房侧大车速度 - 4字节 Float
     */
    private Float gantryElectricalSpeed;

    /**
     * 任务状态 - 2字节 Short (0-无任务正在作业 1-任务正在作业中)
     */
    private Short taskStatus;

    /**
     * 作业类型 - 2字节 Short
     * 0-未知 1-列车至堆场 2-集卡至堆场 3-列车至集卡 4-堆场至列车
     * 5-堆场至集卡 6-集卡至列车 7-堆场内倒箱 8-列车至列车
     * 9-堆场定位 10-火车定位 11-火车车头定位
     */
    private Short workType;

    /**
     * 吊具距离箱面高度 - 4字节 Float
     * 如吊具未到达任务排上方，值为999，单位m
     */
    private Float spreaderFromContainerHeight;

    /**
     * 小车位置起点 - 4字节 Float
     */
    private Float trolleyStartPosition;

    /**
     * 大车位置起点 - 4字节 Float
     */
    private Float gantryStartPosition;

    /**
     * 吊具位置起点 - 4字节 Float
     */
    private Float hoistStartPosition;

}
