package net.pingfang.core.hardware.rtsp;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.config.ProjectConfig;
import net.pingfang.util.DateUtil;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.javacv.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.io.File;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * RTSP视频录制服务 - 720P录制
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Slf4j
@Service
public class RtspVideoRecorder {

    @Autowired
    private ProjectConfig projectConfig;

    // 线程池
    private ExecutorService executor = Executors.newCachedThreadPool();

    // 录制任务管理：cameraId -> Future
    private ConcurrentHashMap<Integer, Future<?>> recordingTasks = new ConcurrentHashMap<>();

    // 录制器管理：cameraId -> FFmpegFrameRecorder
    private ConcurrentHashMap<Integer, FFmpegFrameRecorder> recorders = new ConcurrentHashMap<>();

    // 抓取器管理：cameraId -> FFmpegFrameGrabber
    private ConcurrentHashMap<Integer, FFmpegFrameGrabber> grabbers = new ConcurrentHashMap<>();

    // 录制状态管理
    private volatile boolean isRecording = false;

    // 关闭钩子，确保异常退出时也能正确关闭录制
    private Thread shutdownHook;

    /**
     * 开始录制所有相机的720P视频
     *
     * @param seqNo        序列号
     * @param urlList      RTSP URL列表
     * @param cameraIdList 相机ID列表
     */
    public synchronized void startRecording(String seqNo, java.util.List<String> urlList, java.util.List<Integer> cameraIdList) {
        if (isRecording) {
            log.warn("视频录制已在进行中，跳过重复启动");
            return;
        }

        if (urlList == null || cameraIdList == null || urlList.size() != cameraIdList.size()) {
            log.error("RTSP URL列表和相机ID列表不匹配，无法开始录制");
            return;
        }

        isRecording = true;
        log.info("开始录制720P RTSP视频 - seqNo: {}, 相机数量: {}", seqNo, cameraIdList.size());

        // 注册JVM关闭钩子，确保异常退出时也能正确关闭录制
        registerShutdownHook();

        for (int i = 0; i < urlList.size(); i++) {
            String rtspUrl = urlList.get(i);
            Integer cameraId = cameraIdList.get(i);

            Future<?> task = executor.submit(() -> recordVideo(seqNo, rtspUrl, cameraId));
            recordingTasks.put(cameraId, task);
        }
    }

    /**
     * 停止录制所有视频
     */
    public synchronized void stopRecording() {
        if (!isRecording) {
            log.debug("当前没有进行视频录制");
            return;
        }

        log.info("停止720P RTSP视频录制");
        isRecording = false;

        // 停止所有录制任务
        recordingTasks.forEach((cameraId, task) -> {
            if (task != null && !task.isDone()) {
                task.cancel(true);
                log.debug("取消相机{}的录制任务", cameraId);
            }
        });

        // 关闭所有录制器
        recorders.forEach((cameraId, recorder) -> {
            try {
                if (recorder != null) {
                    recorder.stop();
                    recorder.release();
                    log.debug("关闭相机{}的录制器", cameraId);
                }
            } catch (Exception e) {
                log.warn("关闭相机{}录制器时发生异常", cameraId, e);
            }
        });

        // 关闭所有抓取器
        grabbers.forEach((cameraId, grabber) -> {
            try {
                if (grabber != null) {
                    grabber.stop();
                    grabber.release();
                    log.debug("关闭相机{}的抓取器", cameraId);
                }
            } catch (Exception e) {
                log.warn("关闭相机{}抓取器时发生异常", cameraId, e);
            }
        });

        // 清理资源
        recordingTasks.clear();
        recorders.clear();
        grabbers.clear();

        // 移除关闭钩子
        removeShutdownHook();

        log.info("720P RTSP视频录制已停止");
    }

    /**
     * 录制单个相机的720P视频
     *
     * @param seqNo    序列号
     * @param rtspUrl  RTSP URL
     * @param cameraId 相机ID
     */
    private void recordVideo(String seqNo, String rtspUrl, Integer cameraId) {
        FFmpegFrameGrabber grabber = null;
        FFmpegFrameRecorder recorder = null;

        try {
            // 创建输出文件路径
            String dateStr = DateUtil.getDate(DateUtil.TYPE_EIGHT);
            String outputDir = projectConfig.getFileDirPath() + "rtsp/" + dateStr + "/" + seqNo + "/" + cameraId + "/";
            File dir = new File(outputDir);
            if (!dir.exists()) {
                dir.mkdirs();
            }

            String outputFile = outputDir + "video_720p_" + cameraId + "_" + seqNo + ".mp4";
            log.info("开始录制相机{}的720P视频到: {}", cameraId, outputFile);

            // 初始化抓取器
            grabber = new FFmpegFrameGrabber(rtspUrl);
            grabber.setOption("rtsp_transport", "tcp");
            grabber.setOption("rw_timeout", "5000000");
            grabber.setOption("stimeout", "3000000");
            // 设置720P分辨率
            grabber.setImageWidth(1280);
            grabber.setImageHeight(720);
            grabber.start();

            // 初始化录制器 - 720P配置
            recorder = new FFmpegFrameRecorder(outputFile, 1280, 720);
            recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
            recorder.setFormat("mp4");
            recorder.setFrameRate(25); // 25fps
            recorder.setVideoBitrate(2000000); // 2Mbps，适合720P
            recorder.setVideoQuality(0); // 最高质量
            recorder.start();

            // 保存到管理器中
            grabbers.put(cameraId, grabber);
            recorders.put(cameraId, recorder);

            // 开始录制循环
            Frame frame;
            long startTime = System.currentTimeMillis();
            int frameCount = 0;

            while (isRecording && !Thread.currentThread().isInterrupted()) {
                try {
                    frame = grabber.grab();
                    if (frame != null) {
                        recorder.record(frame);
                        frameCount++;

                        // 每1000帧记录一次日志
                        if (frameCount % 1000 == 0) {
                            long elapsed = System.currentTimeMillis() - startTime;
                            log.debug("相机{}已录制{}帧720P视频，耗时{}ms", cameraId, frameCount, elapsed);
                        }
                    }
                } catch (Exception e) {
                    if (isRecording) {
                        log.warn("相机{}录制720P视频过程中发生异常", cameraId, e);
                        break;
                    }
                }
            }

            long totalTime = System.currentTimeMillis() - startTime;
            log.info("相机{}录制720P视频完成 - 总帧数:{}, 总时长:{}ms, 文件:{}", 
                    cameraId, frameCount, totalTime, outputFile);

        } catch (Exception e) {
            log.error("相机{}录制720P视频失败", cameraId, e);
        } finally {
            // 清理资源 - 确保录制器正确关闭以保证文件完整性
            try {
                if (recorder != null) {
                    log.debug("正在关闭相机{}的720P录制器...", cameraId);
                    recorder.stop();
                    recorder.release();
                    log.debug("相机{}的720P录制器已关闭", cameraId);
                }
            } catch (Exception e) {
                log.error("关闭相机{}录制器时发生异常，可能导致视频文件损坏", cameraId, e);
            }

            try {
                if (grabber != null) {
                    log.debug("正在关闭相机{}的抓取器...", cameraId);
                    grabber.stop();
                    grabber.release();
                    log.debug("相机{}抓取器已关闭", cameraId);
                }
            } catch (Exception e) {
                log.error("关闭相机{}抓取器时发生异常", cameraId, e);
            }

            // 从管理器中移除
            recorders.remove(cameraId);
            grabbers.remove(cameraId);
            recordingTasks.remove(cameraId);
        }
    }

    /**
     * 注册JVM关闭钩子
     */
    private void registerShutdownHook() {
        if (shutdownHook == null) {
            shutdownHook = new Thread(() -> {
                log.warn("检测到JVM即将关闭，正在紧急停止720P视频录制...");
                emergencyStopRecording();
            }, "RtspVideoRecorder-ShutdownHook");

            Runtime.getRuntime().addShutdownHook(shutdownHook);
            log.debug("已注册720P视频录制关闭钩子");
        }
    }

    /**
     * 移除JVM关闭钩子
     */
    private void removeShutdownHook() {
        if (shutdownHook != null) {
            try {
                Runtime.getRuntime().removeShutdownHook(shutdownHook);
                log.debug("已移除720P视频录制关闭钩子");
            } catch (IllegalStateException e) {
                // JVM已经在关闭过程中，忽略异常
                log.debug("JVM正在关闭，无法移除关闭钩子");
            } finally {
                shutdownHook = null;
            }
        }
    }

    /**
     * 紧急停止录制（用于关闭钩子）
     */
    private void emergencyStopRecording() {
        if (!isRecording) {
            return;
        }

        log.warn("紧急停止720P RTSP视频录制");
        isRecording = false;

        // 强制停止所有录制任务
        recordingTasks.forEach((cameraId, task) -> {
            if (task != null && !task.isDone()) {
                task.cancel(true);
                log.debug("紧急取消相机{}的录制任务", cameraId);
            }
        });

        // 紧急关闭所有录制器（确保文件完整性）
        recorders.forEach((cameraId, recorder) -> {
            try {
                if (recorder != null) {
                    log.debug("紧急关闭相机{}的录制器", cameraId);
                    recorder.stop();
                    recorder.release();
                }
            } catch (Exception e) {
                log.error("紧急关闭相机{}录制器时发生异常", cameraId, e);
            }
        });

        // 紧急关闭所有抓取器
        grabbers.forEach((cameraId, grabber) -> {
            try {
                if (grabber != null) {
                    log.debug("紧急关闭相机{}的抓取器", cameraId);
                    grabber.stop();
                    grabber.release();
                }
            } catch (Exception e) {
                log.error("紧急关闭相机{}抓取器时发生异常", cameraId, e);
            }
        });

        log.warn("紧急停止720P视频录制完成");
    }

    /**
     * 检查是否正在录制
     */
    public boolean isRecording() {
        return isRecording;
    }

    /**
     * 获取当前录制的相机数量
     */
    public int getRecordingCameraCount() {
        return recordingTasks.size();
    }

    /**
     * Spring Bean销毁时调用，确保资源正确释放
     */
    @PreDestroy
    public void destroy() {
        log.info("RtspVideoRecorder正在销毁，停止所有720P录制...");
        if (isRecording) {
            stopRecording();
        }
        
        // 关闭线程池
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        log.info("RtspVideoRecorder销毁完成");
    }
}
