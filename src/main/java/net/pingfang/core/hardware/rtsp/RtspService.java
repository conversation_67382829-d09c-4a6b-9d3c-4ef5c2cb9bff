package net.pingfang.core.hardware.rtsp;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.pingfang.config.ProjectConfig;
import net.pingfang.config.RtspConfig;
import net.pingfang.config.RtspVideoConfig;
import net.pingfang.core.algorithm.ailib.ContainerAlgorithmService;
import net.pingfang.core.algorithm.ailib.TopPlateAlgorithmService;
import net.pingfang.core.algorithm.ailib.model.DetectResult;
import net.pingfang.core.algorithm.ailib.model.DetectResults;
import net.pingfang.core.algorithm.ailib.model.RecContainer;
import net.pingfang.core.algorithm.ailib.model.RecTopPlate;

import net.pingfang.service.EcsService;
import net.pingfang.core.hardware.plc.PlcCoreService;
import net.pingfang.core.hardware.plc.Plc;
import net.pingfang.core.hardware.camera.hikvision.HikvisionService;
import net.pingfang.core.hardware.camera.hikvision.HikvisionServiceFactory;
import net.pingfang.model.dto.EcsIdentifyInfoDTO;
import net.pingfang.service.RtspPreRecognitionService;
import net.pingfang.model.vo.business.RecognizeConfigVO;
import net.pingfang.repository.RecognizeConfigRepository;
import net.pingfang.enums.RecognizeConfigTypeEnum;
import net.pingfang.enums.SafeWorkTypeEnum;
import org.springframework.util.StringUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import net.pingfang.util.DateUtil;
import net.pingfang.util.PathConfigUtil;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.bytedeco.javacv.Java2DFrameConverter;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import net.pingfang.service.ThreadPoolManagerService;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.Instant;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.HashMap;
import java.util.Set;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * rtsp视频流识别箱号流程
 *
 * @title: RtspService
 * @author: cb
 * @date: 2025-05-28 16:45
 * @version: 1.0
 */
@Component
@Slf4j
@Data
public class RtspService {

    @Resource
    private RtspConfig rtspConfig;

    @Resource
    private ProjectConfig projectConfig;

    @Resource
    private PathConfigUtil pathConfigUtil;

    /**
     * 箱号识别对接service，需要更换为支持图片处理的箱号识别算法对接service
     */
    @Resource
    private ContainerAlgorithmService containerAlgorithmService;

    @Resource
    private EcsService ecsService;

    @Resource
    private RtspVideoRecorder videoRecorder;

    @Resource
    private RtspVideoConfig videoConfig;

    @Resource
    private PlcCoreService plcCoreService;

    @Resource
    private TopPlateAlgorithmService topPlateAlgorithmService;

    @Resource
    private HikvisionServiceFactory hikvisionServiceFactory;

    @Resource
    private RecognizeConfigRepository recognizeConfigRepository;

    @Resource
    private RtspPreRecognitionService preRecognitionService;

    @Resource
    private ThreadPoolManagerService threadPoolManager;

    public static volatile Boolean isCapturing = false;

    public static volatile Boolean preEnd = false;

    //缓存每个相机的上一个识别数据
    private ConcurrentHashMap<Integer, EcsIdentifyInfoDTO> identifyInfoDTOMap = new ConcurrentHashMap<>();

    // 当前使用的seqNo，用于路径管理
    private volatile String currentSeqNo = null;

    // 标记是否已经生成了seqNo
    private volatile boolean seqNoGenerated = false;

    // RTSP最佳结果缓存：seqNo -> cameraId -> 最佳结果
    private ConcurrentHashMap<String, Map<Integer, RtspBestResult>> seqNoBestResults = new ConcurrentHashMap<>();

    // 智能结束相关数据结构
    // 记录每个seqNo的开始时间
    private final Map<String, Long> seqNoStartTimes = new ConcurrentHashMap<>();

    // 记录每个seqNo下各相机的连续识别结果
    private final Map<String, Map<Integer, List<String>>> seqNoCameraResults = new ConcurrentHashMap<>();

    // 防重复推送标记：记录已推送到ECS的seqNo
    private final Set<String> pushedSeqNos = ConcurrentHashMap.newKeySet();

    // 相机连接状态跟踪
    private final Set<Integer> connectedCameras = ConcurrentHashMap.newKeySet(); // 已连接的相机
    private final Set<Integer> allCameraIds = new java.util.HashSet<>(); // 所有相机ID

    // 当前结束原因跟踪
    private volatile String currentEndReason = "";

    public synchronized String start() {
        // 检查是否已经在运行中
        if (isCapturing) {
            log.info("RTSP服务已经在运行中，跳过重复启动");
            return "already_running";
        }

        // 确保状态完全清理（额外保护）
        connectedCameras.clear();
        allCameraIds.clear();
        log.debug("启动前状态清理完成");

        preEnd = false;
        // 生成新的seqNo
        currentSeqNo = generateSeqNo();
        seqNoGenerated = true;

        log.info("RTSP预识别开始 - 生成seqNo: {}", currentSeqNo);

        // 记录预识别开始时间
        if (currentSeqNo != null) {
            seqNoStartTimes.put(currentSeqNo, System.currentTimeMillis());
            log.info("预识别开始计时 - seqNo:{}, 最大时长:{}秒", currentSeqNo, rtspConfig.getEnd().getTimeout().getMaxSeconds());
        }

        // 检查是否需要进行车顶号抓拍
        checkAndStartTruckTopCapture();

        // 初始化相机连接状态跟踪
        allCameraIds.clear();
        connectedCameras.clear();
        allCameraIds.addAll(rtspConfig.getCameraIDList());
        log.info("初始化相机连接状态跟踪 - 总相机数:{}, 相机列表:{}", allCameraIds.size(), allCameraIds);

        log.info("检查RTSP配置 - urlList: {}, cameraIDList: {}",
                rtspConfig.getUrlList(), rtspConfig.getCameraIDList());

        // 验证IP到cameraID的映射
        validateCameraIdMapping();

        if (!CollectionUtils.isEmpty(rtspConfig.getUrlList())) {
            log.info("Rtsp视频流识别开始抓图！");
            isCapturing = true;

//            // 启动720P视频录制（如果启用）
//            if (videoConfig.getRecording().isEnabled()) {
//                try {
//                    videoRecorder.startRecording(currentSeqNo, rtspConfig.getUrlList(), rtspConfig.getCameraIDList());
//                    log.info("720P RTSP视频录制已启动 - seqNo: {}", currentSeqNo);
//                } catch (Exception e) {
//                    log.error("启动720P RTSP视频录制失败", e);
//                }
//            } else {
//                log.debug("720P RTSP视频录制功能已禁用");
//            }

            log.info("🚀 开始启动图片抓拍任务 - 相机数量: {}", rtspConfig.getUrlList().size());
            for (int i = 0; i < rtspConfig.getUrlList().size(); i++) {
                int index = i;
                // 直接使用生成的seqNo作为路径
                String taskIdForPath = currentSeqNo;
                String path = this.projectConfig.getFileDirPath() + "rtsp" + "/" + DateUtil.getDate(DateUtil.TYPE_EIGHT) + "/" + taskIdForPath + "/" + rtspConfig.getCameraIDList().get(index) + "/";
                String url = rtspConfig.getUrlList().get(index);
                Integer cameraId = rtspConfig.getCameraIDList().get(index);

                log.info("📋 提交相机{}抓拍任务 - URL:{}, 路径:{}", cameraId, url, path);

                String taskDescription = String.format("相机%d抓拍任务 - URL:%s", cameraId, url);
                threadPoolManager.submitRtspTask(() -> {
                    captureFrame(url, cameraId, path, rtspConfig.getFps());
                }, taskDescription);
            }
            log.info("所有图片抓拍任务已提交完成");
        } else {
            log.warn("未配置rtsp流地址！");
        }
        return "start";
    }



    public synchronized String end() {
        isCapturing = false;

        // 如果有当前seqNo，推送最佳结果到ECS
        if (currentSeqNo != null && seqNoGenerated) {
            String finalSeqNo = currentSeqNo;

            // 防重复推送检查
            if (pushedSeqNos.contains(finalSeqNo)) {
                log.info("RTSP预识别结束，seqNo:{}已推送过ECS，跳过重复推送", finalSeqNo);
            } else {
                log.info("RTSP预识别结束，准备推送最佳结果到ECS - seqNo:{}", finalSeqNo);

                // 标记为已推送，防止重复
                pushedSeqNos.add(finalSeqNo);

                String taskDescription = String.format("推送最佳结果到ECS - seqNo:%s", finalSeqNo);
                threadPoolManager.submitRtspTask(() -> {
                    try {
                        // 延迟2秒确保所有识别完成
                        Thread.sleep(2000);
                        log.info("开始推送RTSP最佳识别结果到ECS - seqNo:{}", finalSeqNo);
                        uploadBestResultsToEcs(finalSeqNo);
                    } catch (InterruptedException e) {
                        log.warn("推送最佳结果被中断 - seqNo:{}", finalSeqNo, e);
                        // 推送失败时移除标记，允许重试
                        pushedSeqNos.remove(finalSeqNo);
                    } catch (Exception e) {
                        log.error("推送最佳结果异常 - seqNo:{}", finalSeqNo, e);
                        // 推送失败时移除标记，允许重试
                        pushedSeqNos.remove(finalSeqNo);
                    }
                }, taskDescription);
            }
        } else {
            log.info("RTSP预识别结束，无seqNo或未生成，跳过ECS推送");
        }

        // 清理智能结束相关数据
        if (currentSeqNo != null) {
            seqNoStartTimes.remove(currentSeqNo);
            seqNoCameraResults.remove(currentSeqNo);
            // 延迟清理推送标记，给推送任务一些时间完成
            String finalSeqNoForCleanup = currentSeqNo;
            String taskDescription = String.format("延迟清理推送标记 - seqNo:%s", finalSeqNoForCleanup);
            threadPoolManager.submitRtspTask(() -> {
                try {
                    Thread.sleep(10000); // 延迟10秒
                    pushedSeqNos.remove(finalSeqNoForCleanup);
                    log.debug("已清理seqNo:{}的推送标记", finalSeqNoForCleanup);
                } catch (InterruptedException e) {
                    log.debug("清理推送标记被中断 - seqNo:{}", finalSeqNoForCleanup);
                }
            }, taskDescription);
            log.debug("已清理seqNo:{}的智能结束相关数据", currentSeqNo);
        }

        // 清理相机连接状态
        connectedCameras.clear();
        allCameraIds.clear();

        // 重置结束原因
        currentEndReason = "";

        log.debug("已清理相机连接状态跟踪数据");

        // 重置状态
        currentSeqNo = null;
        seqNoGenerated = false;

        log.info("RTSP预识别结束");
        return "end";
    }

    public void captureFrame(String rtspUrl, Integer cameraID, String initialOutputPath, Integer fps) {
        log.info("🎬 开始执行相机{}抓拍任务 - URL:{}, FPS:{}", cameraID, rtspUrl, fps);

        // 连接重试配置 - 增加重试次数以提高稳定性
        final int MAX_RETRY_COUNT = 5;  // 增加到5次重试
        final int RETRY_DELAY_SECONDS = 3;  // 减少重试间隔到3秒
        int retryCount = 0;

        FFmpegFrameGrabber grabber = null;

        // 确保输出目录存在
        File dir = new File(initialOutputPath);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        try {
            // 初始化并连接RTSP流（带重试机制）
            grabber = initializeAndConnectGrabber(rtspUrl, cameraID, fps, MAX_RETRY_COUNT, RETRY_DELAY_SECONDS);
            if (grabber == null) {
                log.error("❌ 相机{}连接失败，已达到最大重试次数，退出任务", cameraID);
                return;
            }

            // 标记相机连接成功
            markCameraConnected(cameraID);
            log.info("✅ 相机{}连接成功，开始持续抓拍", cameraID);

            // 主抓拍循环
            performContinuousCapture(grabber, cameraID, initialOutputPath, fps);

        } catch (Exception e) {
            log.error("❌ 相机{}抓拍任务异常", cameraID, e);
        } finally {
            // 确保资源正确释放
            closeGrabberSafely(grabber, cameraID);
        }

        log.info("🏁 相机{}抓拍任务完全结束", cameraID);
    }

    /**
     * 初始化并连接RTSP抓取器（带重试机制）
     */
    private FFmpegFrameGrabber initializeAndConnectGrabber(String rtspUrl, Integer cameraID, Integer fps,
                                                          int maxRetryCount, int retryDelaySeconds) {
        FFmpegFrameGrabber grabber = null;
        int retryCount = 0;

        while (retryCount <= maxRetryCount && isCapturing) {
            try {
                log.info("🔗 尝试连接RTSP流 - 相机ID:{}, 尝试次数:{}/{}, URL:{}",
                        cameraID, retryCount + 1, maxRetryCount + 1, rtspUrl);

                // 创建并配置抓取器
                grabber = FFmpegFrameGrabber.createDefault(rtspUrl);
                configureGrabber(grabber, fps);

                // 尝试连接
                grabber.start();

                log.info("✅ RTSP连接成功 - 相机ID:{}, 分辨率:{}x{}",
                        cameraID, grabber.getImageWidth(), grabber.getImageHeight());
                return grabber;

            } catch (FFmpegFrameGrabber.Exception e) {
                retryCount++;
                log.error("❌ 相机{}连接失败 - 尝试次数:{}/{}, 错误:{}",
                        cameraID, retryCount, maxRetryCount + 1, e.getMessage());

                // 清理失败的grabber
                if (grabber != null) {
                    try {
                        grabber.close();
                    } catch (Exception closeEx) {
                        log.debug("关闭失败的grabber时出错", closeEx);
                    }
                    grabber = null;
                }

                // 如果还有重试机会且未停止，则等待后重试
                if (retryCount <= maxRetryCount && isCapturing) {
                    try {
                        log.info("⏳ 相机{}等待{}秒后重试...", cameraID, retryDelaySeconds);
                        Thread.sleep(retryDelaySeconds * 1000);
                    } catch (InterruptedException ie) {
                        log.warn("⚠️ 相机{}重试等待被中断", cameraID);
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        return null; // 连接失败
    }

    /**
     * 配置RTSP抓取器参数
     */
    private void configureGrabber(FFmpegFrameGrabber grabber, Integer fps) throws FFmpegFrameGrabber.Exception {
        // 网络传输配置
        grabber.setOption("rtsp_transport", "tcp");     // 强制TCP传输，提高稳定性
        grabber.setOption("rw_timeout", "8000000");     // 读写超时8秒（减少等待时间）
        grabber.setOption("stimeout", "5000000");       // socket超时5秒
        grabber.setOption("max_delay", "2000000");      // 最大延迟2秒（减少延迟）

        // 缓冲区配置
        grabber.setOption("buffer_size", "1024000");    // 设置缓冲区大小1MB
        grabber.setOption("max_interleave_delta", "1000000"); // 最大交错增量1秒

        // 重连配置
        grabber.setOption("reconnect", "1");            // 启用自动重连
        grabber.setOption("reconnect_at_eof", "1");     // EOF时重连
        grabber.setOption("reconnect_streamed", "1");   // 流式重连

        // 分辨率和帧率配置
        grabber.setImageWidth(1280);                    // 设置分辨率
        grabber.setImageHeight(720);
        grabber.setFrameRate(fps);                      // 设置帧率

        log.debug("RTSP抓取器配置完成 - 分辨率:1280x720, 帧率:{}, TCP传输", fps);
    }
    /**
     * 执行持续抓拍
     */
    private void performContinuousCapture(FFmpegFrameGrabber grabber, Integer cameraID,
                                        String initialOutputPath, Integer fps) {
        long lastCaptureTime = System.currentTimeMillis();
        final long CAPTURE_INTERVAL_MS = 1000; // 每秒抓拍一次
        int consecutiveFailures = 0;
        final int MAX_CONSECUTIVE_FAILURES = 10; // 最大连续失败次数

        log.info("🎬 相机{}开始持续抓拍循环", cameraID);

        while (isCapturing) {
            try {
                // 控制抓拍频率
                long currentTime = System.currentTimeMillis();
                if (currentTime - lastCaptureTime < CAPTURE_INTERVAL_MS) {
                    Thread.sleep(100); // 短暂休眠，避免CPU占用过高
                    continue;
                }
                lastCaptureTime = currentTime;

                // 获取当前输出路径
                String currentOutputPath = getCurrentOutputPath(initialOutputPath, cameraID);

                // 抓拍并处理帧
                boolean success = captureAndProcessFrame(grabber, cameraID, currentOutputPath);

                if (success) {
                    consecutiveFailures = 0; // 重置失败计数
                } else {
                    consecutiveFailures++;
                    log.warn("⚠️ 相机{}抓拍失败，连续失败次数: {}/{}", cameraID, consecutiveFailures, MAX_CONSECUTIVE_FAILURES);

                    // 如果连续失败次数过多，可能是连接断开，退出循环
                    if (consecutiveFailures >= MAX_CONSECUTIVE_FAILURES) {
                        log.error("❌ 相机{}连续失败{}次，可能连接已断开，退出抓拍", cameraID, MAX_CONSECUTIVE_FAILURES);
                        break;
                    }
                }

            } catch (InterruptedException e) {
                log.warn("⚠️ 相机{}抓拍被中断", cameraID);
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                consecutiveFailures++;
                log.error("❌ 相机{}抓拍过程中出现异常: {}, 连续失败次数: {}/{}",
                         cameraID, e.getMessage(), consecutiveFailures, MAX_CONSECUTIVE_FAILURES);

                // 如果连续异常过多，退出循环
                if (consecutiveFailures >= MAX_CONSECUTIVE_FAILURES) {
                    log.error("❌ 相机{}连续异常{}次，退出抓拍", cameraID, MAX_CONSECUTIVE_FAILURES);
                    break;
                }

                // 出现异常时短暂休眠，避免快速重试
                try {
                    Thread.sleep(2000); // 增加到2秒
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        log.info("🏁 相机{}持续抓拍循环结束", cameraID);
    }

    /**
     * 获取当前输出路径
     */
    private String getCurrentOutputPath(String initialOutputPath, Integer cameraID) {
        if (seqNoGenerated && currentSeqNo != null) {
            String dateStr = DateUtil.getDate(DateUtil.TYPE_EIGHT);
            String currentOutputPath = this.projectConfig.getFileDirPath() + "rtsp" + "/" +
                                     dateStr + "/" + currentSeqNo + "/" + cameraID + "/";

            // 确保目录存在
            File currentDir = new File(currentOutputPath);
            if (!currentDir.exists()) {
                currentDir.mkdirs();
            }
            return currentOutputPath;
        }
        return initialOutputPath;
    }

    /**
     * 抓拍并处理单帧
     * @return true表示抓拍成功，false表示失败
     */
    private boolean captureAndProcessFrame(FFmpegFrameGrabber grabber, Integer cameraID, String outputPath) {
        Frame frame = null;
        try {
            // 抓取帧
            frame = grabber.grabImage();
            if (frame == null || frame.image == null || frame.image.length == 0) {
                log.debug("相机{}抓取到空帧，跳过", cameraID);
                return false; // 空帧视为失败
            }

            // 转换并处理图像
            try (Java2DFrameConverter converter = new Java2DFrameConverter()) {
                BufferedImage image = converter.getBufferedImage(frame);
                if (image != null) {
                    // 保存图片
                    String imagePath = outputPath + System.currentTimeMillis() + "_" + cameraID + ".jpg";
                    ImageIO.write(image, "jpg", new File(imagePath));
                    log.debug("📷 图片保存成功: {}", imagePath);

                    // 进行识别 - 添加异常保护确保识别失败不影响抓拍
                    try {
                        detectContainerFromMemory(image, cameraID, imagePath);
                        log.debug("🔍 相机{}识别处理完成", cameraID);
                    } catch (Exception recognitionEx) {
                        log.error("🔍 相机{}识别处理异常，但不影响抓拍继续: {}", cameraID, recognitionEx.getMessage());
                        // 识别异常不影响抓拍成功状态
                    }

                    return true; // 抓拍和保存成功
                } else {
                    log.warn("⚠️ 相机{}图像转换失败", cameraID);
                    return false;
                }
            }

        } catch (FFmpegFrameGrabber.Exception e) {
            log.error("📷 相机{}抓拍失败: {}", cameraID, e.getMessage());
            return false;
        } catch (IOException e) {
            log.error("💾 相机{}保存图片失败: {}", cameraID, e.getMessage());
            return false;
        } catch (Exception e) {
            log.error("🔄 相机{}图片处理失败: {}", cameraID, e.getMessage());
            return false;
        } finally {
            // 确保帧资源被释放
            if (frame != null) {
                try {
                    frame.close();
                } catch (Exception e) {
                    log.debug("关闭帧资源时出错", e);
                }
            }
        }
    }

    /**
     * 安全关闭抓取器
     */
    private void closeGrabberSafely(FFmpegFrameGrabber grabber, Integer cameraID) {
        if (grabber != null) {
            try {
                grabber.stop();
                grabber.close();
                log.info("🔒 相机{}抓取器已关闭", cameraID);
            } catch (Exception e) {
                log.warn("⚠️ 关闭相机{}抓取器时出错: {}", cameraID, e.getMessage());
            }
        }
    }

    /**
     * 生成seqNo
     */
    private String generateSeqNo() {
        return DateUtil.getDate(DateUtil.TYPE_FIVE); // yyyyMMddHHmmssSSS
    }

    /**
     * 更新最佳识别结果
     */
    private void updateBestResult(String seqNo, Integer cameraId, String imagePath,
                                 DetectResult detectResult, RecContainer recognitionResult) {
        float currentConfidence = recognitionResult.getTrust();

        seqNoBestResults.computeIfAbsent(seqNo, k -> new ConcurrentHashMap<>())
                       .compute(cameraId, (k, existing) -> {
                           if (existing == null || currentConfidence > existing.getConfidence()) {
                               // 删除旧图片文件
                               if (existing != null) {
                                   deleteImageFile(existing.getImagePath());
                                   log.debug("替换低质量图片 - seqNo:{}, 相机:{}, 旧可信度:{}, 新可信度:{}",
                                            seqNo, cameraId, existing.getConfidence(), currentConfidence);
                               }

                               log.info("更新最佳识别结果 - seqNo:{}, 相机:{}, 可信度:{}, 箱号:{}",
                                       seqNo, cameraId, currentConfidence, recognitionResult.getCtnNo());

                               RtspBestResult newResult = new RtspBestResult(seqNo, imagePath, detectResult,
                                                       recognitionResult, currentConfidence, cameraId,
                                                       System.currentTimeMillis(), 1);
                               return newResult;
                           } else {
                               // 删除当前低质量图片
                               deleteImageFile(imagePath);
                               log.debug("保留原有高质量图片 - seqNo:{}, 相机:{}, 原可信度:{}, 当前可信度:{}",
                                        seqNo, cameraId, existing.getConfidence(), currentConfidence);
                               return existing;
                           }
                       });
    }



    /**
     * 删除图片文件
     */
    private void deleteImageFile(String imagePath) {
        try {
            File file = new File(imagePath);
            if (file.exists() && file.delete()) {
                log.debug("删除图片文件: {}", imagePath);
            }
        } catch (Exception e) {
            log.warn("删除图片文件失败: {}", imagePath, e);
        }
    }

    /**
     * 上传最佳结果到ECS
     */
    public void uploadBestResultsToEcs(String seqNo) {
        log.info("🚀 开始执行ECS推送 - seqNo:{}", seqNo);

        // 添加调试信息：检查缓存状态
        log.info("📊 当前缓存状态 - seqNo:{}, 总缓存数:{}, 缓存的seqNo列表:{}",
                seqNo, seqNoBestResults.size(), seqNoBestResults.keySet());

        Map<Integer, RtspBestResult> cameraResults = seqNoBestResults.remove(seqNo);
        if (cameraResults == null) {
            log.warn("❌ RTSP预识别结束，seqNo:{}在缓存中不存在", seqNo);
            return;
        }

        if (cameraResults.isEmpty()) {
            log.warn("❌ RTSP预识别结束，seqNo:{}的缓存结果为空", seqNo);
            return;
        }

        log.info("RTSP预识别结束，开始选择最佳结果推送ECS - seqNo:{}, 相机结果数:{}", seqNo, cameraResults.size());

        // 打印所有相机的识别结果
        cameraResults.forEach((cameraId, result) -> {
            RecContainer container = result.getRecognitionResult();
            log.info("相机{}识别结果 - 箱号:{}, 可信度:{}, 校验通过:{}",
                    cameraId, container.getCtnNo(), container.getTrust(), container.getBcheck());
        });

        // 选择通过校验且可信度最高的单个结果
        RtspBestResult globalBest = cameraResults.values().stream()
                .filter(result -> result.getRecognitionResult().getBcheck()) // 必须通过校验
                .max(Comparator.comparing(result -> result.getRecognitionResult().getTrust())) // 选择可信度最高
                .orElse(null);

        // 如果没有通过校验的结果，选择可信度最高的结果
        if (globalBest == null) {
            log.warn("没有通过校验的识别结果，选择可信度最高的结果 - seqNo:{}", seqNo);
            globalBest = cameraResults.values().stream()
                    .max(Comparator.comparing(result -> result.getRecognitionResult().getTrust()))
                    .orElse(null);
        }

        if (globalBest != null) {
            try {
                RecContainer bestContainer = globalBest.getRecognitionResult();
                log.info("选择最佳识别结果 - seqNo:{}, 相机:{}, 箱号:{}, 可信度:{}, 校验通过:{}",
                        seqNo, globalBest.getCameraId(), bestContainer.getCtnNo(),
                        bestContainer.getTrust(), bestContainer.getBcheck());

                // 创建ECS上传DTO（包含图片URL）
                EcsIdentifyInfoDTO ecsDTO = createEcsDTO(globalBest);
                ecsService.sendIdentifyInfo(ecsDTO);

                log.info("✅ RTSP最佳识别结果已推送ECS - seqNo:{}, 相机:{}, 箱号:{}, 图片URL数:{}",
                        seqNo, globalBest.getCameraId(), bestContainer.getCtnNo(),
                        ecsDTO.getImgUrls().size());

                // 🆕 ECS推送成功后保存到数据库
                savePreRecognitionResultToDatabase(seqNo, globalBest, cameraResults);

                // ECS推送成功后立即停止抓拍
                stopCapturingImmediately("ECS推送成功");
            } catch (Exception e) {
                log.error("RTSP结果推送ECS失败 - seqNo:{}", seqNo, e);
            }
        } else {
            log.warn("RTSP预识别结束，未找到可用的识别结果 - seqNo:{}", seqNo);
        }
    }

    /**
     * 创建ECS上传DTO（包含图片URL）
     */
    private EcsIdentifyInfoDTO createEcsDTO(RtspBestResult bestResult) {
        RecContainer container = bestResult.getRecognitionResult();

        // 处理图片路径，转换为HTTP可访问的URL
        String actualFilePath = bestResult.getImagePath();
        if (actualFilePath.startsWith(this.projectConfig.getFileDirPath())) {
            actualFilePath = actualFilePath.substring(this.projectConfig.getFileDirPath().length());
        }
        // 确保路径分隔符正确
        actualFilePath = actualFilePath.replace("\\", "/");
        if (!actualFilePath.startsWith("/")) {
            actualFilePath = "/" + actualFilePath;
        }

        String iisImgPath = pathConfigUtil.getIisUrl() + actualFilePath;

        log.debug("图片路径转换 - 原路径:{}, 转换后URL:{}", bestResult.getImagePath(), iisImgPath);

        EcsIdentifyInfoDTO identifyInfoDTO = new EcsIdentifyInfoDTO();
        identifyInfoDTO.setCraneNo(pathConfigUtil.getRmgName());
        identifyInfoDTO.setIdentifyType(2); // 集装箱识别
        identifyInfoDTO.setIdentifyCtnNo(container.getCtnNo());
        identifyInfoDTO.setIdentifyCtnNoIso(container.getIso());

        // 设置图片URL列表
        List<String> imgUrls = new ArrayList<>();
        imgUrls.add(iisImgPath);
        identifyInfoDTO.setImgUrls(imgUrls);

        log.debug("ECS数据构建完成 - 箱号:{}, 图片URL:{}", container.getCtnNo(), iisImgPath);

        return identifyInfoDTO;
    }

    /**
     * 使用内存数据进行箱号检测和识别
     * @param bufferedImage 图片数据
     * @param cameraID 相机ID
     * @param filePath 保存的图片文件路径（用于结果记录和ECS上传）
     */
    void detectContainerFromMemory(BufferedImage bufferedImage, Integer cameraID, String filePath) {
        try {
            // 使用吊具摄像头专用接口进行检测（内存数据）
            DetectResults detectResults = null;
            try {
                detectResults = containerAlgorithmService.detPictureSpreader(filePath, cameraID);
            } catch (Exception e) {
                log.error("❌ 相机{}检测算法调用异常: {}, 图片: {}", cameraID, e.getMessage(), filePath);
                deleteImageFile(filePath);
                return;
            }

            if (detectResults == null || detectResults.det_num == 0) {
                // 删除无效图片
                log.debug("相机{}检测无结果，删除图片: {}", cameraID, filePath);
                deleteImageFile(filePath);
                return;
            }
        int totalResults = detectResults.det_num;


        // 使用lambda表达式过滤有效的检测结果
        List<DetectResult> detectResultList = Arrays.stream(detectResults.getDr())
                .filter(Objects::nonNull)  // 过滤null元素
                .filter(result -> result.getDet_fr() > 0)  // 过滤低可信度结果
//                .filter(result -> result.getDet_type() == 15)  // 只保留箱面类型15
                .collect(Collectors.toList());

        // 统计过滤结果
        int filteredByConfidence = (int) Arrays.stream(detectResults.getDr())
                .filter(Objects::nonNull)
                .filter(result -> result.getDet_fr() <= 0)
                .count();
        int filteredByType = (int) Arrays.stream(detectResults.getDr())
                .filter(Objects::nonNull)
                .filter(result -> result.getDet_fr() > 0)
//                .filter(result -> result.getDet_type() != 15)
                .count();
        int validResults = detectResultList.size();



        log.info("RTSP检测结果过滤完成 - 相机ID:{}, 总数:{}, 低可信度过滤:{}, 非箱面类型过滤:{}, 有效结果:{}",
                cameraID, totalResults, filteredByConfidence, filteredByType, validResults);

        // 过滤后如果没有合适的检测结果,那么就直接跳过
        if (CollectionUtils.isEmpty(detectResultList)) {
            log.warn("RTSP检测结果过滤后无有效结果 - 相机ID:{}, 图片:{}, 跳过后续识别", cameraID, filePath);
            // 删除无效图片
            deleteImageFile(filePath);
            return;
        }
        detectResults.setDet_num(detectResultList.size());
        detectResults.setDr(detectResultList.toArray(new DetectResult[]{}));

            // 使用吊具摄像头专用识别接口进行识别（内存数据）
            log.debug("使用吊具摄像头专用识别接口开始识别(内存) - 相机ID:{}", cameraID);
            List<RecContainer> containerList = null;
            try {
                containerList = containerAlgorithmService.recPictureSpreader(filePath, cameraID, detectResults);
            } catch (Exception e) {
                log.error("❌ 相机{}识别算法调用异常: {}, 图片: {}", cameraID, e.getMessage(), filePath);
                deleteImageFile(filePath);
                return;
            }

        // 如果没结果，删除图片并返回
        if (CollectionUtils.isEmpty(containerList)) {
            log.info("❌ 内存识别无结果，删除图片 - 相机ID:{}, 图片:{}", cameraID, filePath);
            deleteImageFile(filePath);
            return;
        }

        log.info("📋 内存识别获得结果 - 相机ID:{}, 结果数:{}", cameraID, containerList.size());
        // 打印所有识别结果
        for (int i = 0; i < containerList.size(); i++) {
            RecContainer container = containerList.get(i);
            log.info("  结果{}: 箱号:{}, 可信度:{}, 校验通过:{}",
                    i+1, container.getCtnNo(), container.getTrust(), container.getBcheck());
        }
        // 获取最佳识别结果
        RecContainer bestContainer = containerList.stream()
                .filter(RecContainer::getBcheck)
                .max(Comparator.comparing(RecContainer::getTrust))
                .orElse(null);

        if (bestContainer != null) {
            log.info("✅ RTSP识别到最佳箱号(内存) - 相机ID:{}, 箱号:{}, 可信度:{}, 校验通过:{}",
                    cameraID, bestContainer.getCtnNo(), bestContainer.getTrust(), bestContainer.getBcheck());

            // 如果有当前seqNo，则更新最佳结果缓存，等待预识别结束时统一推送
            if (currentSeqNo != null && seqNoGenerated) {
                updateBestResult(currentSeqNo, cameraID, filePath, detectResultList.get(0), bestContainer);
                log.info("已更新seqNo:{}的最佳识别结果缓存 - 相机ID:{}, 箱号:{}, 可信度:{}",
                        currentSeqNo, cameraID, bestContainer.getCtnNo(), bestContainer.getTrust());

                // 检查是否满足智能结束条件
                checkEndConditions(currentSeqNo, cameraID, bestContainer);

                log.debug("等待预识别结束时统一推送最佳结果到ECS");
                return; // 不立即推送ECS，等待预识别结束
            }

            // 如果没有seqNo，说明不是预识别流程，跳过ECS推送
            log.warn("RTSP识别时没有seqNo，跳过ECS推送 - 相机ID:{}, 箱号:{}", cameraID, bestContainer.getCtnNo());
        } else {
            log.warn("❌ RTSP识别无有效结果(内存) - 相机ID:{}, 原因:没有通过校验的结果", cameraID);
            // 选择可信度最高的结果进行日志记录
            RecContainer highestConfidence = containerList.stream()
                    .max(Comparator.comparing(RecContainer::getTrust))
                    .orElse(null);
            if (highestConfidence != null) {
                log.info("  最高可信度结果: 箱号:{}, 可信度:{}, 校验通过:{}",
                        highestConfidence.getCtnNo(), highestConfidence.getTrust(), highestConfidence.getBcheck());
            }
            deleteImageFile(filePath);
        }

        } catch (Exception e) {
            log.error("❌ 相机{}箱号识别过程发生未预期异常: {}, 图片: {}", cameraID, e.getMessage(), filePath, e);
            // 发生异常时删除图片，避免占用磁盘空间
            try {
                deleteImageFile(filePath);
            } catch (Exception deleteEx) {
                log.warn("删除异常图片文件失败: {}", filePath, deleteEx);
            }
        }
    }

    /**
     * 获取当前seqNo
     */
    public String getCurrentSeqNo() {
        return currentSeqNo;
    }

    /**
     * 根据IP地址获取对应的cameraID
     * IP映射规则：
     * ************* -> 15
     * ************* -> 13
     * ************* -> 14
     * ************* -> 12
     *
     * @param rtspUrl RTSP URL
     * @return 对应的cameraID
     */
    private Integer getCameraIdFromUrl(String rtspUrl) {
        try {
            // 从RTSP URL中提取IP地址
            String ip = rtspUrl.split("@")[1].split(":")[0];
            String lastOctet = ip.substring(ip.lastIndexOf('.') + 1);

            // 根据IP最后一位映射到cameraID
            switch (lastOctet) {
                case "143": return 15;
                case "144": return 13;
                case "145": return 14;
                case "146": return 12;
                default:
                    log.warn("未知的IP地址: {}, 使用默认cameraID: 1", ip);
                    return 1;
            }
        } catch (Exception e) {
            log.error("解析RTSP URL失败: {}", rtspUrl, e);
            return 1;
        }
    }

    /**
     * 验证配置的cameraID是否与IP映射一致
     */
    private void validateCameraIdMapping() {
        List<String> urlList = rtspConfig.getUrlList();
        List<Integer> cameraIdList = rtspConfig.getCameraIDList();

        if (urlList.size() != cameraIdList.size()) {
            log.error("RTSP URL列表和cameraID列表长度不一致！URL数量: {}, cameraID数量: {}",
                    urlList.size(), cameraIdList.size());
            return;
        }

        log.info("=== 验证IP到cameraID映射 ===");
        for (int i = 0; i < urlList.size(); i++) {
            String url = urlList.get(i);
            Integer configuredId = cameraIdList.get(i);
            Integer expectedId = getCameraIdFromUrl(url);

            if (!configuredId.equals(expectedId)) {
                log.error("cameraID映射不一致！URL: {}, 配置ID: {}, 期望ID: {}",
                        url, configuredId, expectedId);
            } else {
                log.info("✓ IP映射正确 - {} -> cameraID: {}", url, configuredId);
            }
        }
        log.info("=== IP到cameraID映射验证完成 ===");
    }

    /**
     * 检查智能结束条件
     * @param seqNo 序列号
     * @param cameraID 相机ID
     * @param bestContainer 最佳识别结果
     */
    private void checkEndConditions(String seqNo, Integer cameraID, RecContainer bestContainer) {
        if (seqNo == null || !seqNoGenerated) {
            return; // 只在预识别流程中检查
        }

        // 新增：等待所有相机连接成功后再进行智能结束检查
        if (connectedCameras.size() < allCameraIds.size()) {
            log.debug("等待所有相机连接完成再进行智能结束检查 - 已连接:{}/{}, 相机:{}",
                    connectedCameras.size(), allCameraIds.size(), cameraID);
            return;
        }

        log.debug("检查预识别结束条件 - seqNo:{}, 相机:{}, 所有相机已连接", seqNo, cameraID);

        // 条件1：高可信度结束（优先级最高）
        if (checkHighConfidenceEnd(seqNo, cameraID, bestContainer)) {
            currentEndReason = "高可信度识别完成";
            stopCapturingImmediately(currentEndReason);
            endPreRecognitionWithReason(seqNo, currentEndReason);
            return;
        }

        // 条件2：距离过近结束
        if (checkTooCloseEnd()) {
            currentEndReason = "吊具距离过近，无法清晰拍摄";
            stopCapturingImmediately(currentEndReason);
            endPreRecognitionWithReason(seqNo, currentEndReason);
            return;
        }

        // 条件4：超时结束
        if (checkTimeoutEnd(seqNo)) {
            currentEndReason = "预识别超时(60秒)";
            stopCapturingImmediately(currentEndReason);
            endPreRecognitionWithReason(seqNo, currentEndReason);
            return;
        }

        // 条件3：上锁结束 - 在PlcCoreService中已实现，这里不需要检查
    }

    /**
     * 检查高可信度结束条件
     * @param seqNo 序列号
     * @param cameraID 相机ID
     * @param container 识别结果
     * @return 是否满足高可信度结束条件
     */
    private boolean checkHighConfidenceEnd(String seqNo, Integer cameraID, RecContainer container) {
        if (!rtspConfig.getEnd().getHighConfidence().isEnabled()) {
            return false;
        }

        // 检查可信度和校验
        if (container.getTrust() >= rtspConfig.getEnd().getHighConfidence().getThreshold()
            && container.getBcheck()) {

            // 记录当前相机的识别结果
            Map<Integer, List<String>> cameraResults = seqNoCameraResults.computeIfAbsent(seqNo, k -> new ConcurrentHashMap<>());
            List<String> cameraHistory = cameraResults.computeIfAbsent(cameraID, k -> new ArrayList<>());

            cameraHistory.add(container.getCtnNo());
            if (cameraHistory.size() > rtspConfig.getEnd().getHighConfidence().getConsecutiveCount()) {
                cameraHistory.remove(0); // 保持最近N次
            }

            log.debug("相机{}识别历史: {}", cameraID, cameraHistory);

            // 检查是否有任意相机连续N次识别到相同箱号
            for (Map.Entry<Integer, List<String>> entry : cameraResults.entrySet()) {
                List<String> history = entry.getValue();
                if (history.size() >= rtspConfig.getEnd().getHighConfidence().getConsecutiveCount()) {
                    String firstResult = history.get(0);
                    if (history.stream().allMatch(result -> result.equals(firstResult))) {
                        log.info("相机{}连续{}次识别到相同箱号: {}, 满足高可信度结束条件",
                                entry.getKey(), rtspConfig.getEnd().getHighConfidence().getConsecutiveCount(), firstResult);
                        return true;
                    }
                }
            }

            // 检查多相机共识（如果有2个或以上相机最近都识别到相同箱号）
            return checkMultiCameraConsensus(cameraResults);
        }
        return false;
    }

    /**
     * 检查多相机共识
     * @param cameraResults 各相机识别结果
     * @return 是否达成共识
     */
    private boolean checkMultiCameraConsensus(Map<Integer, List<String>> cameraResults) {
        Map<String, Integer> containerCounts = new HashMap<>();
        for (Map.Entry<Integer, List<String>> entry : cameraResults.entrySet()) {
            List<String> history = entry.getValue();
            if (!history.isEmpty()) {
                String latestResult = history.get(history.size() - 1);
                containerCounts.put(latestResult, containerCounts.getOrDefault(latestResult, 0) + 1);
            }
        }

        // 如果有箱号被2个以上相机识别到
        for (Map.Entry<String, Integer> entry : containerCounts.entrySet()) {
            if (entry.getValue() >= 2) {
                log.info("箱号{}被{}个相机识别到，满足多相机共识条件", entry.getKey(), entry.getValue());
                return true;
            }
        }
        return false;
    }

    /**
     * 检查距离过近结束条件
     * @return 是否满足距离过近结束条件
     */
    private boolean checkTooCloseEnd() {
        if (!rtspConfig.getEnd().getTooClose().isEnabled()) {
            return false;
        }

        try {
            // 从PlcCoreService获取当前PLC数据
            // 这里假设PlcCoreService有getCurrentPlc()方法
            // 如果没有，需要通过其他方式获取当前高度数据
            Float currentHeight = getCurrentHeight();
            if (currentHeight != null && currentHeight != 999.0f && currentHeight > 0) {
                boolean tooClose = currentHeight <= rtspConfig.getEnd().getTooClose().getHeightThreshold();
                if (tooClose) {
                    log.info("当前高度{}米，低于阈值{}米，满足距离过近结束条件",
                            currentHeight, rtspConfig.getEnd().getTooClose().getHeightThreshold());
                }
                return tooClose;
            }
        } catch (Exception e) {
            log.warn("获取PLC高度数据失败", e);
        }
        return false;
    }

    /**
     * 检查超时结束条件
     * @param seqNo 序列号
     * @return 是否满足超时结束条件
     */
    private boolean checkTimeoutEnd(String seqNo) {
        if (!rtspConfig.getEnd().getTimeout().isEnabled()) {
            return false;
        }

        Long startTime = seqNoStartTimes.get(seqNo);
        if (startTime != null) {
            long elapsed = System.currentTimeMillis() - startTime;
            long maxTime = rtspConfig.getEnd().getTimeout().getMaxSeconds() * 1000L;
            boolean timeout = elapsed > maxTime;
            if (timeout) {
                log.info("预识别已运行{}秒，超过最大时长{}秒，满足超时结束条件",
                        elapsed / 1000, rtspConfig.getEnd().getTimeout().getMaxSeconds());
            }
            return timeout;
        }
        return false;
    }

    /**
     * 获取当前PLC高度数据
     * @return 当前高度（米）
     */
    private Float getCurrentHeight() {
        try {
            // 通过反射或其他方式获取PlcCoreService中的lastPlc
            // 这里暂时返回null，实际使用时需要根据PlcCoreService的接口调整
            // 可以考虑在PlcCoreService中添加一个公共方法来获取当前高度
            log.debug("获取当前PLC高度数据 - 暂时返回null，需要实现具体的获取逻辑");
            return null;
        } catch (Exception e) {
            log.warn("获取当前PLC高度失败", e);
            return null;
        }
    }

    /**
     * 提前结束预识别流程
     * @param seqNo 序列号
     * @param reason 结束原因
     */
    private void endPreRecognitionWithReason(String seqNo, String reason) {
        log.info("预识别提前结束 - seqNo:{}, 原因:{}", seqNo, reason);

        // 调用现有的end()方法，会自动推送最佳结果到ECS
        end();

        // 清理智能结束相关的临时数据（end()方法中已经清理了，这里不需要重复清理）
        // seqNoStartTimes.remove(seqNo);
        // seqNoCameraResults.remove(seqNo);
    }

    /**
     * 检查是否已生成seqNo
     */
    public boolean isSeqNoGenerated() {
        return seqNoGenerated;
    }

    /**
     * 标记相机连接成功
     * @param cameraId 相机ID
     */
    private void markCameraConnected(Integer cameraId) {
        connectedCameras.add(cameraId);
        log.info("相机{}连接成功，已连接相机数:{}/{}", cameraId, connectedCameras.size(), allCameraIds.size());

        // 如果所有相机都连接成功，记录日志
        if (connectedCameras.size() == allCameraIds.size()) {
            log.info("✅ 所有相机连接完成，开始智能结束条件检查");
        }
    }

    /**
     * 测试所有相机连接状态
     */
    public void testAllCameraConnections() {
        log.info("🔍 开始测试所有相机连接状态...");

        List<String> urlList = rtspConfig.getUrlList();
        List<Integer> cameraIdList = rtspConfig.getCameraIDList();

        for (int i = 0; i < urlList.size(); i++) {
            String url = urlList.get(i);
            Integer cameraId = cameraIdList.get(i);

            log.info("🧪 测试相机{} - URL: {}", cameraId, url);

            FFmpegFrameGrabber testGrabber = new FFmpegFrameGrabber(url);
            try {
                testGrabber.setOption("rtsp_transport", "tcp");
                testGrabber.setOption("stimeout", "10000000");

                testGrabber.start();
                log.info("✅ 相机{}连接成功 - 分辨率: {}x{}", cameraId,
                        testGrabber.getImageWidth(), testGrabber.getImageHeight());

                // 尝试抓取一帧测试
                Frame testFrame = testGrabber.grabImage();
                if (testFrame != null) {
                    log.info("✅ 相机{}抓拍测试成功", cameraId);
                    testFrame.close();
                } else {
                    log.warn("⚠️ 相机{}抓拍测试失败 - 无法获取帧", cameraId);
                }

            } catch (Exception e) {
                log.error("❌ 相机{}连接失败 - URL: {}, 错误: {}", cameraId, url, e.getMessage());
            } finally {
                try {
                    testGrabber.stop();
                    testGrabber.release();
                } catch (Exception e) {
                    log.debug("清理测试抓取器异常", e);
                }
            }
        }

        log.info("🔍 所有相机连接测试完成");
    }

    /**
     * 立即停止抓拍
     * @param reason 停止原因
     */
    private void stopCapturingImmediately(String reason) {
        log.info("🛑 立即停止RTSP抓拍 - 原因:{}", reason);
        isCapturing = false;
        preEnd = true;

        // 记录停止时间和原因
        log.info("RTSP抓拍已停止 - 原因:{}, 时间:{}", reason, System.currentTimeMillis());
    }

    /**
     * 保存预识别结果到数据库
     * @param seqNo 序列号
     * @param bestResult 最佳识别结果
     * @param allCameraResults 所有相机识别结果
     */
    private void savePreRecognitionResultToDatabase(String seqNo, RtspBestResult bestResult,
                                                   Map<Integer, RtspBestResult> allCameraResults) {
        try {
            // 获取开始时间
            Long startTimeMs = seqNoStartTimes.get(seqNo);
            LocalDateTime startTime = startTimeMs != null ?
                LocalDateTime.ofInstant(Instant.ofEpochMilli(startTimeMs), ZoneId.systemDefault()) :
                LocalDateTime.now();

            // 获取结束原因
            String endReason = currentEndReason.isEmpty() ? "正常结束" : currentEndReason;

            // 调用服务保存到数据库
            preRecognitionService.savePreRecognitionResult(seqNo, bestResult, allCameraResults, endReason, startTime);

        } catch (Exception e) {
            log.error("保存预识别结果到数据库失败 - seqNo:{}", seqNo, e);
        }
    }

    /**
     * 检查并启动车顶号抓拍
     * 条件：状态为作业中，且作业类型是1-8的其中之一（9、10、11三种定位类型的作业不涉及到抓箱子，不用管），
     * 且小车位置处于作业车道，且吊具距离箱面高度达到预设值，
     * 作业类型为集卡到堆场-2，集卡到火车-6这两种需多用侧边梁相机抓拍
     */
    private void checkAndStartTruckTopCapture() {
        try {
            // 获取当前PLC数据（从PlcCoreService获取最新的PLC数据）
            // 注意：这里需要根据实际的PlcCoreService API来获取当前PLC数据
            // 暂时使用null检查，实际使用时需要实现获取当前PLC数据的方法
            Plc currentPlc = getCurrentPlcData();
            if (currentPlc == null) {
                log.debug("车顶号抓拍检查 - 无PLC数据");
                return;
            }

            // 条件1：状态为作业中
            boolean taskStatusCondition = currentPlc.getTaskStatus() != null && currentPlc.getTaskStatus() == 1;

            // 条件2：作业类型是1-8（排除9、10、11定位类型作业）
            boolean workTypeCondition = currentPlc.getWorkType() != null &&
                                      currentPlc.getWorkType() >= 1 && currentPlc.getWorkType() <= 8;

            // 条件3：小车位置处于作业车道（通过车道查询验证）
            boolean laneCondition = currentPlc.getLane() != null && !currentPlc.getLane().equals("未知");

            // 条件4：吊具距离箱面高度达到预设值（使用RTSP配置的高度范围）
            boolean heightCondition = checkContainerHeightForTruckTop(currentPlc);

            log.info("车顶号抓拍条件检查 - 任务状态:{}, 作业类型:{}, 车道:{}, 高度条件:{}",
                    taskStatusCondition, workTypeCondition, laneCondition, heightCondition);

            // 所有条件都满足才进行车顶号抓拍
            if (taskStatusCondition && workTypeCondition && laneCondition && heightCondition) {
                // 判断是否为特殊作业类型（集卡到堆场-2，集卡到火车-6）
                boolean isSpecialWorkType = (currentPlc.getWorkType().intValue() == 2 ||
                                           currentPlc.getWorkType().intValue() == 6);

                if (isSpecialWorkType) {
                    log.info("检测到特殊作业类型（集卡到堆场-2/集卡到火车-6），启动车顶号抓拍 - 作业类型:{}, seqNo:{}",
                            currentPlc.getWorkType(), currentSeqNo);
                    startTruckTopCaptureInRtsp();
                } else {
                    log.debug("作业类型{}不需要车顶号抓拍", currentPlc.getWorkType());
                }
            } else {
                log.debug("车顶号抓拍条件不满足 - 任务状态:{}, 作业类型:{}, 车道:{}, 高度:{}",
                        taskStatusCondition, workTypeCondition, laneCondition, heightCondition);
            }

        } catch (Exception e) {
            log.error("检查车顶号抓拍条件异常", e);
        }
    }

    /**
     * 检查箱面高度条件（用于车顶号抓拍）
     */
    private boolean checkContainerHeightForTruckTop(Plc plc) {
        if (plc.getSpreaderFromContainerHeight() == null) {
            log.debug("车顶号抓拍高度检查 - 高度数据为空");
            return false;
        }

        float height = plc.getSpreaderFromContainerHeight();

        // 排除无效数据
        if (height == 999.0f || height <= 0) {
            log.debug("车顶号抓拍高度检查 - 无效高度数据: {}", height);
            return false;
        }

        // 获取配置的高度范围（使用RTSP配置）
        float minHeight = rtspConfig.getRecognition().getMinHeight();
        float maxHeight = rtspConfig.getRecognition().getMaxHeight();

        // 检查是否在配置的高度范围内
        boolean condition = height >= minHeight && height <= maxHeight;

        log.debug("车顶号抓拍高度检查 - 当前高度:{}, 条件({}~{}米):{}",
                height, minHeight, maxHeight, condition);

        return condition;
    }

    /**
     * 在RTSP预识别流程中启动车顶号抓拍
     */
    private void startTruckTopCaptureInRtsp() {
        try {
            // 获取车顶号识别摄像头配置
            List<RecognizeConfigVO> truckTopCameras = recognizeConfigRepository.queryWorkConfigByType(
                    null, RecognizeConfigTypeEnum.PLATE_TOP.getValue());

            if (CollectionUtils.isEmpty(truckTopCameras)) {
                log.warn("未找到车顶号识别摄像头配置，跳过车顶号抓拍");
                return;
            }

            log.info("开始RTSP车顶号抓拍 - seqNo:{}, 摄像头数量:{}", currentSeqNo, truckTopCameras.size());

            // 异步执行车顶号抓拍
            for (RecognizeConfigVO cameraConfig : truckTopCameras) {
                String taskDescription = String.format("车顶号抓拍任务 - 摄像头:%s", cameraConfig.getName());
                threadPoolManager.submitRtspTask(() -> {
                    captureTruckTopInRtsp(cameraConfig);
                }, taskDescription);
            }

        } catch (Exception e) {
            log.error("启动RTSP车顶号抓拍异常 - seqNo:{}", currentSeqNo, e);
        }
    }

    /**
     * 执行单个摄像头的车顶号抓拍
     */
    private void captureTruckTopInRtsp(RecognizeConfigVO cameraConfig) {
        try {
            log.info("开始车顶号抓拍 - seqNo:{}, 摄像头:{}", currentSeqNo, cameraConfig.getName());

            // 注册摄像头
            HikvisionService hikvisionService = hikvisionServiceFactory.getService(cameraConfig.getIp());
            boolean registerResult = hikvisionService.register(
                    cameraConfig.getIp(),
                    cameraConfig.getControlPort() != null ? cameraConfig.getControlPort() : 8000,
                    cameraConfig.getUsername(),
                    cameraConfig.getPassword()
            );

            if (!registerResult) {
                log.warn("车顶号摄像头注册失败 - seqNo:{}, 摄像头:{}", currentSeqNo, cameraConfig.getName());
                return;
            }

            // 设置预置位
            if (cameraConfig.getPresetLocation() != null && cameraConfig.getPresetLocation() != 0) {
                boolean setPositionResult = hikvisionService.setPosition(
                        cameraConfig.getChannel() != null ? cameraConfig.getChannel() : 1,
                        39,
                        cameraConfig.getPresetLocation()
                );
                if (!setPositionResult) {
                    log.warn("车顶号摄像头设置预置位失败 - seqNo:{}, 摄像头:{}", currentSeqNo, cameraConfig.getName());
                }
            }

            // 抓拍图片
            String imagePath = captureTruckTopImage(cameraConfig);
            if (imagePath == null) {
                log.warn("车顶号图片抓拍失败 - seqNo:{}, 摄像头:{}", currentSeqNo, cameraConfig.getName());
                return;
            }

            // 识别车顶号
            String truckTopNo = recognizeTruckTopFromImage(imagePath);

            // 发送识别结果到ECS（无论是否识别到车顶号）
            sendTruckTopResultToEcs(truckTopNo, imagePath, cameraConfig);

            if (truckTopNo != null) {
                log.info("RTSP流程中识别到车顶号: {} - seqNo:{}, 摄像头:{}",
                        truckTopNo, currentSeqNo, cameraConfig.getName());
            } else {
                log.info("RTSP流程中未识别到车顶号 - seqNo:{}, 摄像头:{}", currentSeqNo, cameraConfig.getName());
            }

        } catch (Exception e) {
            log.error("车顶号抓拍异常 - seqNo:{}, 摄像头:{}, 错误:{}",
                    currentSeqNo, cameraConfig.getName(), e.getMessage(), e);
        }
    }

    /**
     * 抓拍车顶号图片
     */
    private String captureTruckTopImage(RecognizeConfigVO cameraConfig) {
        try {
            // 构建图片保存路径 - 在RTSP预识别子目录中单独开一个车顶号识别目录
            String dateStr = DateUtil.getDate(DateUtil.TYPE_EIGHT);
            String saveDir = projectConfig.getFileDirPath() + "rtsp/" + dateStr + "/" + currentSeqNo + "/truck_top_recognition/";
            String fileName = "truck_top_" + cameraConfig.getCode() + "_" + System.currentTimeMillis() + ".jpg";
            String filePath = saveDir + fileName;

            File file = new File(filePath);
            if (!file.getParentFile().exists()) {
                boolean created = file.getParentFile().mkdirs();
                if (!created) {
                    log.warn("创建车顶号图片目录失败: {}", saveDir);
                }
            }

            // 抓拍图片
            HikvisionService hikvisionService = hikvisionServiceFactory.getService(cameraConfig.getIp());
            boolean snapResult = hikvisionService.snapMemory(
                    cameraConfig.getChannel() != null ? cameraConfig.getChannel() : 1,
                    file
            );

            if (snapResult) {
                log.debug("车顶号图片抓拍成功 - seqNo:{}, 摄像头:{}, 路径:{}",
                        currentSeqNo, cameraConfig.getName(), filePath);
                return filePath;
            } else {
                log.warn("车顶号图片抓拍失败 - seqNo:{}, 摄像头:{}", currentSeqNo, cameraConfig.getName());
                return null;
            }

        } catch (Exception e) {
            log.error("抓拍车顶号图片异常 - seqNo:{}, 摄像头:{}, 错误:{}",
                    currentSeqNo, cameraConfig.getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从图片识别车顶号
     */
    private String recognizeTruckTopFromImage(String imagePath) {
        try {
            // 检测车顶号
            DetectResults detectResults = topPlateAlgorithmService.detPicture(imagePath);
            if (detectResults == null || detectResults.getDet_num() == 0) {
                log.debug("未检测到车顶号 - 图片:{}", imagePath);
                return null;
            }

            // 识别车顶号
            RecTopPlate recTopPlate = topPlateAlgorithmService.recPicture(imagePath, detectResults);
            if (recTopPlate != null && recTopPlate.getTopPlate() != null && !recTopPlate.getTopPlate().trim().isEmpty()) {
                String truckTopNo = recTopPlate.getTopPlate().trim();
                log.info("识别到车顶号: {} - 图片:{}, 可信度:{}",
                        truckTopNo, imagePath, recTopPlate.getTrust());
                return truckTopNo;
            }

            return null;

        } catch (Exception e) {
            log.error("识别车顶号异常 - 图片:{}, 错误:{}", imagePath, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 发送车顶号识别结果到ECS
     * @param truckTopNo 车顶号（可以为null）
     * @param imagePath 图片路径（可以为null）
     * @param cameraConfig 摄像头配置
     */
    private void sendTruckTopResultToEcs(String truckTopNo, String imagePath, RecognizeConfigVO cameraConfig) {
        try {
            // 构建ECS识别信息
            EcsIdentifyInfoDTO identifyInfo = new EcsIdentifyInfoDTO();

            // 设置门吊号
            identifyInfo.setCraneNo(pathConfigUtil.getRmgName());

            // 设置识别类型为拖车
            identifyInfo.setIdentifyType(1);

            // 设置车顶号（如果为空则设置为null）
            identifyInfo.setIdentifyTruckRoofNo(truckTopNo);

            // 如果有图片路径，转换为URL并添加
            if (!StringUtils.isEmpty(imagePath)) {
                List<String> imgUrls = new ArrayList<>();
                String imageUrl = convertImagePathToUrl(imagePath);
                if (!StringUtils.isEmpty(imageUrl)) {
                    imgUrls.add(imageUrl);
                    identifyInfo.setImgUrls(imgUrls);
                    log.debug("RTSP车顶号识别图片URL已添加 - seqNo: {}, 图片URL: {}", currentSeqNo, imageUrl);
                }
            }

            log.info("开始发送RTSP车顶号识别结果到ECS - seqNo: {}, 车顶号: {}, 摄像头: {}",
                    currentSeqNo, truckTopNo != null ? truckTopNo : "无", cameraConfig.getName());

            // 异步发送到ECS
            String taskDescription = String.format("发送车顶号识别结果到ECS - seqNo:%s, 车顶号:%s",
                    currentSeqNo, truckTopNo != null ? truckTopNo : "无");
            threadPoolManager.submitRtspTask(() -> {
                try {
                    ecsService.sendIdentifyInfo(identifyInfo);
                    log.info("RTSP车顶号识别结果发送到ECS成功 - seqNo: {}, 车顶号: {}",
                            currentSeqNo, truckTopNo != null ? truckTopNo : "无");
                } catch (Exception e) {
                    log.error("发送RTSP车顶号识别结果到ECS异常 - seqNo: {}, 车顶号: {}, 错误: {}",
                            currentSeqNo, truckTopNo != null ? truckTopNo : "无", e.getMessage(), e);
                }
            }, taskDescription);

        } catch (Exception e) {
            log.error("构建RTSP车顶号ECS信息异常 - seqNo: {}, 摄像头: {}, 错误: {}",
                    currentSeqNo, cameraConfig.getName(), e.getMessage(), e);
        }
    }

    /**
     * 将图片路径转换为URL（复用TruckTopRecognitionManager的逻辑）
     * @param imagePath 图片路径
     * @return 图片URL
     */
    private String convertImagePathToUrl(String imagePath) {
        try {
            if (StringUtils.isEmpty(imagePath)) {
                return null;
            }

            // 如果已经是完整URL，直接返回
            if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
                return imagePath;
            }

            // 获取IIS URL配置
            String iisUrl = pathConfigUtil.getIisUrl();
            if (StringUtils.isEmpty(iisUrl)) {
                log.warn("IIS URL配置为空，无法转换图片路径为URL");
                return null;
            }

            // 处理图片路径，转换为HTTP可访问的URL
            String actualFilePath = imagePath;

            // 如果路径包含项目文件目录，则去除该部分
            String baseDir = projectConfig.getFileDirPath();
            if (!StringUtils.isEmpty(baseDir) && actualFilePath.startsWith(baseDir)) {
                actualFilePath = actualFilePath.substring(baseDir.length());
            }

            // 确保路径分隔符正确（Windows -> Unix）
            actualFilePath = actualFilePath.replace("\\", "/");
            if (!actualFilePath.startsWith("/")) {
                actualFilePath = "/" + actualFilePath;
            }

            // 确保IIS URL以/结尾
            if (!iisUrl.endsWith("/")) {
                iisUrl += "/";
            }

            return iisUrl + actualFilePath.substring(1); // 去掉开头的/避免双斜杠

        } catch (Exception e) {
            log.error("转换图片路径为URL失败 - 原路径: {}, 错误: {}", imagePath, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取当前PLC数据
     * 注意：这个方法需要根据实际的PlcCoreService API来实现
     */
    private Plc getCurrentPlcData() {
        try {
            // 这里需要根据实际的PlcCoreService API来获取当前PLC数据
            // 由于PlcCoreService没有直接的getCurrentPlc方法，
            // 我们可以通过其他方式获取，比如从缓存或者其他服务

            // 临时返回null，实际使用时需要实现具体的获取逻辑
            log.debug("getCurrentPlcData方法需要根据实际API实现");
            return null;

        } catch (Exception e) {
            log.error("获取当前PLC数据异常", e);
            return null;
        }
    }

    /**
     * 获取线程池状态（用于监控和调试）
     */
    public String getThreadPoolStatus() {
        return threadPoolManager.getRtspPoolStatus();
    }
}
