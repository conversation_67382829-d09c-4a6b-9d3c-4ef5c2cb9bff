package net.pingfang.core.hardware.rtsp;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import net.pingfang.core.algorithm.ailib.model.DetectResult;
import net.pingfang.core.algorithm.ailib.model.RecContainer;

/**
 * RTSP最佳识别结果
 *
 * <AUTHOR>
 * @date 2025-07-01
 */
@Data
@NoArgsConstructor
public class RtspBestResult {
    
    /**
     * 序列号
     */
    private String seqNo;
    
    /**
     * 图片路径
     */
    private String imagePath;
    
    /**
     * 检测结果
     */
    private DetectResult detectResult;
    
    /**
     * 识别结果
     */
    private RecContainer recognitionResult;
    
    /**
     * 可信度
     */
    private float confidence;
    
    /**
     * 相机ID
     */
    private Integer cameraId;
    
    /**
     * 时间戳
     */
    private long timestamp;

    /**
     * 识别次数
     */
    private int recognitionCount = 1;

    /**
     * 全参构造函数
     */
    public RtspBestResult(String seqNo, String imagePath, DetectResult detectResult,
                         RecContainer recognitionResult, float confidence, Integer cameraId,
                         long timestamp, int recognitionCount) {
        this.seqNo = seqNo;
        this.imagePath = imagePath;
        this.detectResult = detectResult;
        this.recognitionResult = recognitionResult;
        this.confidence = confidence;
        this.cameraId = cameraId;
        this.timestamp = timestamp;
        this.recognitionCount = recognitionCount;
    }
}
