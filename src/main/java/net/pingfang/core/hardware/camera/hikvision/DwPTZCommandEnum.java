package net.pingfang.core.hardware.camera.hikvision;

import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * 云台控制指令枚举类
 *
 * @author: CM
 * @date: 2023/5/29
 */
public enum DwPTZCommandEnum {
    LIGHT_PWRON(2, "接通灯光电源"),
    WIPER_PWRON(3, "接通雨刷开关"),
    FAN_PWRON(4, "接通风扇开关"),
    HEATER_PWRON(5, "接通加热器开关"),
    AUX_PWRON1(6, "接通辅助设备开关"),
    AUX_PWRON2(7, "接通辅助设备开关"),
    ZOOM_IN(11, "焦距变大(倍率变大)"),
    ZOOM_OUT(12, "焦距变小(倍率变小)"),
    FOCUS_NEAR(13, "焦点前调"),
    FOCUS_FAR(14, "焦点后调"),
    IRIS_OPEN(15, "光圈扩大"),
    IRIS_CLOSE(16, "光圈缩小"),
    TILT_UP(21, "云台上仰"),
    TILT_DOWN(22, "云台下俯"),
    PAN_LEFT(23, "云台左转"),
    PAN_RIGHT(24, "云台右转"),
    UP_LEFT(25, "云台上仰和左转"),
    UP_RIGHT(26, "云台上仰和右转"),
    DOWN_LEFT(27, "云台下俯和左转"),
    DOWN_RIGHT(28, "云台下俯和右转"),
    PAN_AUTO(29, "云台左右自动扫描"),
    TILT_DOWN_ZOOM_IN(58, "云台下俯和焦距变大(倍率变大)"),
    TILT_DOWN_ZOOM_OUT(59, "云台下俯和焦距变小(倍率变小)"),
    PAN_LEFT_ZOOM_IN(60, "云台左转和焦距变大(倍率变大)"),
    PAN_LEFT_ZOOM_OUT(61, "云台左转和焦距变小(倍率变小)"),
    PAN_RIGHT_ZOOM_IN(62, "云台右转和焦距变大(倍率变大)"),
    PAN_RIGHT_ZOOM_OUT(63, "云台右转和焦距变小(倍率变小)"),
    UP_LEFT_ZOOM_IN(64, "云台上仰和左转和焦距变大(倍率变大"),
    UP_LEFT_ZOOM_OUT(65, "云台上仰和左转和焦距变小(倍率变小"),
    UP_RIGHT_ZOOM_IN(66, "云台上仰和右转和焦距变大(倍率变大"),
    UP_RIGHT_ZOOM_OUT(67, "云台上仰和右转和焦距变小(倍率变小"),
    DOWN_LEFT_ZOOM_IN(68, "云台下俯和左转和焦距变大(倍率变大"),
    DOWN_LEFT_ZOOM_OUT(69, "云台下俯和左转和焦距变小(倍率变小"),
    DOWN_RIGHT_ZOOM_IN(70, "云台下俯和右转和焦距变大(倍率变大"),
    DOWN_RIGHT_ZOOM_OUT(71, "云台下俯和右转和焦距变小(倍率变小"),
    TILT_UP_ZOOM_IN(72, "云台上仰和焦距变大(倍率变大)"),
    TILT_UP_ZOOM_OUT(73, "云台上仰和焦距变小(倍率变小)");

    @Getter
    private Integer value;

    @Getter
    private String desc;

    DwPTZCommandEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDescByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (DwPTZCommandEnum enu : DwPTZCommandEnum.values()) {
            if (enu.getValue().equals(value)) {
                return enu.getDesc();
            }
        }
        return null;
    }

    public static Integer getValueByDesc(String desc) {
        if (StringUtils.isEmpty(desc)) {
            return null;
        }
        for (DwPTZCommandEnum enu : DwPTZCommandEnum.values()) {
            if (enu.getDesc().equalsIgnoreCase(desc)) {
                return enu.getValue();
            }
        }
        return null;
    }
}
