package net.pingfang.controller;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.core.hardware.rtsp.RtspService;
import net.pingfang.core.hardware.plc.PlcCoreService;
import net.pingfang.service.impl.TruckTopRecognitionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池监控控制器 - 用于线上自测
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Slf4j
@RestController
@RequestMapping("/api/monitor/threadpool")
public class ThreadPoolMonitorController {

    @Autowired
    private RtspService rtspService;
    
    @Autowired
    private PlcCoreService plcCoreService;
    
    @Autowired
    private TruckTopRecognitionManager truckTopRecognitionManager;

    /**
     * 获取所有线程池状态
     */
    @GetMapping("/status")
    public Map<String, Object> getThreadPoolStatus() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // RTSP服务线程池状态
            Map<String, Object> rtspStatus = getExecutorStatus(rtspService, "executor", "RTSP服务");
            result.put("rtspService", rtspStatus);
            
            // PLC服务线程池状态
            Map<String, Object> plcStatus = getExecutorStatus(plcCoreService, "executor", "PLC服务");
            result.put("plcService", plcStatus);
            
            // 车顶号识别线程池状态
            Map<String, Object> truckTopStatus = getExecutorStatus(truckTopRecognitionManager, "executorService", "车顶号识别");
            result.put("truckTopService", truckTopStatus);
            
            result.put("timestamp", System.currentTimeMillis());
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("获取线程池状态异常", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 测试线程池任务提交
     */
    @GetMapping("/test-submit")
    public Map<String, Object> testTaskSubmit() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 测试RTSP线程池任务提交
            boolean rtspSubmitSuccess = testExecutorSubmit(rtspService, "executor", "RTSP测试任务");
            result.put("rtspSubmitSuccess", rtspSubmitSuccess);
            
            // 测试PLC线程池任务提交
            boolean plcSubmitSuccess = testExecutorSubmit(plcCoreService, "executor", "PLC测试任务");
            result.put("plcSubmitSuccess", plcSubmitSuccess);
            
            // 测试车顶号识别线程池任务提交
            boolean truckTopSubmitSuccess = testExecutorSubmit(truckTopRecognitionManager, "executorService", "车顶号测试任务");
            result.put("truckTopSubmitSuccess", truckTopSubmitSuccess);
            
            result.put("timestamp", System.currentTimeMillis());
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("测试任务提交异常", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 强制触发垃圾回收并检查线程池状态
     */
    @GetMapping("/gc-test")
    public Map<String, Object> gcTest() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 记录GC前状态
            Map<String, Object> beforeGc = getThreadPoolStatus();
            
            // 强制垃圾回收
            System.gc();
            Thread.sleep(2000); // 等待2秒
            
            // 记录GC后状态
            Map<String, Object> afterGc = getThreadPoolStatus();
            
            result.put("beforeGc", beforeGc);
            result.put("afterGc", afterGc);
            result.put("timestamp", System.currentTimeMillis());
            result.put("success", true);
            
        } catch (Exception e) {
            log.error("GC测试异常", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }
        
        return result;
    }

    /**
     * 获取执行器状态
     */
    private Map<String, Object> getExecutorStatus(Object service, String fieldName, String serviceName) {
        Map<String, Object> status = new HashMap<>();
        
        try {
            Field field = service.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            ExecutorService executor = (ExecutorService) field.get(service);
            
            if (executor == null) {
                status.put("status", "NULL");
                status.put("error", "线程池对象为空");
                return status;
            }
            
            status.put("serviceName", serviceName);
            status.put("isShutdown", executor.isShutdown());
            status.put("isTerminated", executor.isTerminated());
            
            // 如果是ThreadPoolExecutor，获取更详细信息
            if (executor instanceof ThreadPoolExecutor) {
                ThreadPoolExecutor tpe = (ThreadPoolExecutor) executor;
                status.put("corePoolSize", tpe.getCorePoolSize());
                status.put("maximumPoolSize", tpe.getMaximumPoolSize());
                status.put("activeCount", tpe.getActiveCount());
                status.put("taskCount", tpe.getTaskCount());
                status.put("completedTaskCount", tpe.getCompletedTaskCount());
                status.put("queueSize", tpe.getQueue().size());
            }
            
            status.put("status", "NORMAL");
            
        } catch (Exception e) {
            log.error("获取{}线程池状态异常", serviceName, e);
            status.put("status", "ERROR");
            status.put("error", e.getMessage());
        }
        
        return status;
    }

    /**
     * 测试执行器任务提交
     */
    private boolean testExecutorSubmit(Object service, String fieldName, String taskName) {
        try {
            Field field = service.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            ExecutorService executor = (ExecutorService) field.get(service);
            
            if (executor == null) {
                log.warn("{}线程池对象为空", taskName);
                return false;
            }
            
            if (executor.isShutdown() || executor.isTerminated()) {
                log.warn("{}线程池已关闭，无法提交任务", taskName);
                return false;
            }
            
            // 提交测试任务
            executor.submit(() -> {
                log.info("{}执行成功 - 线程: {}", taskName, Thread.currentThread().getName());
                try {
                    Thread.sleep(100); // 模拟任务执行
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            });
            
            return true;
            
        } catch (Exception e) {
            log.error("测试{}任务提交异常", taskName, e);
            return false;
        }
    }
}
