package net.pingfang.controller;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.model.dto.RtspPreRecognitionQueryDTO;
import net.pingfang.model.dto.RtspPreRecognitionListDTO;
import net.pingfang.model.dto.RtspPreRecognitionDetailDTO;
import net.pingfang.service.RtspPreRecognitionService;
import org.springframework.beans.factory.annotation.Autowired;
import net.pingfang.model.common.Page;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * RTSP预识别Controller
 * 
 * <AUTHOR>
 * @since 2025-07-07
 */
@Slf4j
@RestController
@RequestMapping("/api/rtsp/pre-recognition")
public class RtspPreRecognitionController {
    
    @Autowired
    private RtspPreRecognitionService preRecognitionService;
    
    /**
     * 分页查询预识别记录列表 (POST方式，兼容前端)
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    @PostMapping("/list")
    public Map<String, Object> getPreRecognitionListPost(@RequestBody RtspPreRecognitionQueryDTO queryDTO) {
        log.info("查询预识别列表(POST) - 参数:{}", queryDTO);

        Map<String, Object> result = new HashMap<>();

        try {
            Page<RtspPreRecognitionListDTO> pageResult = preRecognitionService.getPreRecognitionList(queryDTO);

            // 获取统计信息
            Map<String, Object> statistics = getStatisticsData();

            Map<String, Object> data = new HashMap<>();
            data.put("total", pageResult.getTotal());
            data.put("page", queryDTO.getPage());
            data.put("size", queryDTO.getSize());
            data.put("pages", (pageResult.getTotal() + queryDTO.getSize() - 1) / queryDTO.getSize());
            data.put("records", pageResult.getRecords());

            // 添加统计信息到返回数据中
            data.put("statistics", statistics);

            result.put("code", 200);
            result.put("message", "success");
            result.put("data", data);

            log.info("查询预识别列表成功(POST) - 总数:{}, 当前页:{}", pageResult.getTotal(), queryDTO.getPage());

        } catch (Exception e) {
            log.error("查询预识别列表失败(POST)", e);
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
            result.put("data", null);
        }

        return result;
    }

    /**
     * 根据ID获取预识别记录的图片列表
     *
     * @param id 记录ID
     * @return 图片列表
     */
    @GetMapping("/{id}/images")
    public Map<String, Object> getPreRecognitionImages(@PathVariable Long id) {
        log.info("查询预识别图片列表 - ID:{}", id);

        Map<String, Object> result = new HashMap<>();

        try {
            List<Map<String, Object>> imageList = preRecognitionService.getPreRecognitionImages(id);

            if (imageList != null) {
                result.put("code", 200);
                result.put("message", "success");
                result.put("data", imageList);

                log.info("查询预识别图片列表成功 - ID:{}, 图片数量:{}", id, imageList.size());
            } else {
                result.put("code", 404);
                result.put("message", "未找到指定的预识别记录");
                result.put("data", null);

                log.warn("未找到预识别记录 - ID:{}", id);
            }

        } catch (Exception e) {
            log.error("查询预识别图片列表失败 - ID:{}", id, e);
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
            result.put("data", null);
        }
        
        return result;
    }

    /**
     * 根据ID获取预识别详情
     *
     * @param id 记录ID
     * @return 详情信息
     */
    @GetMapping("/{id}")
    public Map<String, Object> getPreRecognitionDetail(@PathVariable Long id) {
        log.info("查询预识别详情 - ID:{}", id);

        Map<String, Object> result = new HashMap<>();

        try {
            RtspPreRecognitionDetailDTO detail = preRecognitionService.getPreRecognitionDetail(id);

            if (detail != null) {
                result.put("code", 200);
                result.put("message", "success");
                result.put("data", detail);

                log.info("查询预识别详情成功 - ID:{}, seqNo:{}", id, detail.getSeqNo());
            } else {
                result.put("code", 404);
                result.put("message", "未找到指定的预识别记录");
                result.put("data", null);

                log.warn("未找到预识别记录 - ID:{}", id);
            }

        } catch (Exception e) {
            log.error("查询预识别详情失败 - ID:{}", id, e);
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
            result.put("data", null);
        }

        return result;
    }

    /**
     * 获取预识别统计信息
     * 
     * @param craneNo 吊机号
     * @param days 统计天数（默认7天）
     * @return 统计信息
     */
    @GetMapping("/statistics")
    public Map<String, Object> getPreRecognitionStatistics(
            @RequestParam(required = false) String craneNo,
            @RequestParam(defaultValue = "7") Integer days) {
        
        log.info("查询预识别统计 - 吊机号:{}, 天数:{}", craneNo, days);
        
        Map<String, Object> result = new HashMap<>();
        
        try {
            // TODO: 实现统计功能
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalCount", 0);
            statistics.put("successCount", 0);
            statistics.put("failedCount", 0);
            statistics.put("avgDuration", 0);
            statistics.put("avgConfidence", 0.0);
            
            result.put("code", 200);
            result.put("message", "success");
            result.put("data", statistics);
            
        } catch (Exception e) {
            log.error("查询预识别统计失败", e);
            result.put("code", 500);
            result.put("message", "查询失败: " + e.getMessage());
            result.put("data", null);
        }
        
        return result;
    }

    /**
     * 获取统计数据的私有方法
     */
    private Map<String, Object> getStatisticsData() {
        // 创建一个查询DTO来获取所有数据
        RtspPreRecognitionQueryDTO queryDTO = new RtspPreRecognitionQueryDTO();
        queryDTO.setPage(1);
        queryDTO.setSize(Integer.MAX_VALUE); // 获取所有数据用于统计

        Page<RtspPreRecognitionListDTO> allData = preRecognitionService.getPreRecognitionList(queryDTO);
        List<RtspPreRecognitionListDTO> records = allData.getRecords();

        Map<String, Object> statistics = new HashMap<>();

        if (records == null || records.isEmpty()) {
            statistics.put("totalCount", 0);
            statistics.put("successCount", 0);
            statistics.put("failedCount", 0);
            statistics.put("successRate", "0.00%");
            statistics.put("avgConfidence", "0.00");
            return statistics;
        }

        int totalCount = records.size();
        long successCount = records.stream()
                .filter(r -> "识别成功".equals(r.getEcsPushStatus()) &&
                           r.getBestContainerNo() != null &&
                           !"未识别".equals(r.getBestContainerNo()))
                .count();
        int failedCount = totalCount - (int)successCount;

        // 计算识别率
        double successRate = totalCount > 0 ? (double)successCount / totalCount * 100 : 0.0;
        String successRateStr = String.format("%.2f%%", successRate);

        // 计算平均置信度（只计算成功的记录）
        double avgConfidence = records.stream()
                .filter(r -> "识别成功".equals(r.getEcsPushStatus()) && r.getBestConfidence() != null)
                .mapToDouble(r -> r.getBestConfidence().doubleValue())
                .average()
                .orElse(0.0);

        statistics.put("totalCount", totalCount);
        statistics.put("successCount", (int)successCount);
        statistics.put("failedCount", failedCount);
        statistics.put("successRate", successRateStr);
        statistics.put("avgConfidence", String.format("%.2f", avgConfidence));

        return statistics;
    }
}
