package net.pingfang.constant;

/**
 * <AUTHOR>
 * @since 2023-07-17 15:47
 */
public interface ErrorMessageConstant {

    interface RecognitionErrorMessage {
        String SPREADER_TYPE_NULL = "吊具类型不能为空";
        String WORK_TYPE_NULL = "作业类型不能为空";
        String LANE_NULL = "车道不能为空";
        String FLAG_NULL = "检测类型不能为空";
        String TYPE_NULL = "预置位类型不能为空";
    }


    interface LaneErrorMessage {
        String SPREADER_TYPE_NULL = "吊具类型不能为空";
        String CONFIG_NULL = "配置数值不能为空";
        String LANE_NULL = "车道不能为空";
        String CAMERA_NULL = "摄像头不能为空";
    }
}
