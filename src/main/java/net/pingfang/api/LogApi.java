package net.pingfang.api;

import java.io.IOException;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.LogReqDTO;
import net.pingfang.model.dto.QueryLogListResDTO;
import net.pingfang.service.LogService;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 日志相关API
 *
 * @author: CM
 * @date: 2023/6/5
 */
@RestController
@RequestMapping("/log")
public class LogApi {
    @Autowired
    private LogService logService;

    /**
     * 查询日志列表
     * @param param 入参
     * @return 日志路径
     */
    @PostMapping("/query-log-list")
    public Result<List<QueryLogListResDTO>> queryLogList(@RequestBody LogReqDTO param) throws IOException {
        return logService.queryLogList(param);
    }

    /**
     * 查询
     * @param filePath 日志路径
     * @return 日志base64文件
     */
    @GetMapping("/query-log-file")
    public Result<List<String>> queryLogFile(@RequestParam("filePath") String filePath){
        return logService.queryLogFile(filePath);
    }

    /**
     * 下载
     * @param filePath 日志路径
     * @return 日志base64文件
     */
    @GetMapping("/download-log-file")
    public void downloadLogFile(@RequestParam("filePath") String filePath, HttpServletResponse response){
        logService.downloadLogFile(filePath,response);
    }
}
