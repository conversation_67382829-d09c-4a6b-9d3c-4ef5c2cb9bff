package net.pingfang.api;


import net.pingfang.core.hardware.plc.Plc;
import net.pingfang.model.annotation.Log;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.QueryPlcResDTO;
import net.pingfang.model.dto.UpdatePlcReqDTO;
import net.pingfang.service.PlcService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * PLC相关API
 *
 * @author: CM
 * @date: 2023/6/5
 */
@RestController
@RequestMapping("/plc")
public class PlcApi {

    @Autowired
    private PlcService plcService;

    @PostMapping("/send")
    public Result<String> send(@RequestBody Plc plc){
        return plcService.send(plc);
    }

    /**
     * 查询
     * @return
     */
    @GetMapping("/query")
    public Result<QueryPlcResDTO> query(){
        return plcService.query();
    }

    /**
     * 修改
     * @param param
     * @return
     */
    @PostMapping("/update")
    @Log(2)
    public Result<String> update(@RequestBody UpdatePlcReqDTO param){
        return plcService.update(param);
    }

}
