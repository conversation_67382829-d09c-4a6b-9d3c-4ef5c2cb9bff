package net.pingfang.api;

import java.util.List;
import javax.annotation.Resource;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.AddCommandReqDTO;
import net.pingfang.model.entity.Camera;
import net.pingfang.service.SupplierService;
import net.pingfang.service.external.gkpt.GkptService;
import net.pingfang.service.external.gkpt.dto.SendIdentifyReqDTO;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 相机相关API
 *
 * @author: CM
 * @date: 2023/6/5
 */
@RestController
@RequestMapping("/supplier")
public class SupplierApi {

    @Resource
    private SupplierService supplierService;
    @Resource
    private GkptService gkptService;

    /**
     * 三方调用接口
     *
     * @param reqDTOList
     * @return
     */
    @PostMapping("addCommand")
    public Result<String> addCommand(@RequestBody List<AddCommandReqDTO> reqDTOList) {
        supplierService.addCommand(reqDTOList);
        return Result.success();
    }

    @PostMapping("test")
    public void test(@RequestBody SendIdentifyReqDTO resDTO) {
        gkptService.sendIdentify(resDTO);
    }

}
