package net.pingfang.api;

import net.pingfang.model.common.Result;
import net.pingfang.model.entity.Gps;
import net.pingfang.repository.GpsRepository;
import net.pingfang.util.SnowFlakeUtil;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description: Gps定位数据相关API
 * @title: GpsApi
 * @author: cb
 * @date: 2024-06-12 15:09:04
 * @version: 1.0
 */
@RestController
@RequestMapping("/gps")
@Slf4j
public class GpsApi {

    @Resource
    private GpsRepository gpsRepository;

    /**
     * 新增
     * @param gps 入参
     * @return
     */
    @PostMapping("/save")
    public Result<String> save(@RequestBody Gps gps){
        log.info(gps.toString());
        gps.setId(SnowFlakeUtil.getDefaultSnowFlakeId());
        gpsRepository.add(gps);
        return Result.success();
    }
}
