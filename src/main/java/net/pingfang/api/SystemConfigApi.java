package net.pingfang.api;

import net.pingfang.model.annotation.Log;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.SystemInfoDTO;
import net.pingfang.model.dto.SystemInfoSimpleDTO;
import net.pingfang.model.entity.SystemConfig;
import net.pingfang.service.SystemConfigService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 系统配置相关API
 *
 * @author: CM
 * @date: 2023/6/5
 */
@RestController
@RequestMapping("/system-config")
public class SystemConfigApi {
    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 查询全部
     * @return
     */
    @GetMapping("/query-all")
    public Result<List<SystemConfig>> queryAll(){
        return systemConfigService.queryAll();
    }

    /**
     * 更新
     * @param param 入参
     * @return
     */
    @PostMapping("/update")
    @Log(2)
    public Result<String> update(@RequestBody List<SystemConfig> param){
        return systemConfigService.update(param);
    }

    /**
     * 查询系统信息
     * @return
     */
    @GetMapping("/query-system-info")
    public Result<SystemInfoDTO> querySystemInfo(){
        return systemConfigService.querySystemInfo();
    }
    /**
     * 查询系统信息(运行时间/ip)
     * @return
     */
    @GetMapping("/query-simple-system-info")
    public Result<SystemInfoSimpleDTO> querySimpleSystemInfo(){
        return systemConfigService.querySimpleSystemInfo();
    }



}
