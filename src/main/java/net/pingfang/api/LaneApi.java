package net.pingfang.api;

import net.pingfang.model.annotation.Log;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.QueryRecognizeConfigResDTO;
import net.pingfang.model.entity.Lane;
import net.pingfang.model.entity.RecognizeConfig;
import net.pingfang.service.LaneService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 车道相关API
 *
 * @author: CM
 * @date: 2023/6/5
 */
@RestController
@RequestMapping("/lane")
public class LaneApi {
    @Autowired
    private LaneService laneService;

    /**
     * 查询全部
     * @return
     */
    @GetMapping("/query-all")
    public Result<List<Lane>> queryAll(){
        return laneService.queryAll();
    }

    /**
     * 新增
     * @param lane 入参
     * @return
     */
    @PostMapping("/add")
    @Log(2)
    public Result<String> add(@RequestBody Lane lane){
        return laneService.add(lane);
    }

    /**
     * 更新
     * @param lane 入参
     * @return
     */
    @PostMapping("/update")
    @Log(2)
    public Result<String> update(@RequestBody Lane lane){
        return laneService.update(lane);
    }

    /**
     * 删除
     * @param id 主键
     * @return
     */
    @GetMapping("/delete")
    @Log(2)
    public Result<String> delete(@RequestParam("id") Long id){
        return laneService.delete(id);
    }

    /**
     * 新增/更新配置（批量）
     * @param param 入参
     * @return
     */
    @PostMapping("/add-config-list")
    @Log(2)
    public Result<String> addConfigList(@RequestBody List<RecognizeConfig> param){
        return laneService.addConfigList(param);
    }

    /**
     * 新增/更新配置
     * @param param 入参
     * @return
     */
    @PostMapping("/add-config")
    @Log(2)
    public Result<String> addConfig(@RequestBody RecognizeConfig param){
        return laneService.addConfig(param);
    }

    /**
     * 删除配置
     * @param id 入参
     * @return
     */
    @GetMapping("/delete-config")
    @Log(2)
    public Result<String> deleteConfig(@RequestParam("id") Long id){
        return laneService.deleteConfig(id);
    }

    /**
     * 查询配置
     * @param laneId 通道ID
     * @return
     */
    @GetMapping("/query-config")
    public Result<List<QueryRecognizeConfigResDTO>> queryConfig(@RequestParam("laneId") Long laneId){
        return laneService.queryConfig(laneId);
    }



}
