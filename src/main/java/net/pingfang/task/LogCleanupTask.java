package net.pingfang.task;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 日志文件清理定时任务
 * 
 * <AUTHOR>
 * @date 2025-07-03
 */
@Slf4j
@Component
public class LogCleanupTask {

    @Value("${logging.cleanup.path:D:/log/rmg}")
    private String logBasePath;

    @Value("${logging.cleanup.retentionDays:30}")
    private int retentionDays;

    @Value("${logging.cleanup.enabled:true}")
    private boolean cleanupEnabled;

    /**
     * 每天凌晨2点执行日志清理任务
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupOldLogs() {
        if (!cleanupEnabled) {
            log.debug("日志清理功能已禁用");
            return;
        }

        log.info("开始执行日志清理任务 - 保留天数: {}天, 日志路径: {}", retentionDays, logBasePath);
        
        long startTime = System.currentTimeMillis();
        AtomicInteger deletedFiles = new AtomicInteger(0);
        AtomicLong deletedSize = new AtomicLong(0);

        try {
            File logDir = new File(logBasePath);
            if (!logDir.exists() || !logDir.isDirectory()) {
                log.warn("日志目录不存在或不是目录: {}", logBasePath);
                return;
            }

            // 计算过期时间
            long expireTime = System.currentTimeMillis() - (retentionDays * 24L * 60L * 60L * 1000L);
            
            // 清理各个子目录
            cleanupDirectory(new File(logDir, "app"), expireTime, deletedFiles, deletedSize);
            cleanupDirectory(new File(logDir, "error"), expireTime, deletedFiles, deletedSize);
            cleanupDirectory(new File(logDir, "rtsp"), expireTime, deletedFiles, deletedSize);
            cleanupDirectory(new File(logDir, "plc"), expireTime, deletedFiles, deletedSize);
            cleanupDirectory(new File(logDir, "recognition"), expireTime, deletedFiles, deletedSize);

            long endTime = System.currentTimeMillis();
            log.info("日志清理任务完成 - 删除文件数: {}, 释放空间: {}MB, 耗时: {}ms", 
                    deletedFiles.get(), 
                    deletedSize.get() / (1024 * 1024), 
                    endTime - startTime);

        } catch (Exception e) {
            log.error("日志清理任务执行失败", e);
        }
    }

    /**
     * 清理指定目录中的过期日志文件
     * 
     * @param directory 目录
     * @param expireTime 过期时间戳
     * @param deletedFiles 删除文件计数器
     * @param deletedSize 删除文件大小计数器
     */
    private void cleanupDirectory(File directory, long expireTime, AtomicInteger deletedFiles, AtomicLong deletedSize) {
        if (!directory.exists() || !directory.isDirectory()) {
            return;
        }

        File[] files = directory.listFiles();
        if (files == null) {
            return;
        }

        Arrays.stream(files)
                .filter(File::isFile)
                .filter(file -> isLogFile(file.getName()))
                .filter(file -> file.lastModified() < expireTime)
                .forEach(file -> {
                    try {
                        long fileSize = file.length();
                        String fileName = file.getName();
                        
                        if (file.delete()) {
                            deletedFiles.incrementAndGet();
                            deletedSize.addAndGet(fileSize);
                            log.debug("删除过期日志文件: {} ({}KB)", fileName, fileSize / 1024);
                        } else {
                            log.warn("删除日志文件失败: {}", fileName);
                        }
                    } catch (Exception e) {
                        log.error("删除日志文件异常: {}", file.getName(), e);
                    }
                });
    }

    /**
     * 判断是否为日志文件
     * 
     * @param fileName 文件名
     * @return true-是日志文件，false-不是日志文件
     */
    private boolean isLogFile(String fileName) {
        return fileName.endsWith(".log") || 
               fileName.endsWith(".log.gz") ||
               fileName.matches(".*-\\d{4}-\\d{2}-\\d{2}\\.log") ||
               fileName.matches(".*\\.\\d{4}-\\d{2}-\\d{2}\\.\\d+\\.gz");
    }

    /**
     * 手动执行日志清理（用于测试或紧急清理）
     */
    public void manualCleanup() {
        log.info("手动执行日志清理任务");
        cleanupOldLogs();
    }

    /**
     * 获取日志目录统计信息
     * 
     * @return 统计信息字符串
     */
    public String getLogStatistics() {
        try {
            File logDir = new File(logBasePath);
            if (!logDir.exists()) {
                return "日志目录不存在: " + logBasePath;
            }

            AtomicInteger totalFiles = new AtomicInteger(0);
            AtomicLong totalSize = new AtomicLong(0);

            // 统计各个子目录
            countDirectory(new File(logDir, "app"), totalFiles, totalSize);
            countDirectory(new File(logDir, "error"), totalFiles, totalSize);
            countDirectory(new File(logDir, "rtsp"), totalFiles, totalSize);
            countDirectory(new File(logDir, "plc"), totalFiles, totalSize);
            countDirectory(new File(logDir, "recognition"), totalFiles, totalSize);

            return String.format("日志统计 - 文件数: %d, 总大小: %dMB, 配置保留: %d天", 
                    totalFiles.get(), 
                    totalSize.get() / (1024 * 1024), 
                    retentionDays);

        } catch (Exception e) {
            log.error("获取日志统计信息失败", e);
            return "获取统计信息失败: " + e.getMessage();
        }
    }

    /**
     * 统计目录中的文件数量和大小
     */
    private void countDirectory(File directory, AtomicInteger fileCount, AtomicLong totalSize) {
        if (!directory.exists() || !directory.isDirectory()) {
            return;
        }

        File[] files = directory.listFiles();
        if (files == null) {
            return;
        }

        Arrays.stream(files)
                .filter(File::isFile)
                .filter(file -> isLogFile(file.getName()))
                .forEach(file -> {
                    fileCount.incrementAndGet();
                    totalSize.addAndGet(file.length());
                });
    }

    /**
     * 应用启动时执行一次清理（可选）
     */
    public void startupCleanup() {
        if (cleanupEnabled) {
            log.info("应用启动时执行日志清理检查");
            cleanupOldLogs();
        }
    }
}
