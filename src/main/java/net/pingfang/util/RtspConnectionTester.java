package net.pingfang.util;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.config.RtspConfig;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.Frame;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * RTSP连接测试工具
 * 用于测试相机连接状态和基本信息
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Component
public class RtspConnectionTester {

    @Autowired
    private RtspConfig rtspConfig;

    /**
     * 测试所有配置的RTSP相机连接
     */
    public void testAllCameras() {
        log.info("=== 开始测试所有RTSP相机连接 ===");

        // 从配置文件读取RTSP URL和相机ID
        List<String> rtspUrls = rtspConfig.getUrlList();
        List<Integer> cameraIds = rtspConfig.getCameraIDList();

        // 检查配置是否为空
        if (CollectionUtils.isEmpty(rtspUrls)) {
            log.warn("未配置RTSP URL列表，跳过相机连接测试");
            return;
        }

        if (CollectionUtils.isEmpty(cameraIds)) {
            log.warn("未配置相机ID列表，跳过相机连接测试");
            return;
        }

        // 检查配置数量是否匹配
        if (rtspUrls.size() != cameraIds.size()) {
            log.error("RTSP URL数量({})与相机ID数量({})不匹配，请检查配置文件",
                     rtspUrls.size(), cameraIds.size());
            return;
        }

        log.info("从配置文件读取到{}个RTSP相机配置", rtspUrls.size());

        // 逐个测试相机连接
        for (int i = 0; i < rtspUrls.size(); i++) {
            testSingleCamera(rtspUrls.get(i), cameraIds.get(i));
        }

        log.info("=== RTSP相机连接测试完成 ===");
    }

    /**
     * 显示当前RTSP配置信息
     */
    public void showRtspConfig() {
        log.info("=== 当前RTSP配置信息 ===");

        List<String> rtspUrls = rtspConfig.getUrlList();
        List<Integer> cameraIds = rtspConfig.getCameraIDList();

        if (CollectionUtils.isEmpty(rtspUrls)) {
            log.info("RTSP URL列表: 未配置");
        } else {
            log.info("RTSP URL列表: 共{}个", rtspUrls.size());
            for (int i = 0; i < rtspUrls.size(); i++) {
                log.info("  相机{}: {}",
                        (cameraIds != null && i < cameraIds.size()) ? cameraIds.get(i) : (i + 1),
                        maskPassword(rtspUrls.get(i)));
            }
        }

        if (CollectionUtils.isEmpty(cameraIds)) {
            log.info("相机ID列表: 未配置");
        } else {
            log.info("相机ID列表: {}", cameraIds);
        }

        log.info("抓拍帧率: {}fps", rtspConfig.getFps());
        log.info("延迟结束时间: {}秒", rtspConfig.getEndDelayTime());

        log.info("=== RTSP配置信息显示完成 ===");
    }

    /**
     * 测试单个相机连接
     */
    public boolean testSingleCamera(String rtspUrl, Integer cameraId) {
        log.info("开始测试相机{} - URL: {}", cameraId, maskPassword(rtspUrl));

        FFmpegFrameGrabber grabber = null;
        try {
            long startTime = System.currentTimeMillis();

            // 创建抓取器
            grabber = FFmpegFrameGrabber.createDefault(rtspUrl);

            // 设置连接参数
            grabber.setOption("rtsp_transport", "tcp");  // 强制TCP传输
            grabber.setOption("stimeout", "5000000");    // 连接超时5秒
            grabber.setOption("rw_timeout", "5000000");  // 读写超时5秒
            grabber.setImageWidth(1280);                 // 设置分辨率
            grabber.setImageHeight(720);

            // 尝试连接
            grabber.start();
            long connectTime = System.currentTimeMillis() - startTime;

            // 获取流信息
            int width = grabber.getImageWidth();
            int height = grabber.getImageHeight();
            double frameRate = grabber.getFrameRate();

            log.info("✅ 相机{}连接成功! 连接耗时: {}ms", cameraId, connectTime);
            log.info("   分辨率: {}x{}, 帧率: {}", width, height, frameRate);

            // 尝试抓取一帧测试
            Frame frame = grabber.grabImage();
            if (frame != null && frame.image != null) {
                log.info("   ✅ 成功获取视频帧，帧大小: {}x{}", frame.imageWidth, frame.imageHeight);
                frame.close(); // 释放帧资源
            } else {
                log.warn("   ⚠️ 连接成功但无法获取视频帧");
            }

            return true;

        } catch (Exception e) {
            String errorMsg = e.getMessage();
            log.error("❌ 相机{}连接失败: {}", cameraId, errorMsg);

            // 分析错误类型
            if (errorMsg != null) {
                if (errorMsg.contains("401") || errorMsg.toLowerCase().contains("unauthorized")) {
                    log.error("   原因: 认证失败，请检查用户名密码");
                } else if (errorMsg.contains("404") || errorMsg.toLowerCase().contains("not found")) {
                    log.error("   原因: 流路径不存在，请检查RTSP路径");
                } else if (errorMsg.toLowerCase().contains("timeout")) {
                    log.error("   原因: 连接超时，请检查网络连接和IP地址");
                } else if (errorMsg.toLowerCase().contains("connection refused")) {
                    log.error("   原因: 连接被拒绝，请检查端口号和相机状态");
                } else {
                    log.error("   原因: 未知错误");
                }
            }

            return false;

        } finally {
            // 确保资源释放
            if (grabber != null) {
                try {
                    grabber.stop();
                } catch (Exception e) {
                    log.warn("关闭相机{}连接时出错: {}", cameraId, e.getMessage());
                }
            }
        }
    }

    /**
     * 测试相机连接并获取详细信息
     */
    public CameraInfo getCameraInfo(String rtspUrl, Integer cameraId) {
        FFmpegFrameGrabber grabber = null;
        try {
            grabber = FFmpegFrameGrabber.createDefault(rtspUrl);
            grabber.setOption("rtsp_transport", "tcp");
            grabber.setOption("stimeout", "3000000");

            grabber.start();

            CameraInfo info = new CameraInfo();
            info.setCameraId(cameraId);
            info.setRtspUrl(maskPassword(rtspUrl));
            info.setWidth(grabber.getImageWidth());
            info.setHeight(grabber.getImageHeight());
            info.setFrameRate(grabber.getFrameRate());
            info.setConnected(true);

            return info;

        } catch (Exception e) {
            CameraInfo info = new CameraInfo();
            info.setCameraId(cameraId);
            info.setRtspUrl(maskPassword(rtspUrl));
            info.setConnected(false);
            info.setErrorMessage(e.getMessage());
            return info;

        } finally {
            if (grabber != null) {
                try {
                    grabber.stop();
                } catch (Exception e) {
                    // 忽略关闭错误
                }
            }
        }
    }

    /**
     * 屏蔽URL中的密码信息，用于日志输出
     */
    private String maskPassword(String rtspUrl) {
        if (rtspUrl == null) return null;
        return rtspUrl.replaceAll("://([^:]+):([^@]+)@", "://$1:****@");
    }

    /**
     * 相机信息类
     */
    public static class CameraInfo {
        private Integer cameraId;
        private String rtspUrl;
        private Integer width;
        private Integer height;
        private Double frameRate;
        private Boolean connected;
        private String errorMessage;

        // Getters and Setters
        public Integer getCameraId() { return cameraId; }
        public void setCameraId(Integer cameraId) { this.cameraId = cameraId; }

        public String getRtspUrl() { return rtspUrl; }
        public void setRtspUrl(String rtspUrl) { this.rtspUrl = rtspUrl; }

        public Integer getWidth() { return width; }
        public void setWidth(Integer width) { this.width = width; }

        public Integer getHeight() { return height; }
        public void setHeight(Integer height) { this.height = height; }

        public Double getFrameRate() { return frameRate; }
        public void setFrameRate(Double frameRate) { this.frameRate = frameRate; }

        public Boolean getConnected() { return connected; }
        public void setConnected(Boolean connected) { this.connected = connected; }

        public String getErrorMessage() { return errorMessage; }
        public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }

        @Override
        public String toString() {
            if (connected) {
                return String.format("Camera%d: %s [%dx%d@%.1ffps] - 连接正常",
                                   cameraId, rtspUrl, width, height, frameRate);
            } else {
                return String.format("Camera%d: %s - 连接失败: %s",
                                   cameraId, rtspUrl, errorMessage);
            }
        }
    }
}
