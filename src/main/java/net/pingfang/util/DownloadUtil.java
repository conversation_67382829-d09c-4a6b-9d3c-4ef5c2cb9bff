package net.pingfang.util;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import net.pingfang.model.dto.ExportRecordExcelDTO;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

/**
 * 文件下载工具类
 * <AUTHOR>
 * @since 2023-07-19 11:14
 */
@Slf4j
public class DownloadUtil {

    private int FILE_SIZE = 0;

    /**
     * 下载excel
     * @param fileName 导出文件名
     * @param list 导出数据
     * @param response http
     */
    public static void DownloadExcel(String fileName,List<?> list, HttpServletResponse response) {
        try {
            //响应头
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            response.setCharacterEncoding("UTF-8");
            //数据填充
            ServletOutputStream out = response.getOutputStream();
            EasyExcel.write(response.getOutputStream(), ExportRecordExcelDTO.class).registerWriteHandler(new LongestMatchColumnWidthStyleStrategy()).sheet("sheet").doWrite(list);
        }catch (Exception e){
            log.error("文件下载 error:{}", ExceptionUtil.getStackTrace(e));
        }
    }

    /**
     * 下载并压缩
     * @param zipFileName 压缩包名称
     * @param pathList 文件存储地址
     * @param response http
     */
    public static void DownloadAndCompression(String zipFileName,List<String> pathList, HttpServletResponse response) {
        try {
            //获取文件大小
            int size = 0;
//            for (String path : pathList) {
//                File file = new File(path);
//                if(!file.exists()){
//                    continue;
//                }
//                size+=file.length();
//            }
            //响应头
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(zipFileName.getBytes("GB2312"), "ISO-8859-1"));  // 需要编码否则中文乱码
            response.setContentType("application/zip;charset=utf-8");
            response.setCharacterEncoding("UTF-8");
            //文件大小
            response.setContentLength(size);
            //创建压缩包输出流
            ZipOutputStream zipOut = new ZipOutputStream(response.getOutputStream());
            response.setContentLength(size);
            for (String path : pathList) {
                File file = new File(path);
                if(!file.exists()){
                    continue;
                }
                FileInputStream fileIn = new FileInputStream(file);
                ZipEntry zipEntry = new ZipEntry(file.getName());
                zipOut.putNextEntry(zipEntry);
                byte[] zipBuffer = new byte[1024];
                int zipLength;
                while ((zipLength = fileIn.read(zipBuffer)) > 0) {
                    zipOut.write(zipBuffer, 0, zipLength);
                }
                size +=fileIn.available();
                fileIn.close();
            }
            // 关闭压缩包输出流
            zipOut.close();
        }catch (IOException e){
            log.error("文件下载 error:{}", ExceptionUtil.getStackTrace(e));
        }
    }

    public static String getFileSize(String size) {
        double length = Double.parseDouble(size);
        //如果字节数少于1024，则直接以B为单位，否则先除于1024，后3位因太少无意义
        if (length < 1024) {
            return length + "B";
        } else {
            length = length / 1024.0;
        }
        //如果原字节数除于1024之后，少于1024，则可以直接以KB作为单位
        //因为还没有到达要使用另一个单位的时候
        //接下去以此类推
        if (length < 1024) {
            return Math.round(length * 100) / 100.0 + "KB";
        } else {
            length = length / 1024.0;
        }
        if (length < 1024) {
            //因为如果以MB为单位的话，要保留最后1位小数，
            //因此，把此数乘以100之后再取余
            return Math.round(length * 100) / 100.0 + "MB";
        } else {
            //否则如果要以GB为单位的，先除于1024再作同样的处理
            return Math.round(length / 1024 * 100) / 100.0 + "GB";
        }
    }


}
