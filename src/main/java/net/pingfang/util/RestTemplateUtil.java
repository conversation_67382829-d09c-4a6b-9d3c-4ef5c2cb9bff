package net.pingfang.util;

import net.pingfang.model.common.Result;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

/**
 * restTemplate工具类
 *
 * @Author: CM
 * @Date: 2022/7/5 10:48
 */
@Component
@Slf4j
public class RestTemplateUtil {
    @Autowired
    private RestTemplate restTemplate;



    /**
     * POST请求(不带url参数)-针对响应类型是 text/plain JSON无法自动转换成对象的使用该方法 先接收body字符串，在调用端转换成Result泛型
     *
     * @param url          请求路径
     * @param requestBody  请求body
     * @return
     */
    public String post(String url, Object requestBody) {
        long start = System.currentTimeMillis();
        log.info("POST -->URL [{}], Request:{}", url, JsonUtil.toJson(requestBody));
        String body = null;
        try {
            body = restTemplate.postForEntity(url, requestBody, String.class).getBody();
            log.info("POST -->URL [{}], Response(body):{}", url, JsonUtil.toJson(body));
        } catch (Exception exception) {
            String stackTrace = ExceptionUtil.getStackTrace(exception);
            log.error("POST -->URL [{}], Error:{}", url, stackTrace);
        }
        long end = System.currentTimeMillis();
        return body;
    }

    /**
     * POST请求(不带url参数)-针对响应类型是 text/plain JSON无法自动转换成对象的使用该方法 先接收body字符串，在调用端转换成Result泛型
     *
     * @param url          请求路径
     * @param requestBody  请求body
     * @return
     */
    public String post(String url, String requestBody) {
        long start = System.currentTimeMillis();
        log.info("POST -->URL [{}], Request:{}", url, requestBody);
        String body = null;
        try {
            body = restTemplate.postForEntity(url, requestBody, String.class).getBody();
            log.info("POST -->URL [{}], Response(body):{}", url, JsonUtil.toJson(body));
        } catch (Exception exception) {
            String stackTrace = ExceptionUtil.getStackTrace(exception);
            log.error("POST -->URL [{}], Error:{}", url, stackTrace);
        }
        long end = System.currentTimeMillis();
        return body;
    }

    /**
     * POST请求(不带url参数)
     *
     * @param url          请求路径
     * @param requestBody  请求body残损
     * @param responseType 请求返回数据类型
     * @return
     */
    public <T> T post(String url, Object requestBody, Class<T> responseType) {
        long start = System.currentTimeMillis();
        log.info("POST -->URL [{}], Request:{}", url, JsonUtil.toJson(requestBody));
        T result = null;
        try {
            result = restTemplate.postForEntity(url, requestBody, responseType).getBody();
            log.info("POST -->URL [{}], Response:{}", url, JsonUtil.toJson(result));
        } catch (Exception exception) {
            String stackTrace = ExceptionUtil.getStackTrace(exception);
            log.error("POST -->URL [{}], Error:{}", url, stackTrace);
        }
        long end = System.currentTimeMillis();
        return result;
    }

    /**
     * POST请求(带url参数)
     *
     * @param url          请求路径
     * @param requestBody  请求body残损
     * @param responseType 请求返回数据类型
     * @param requestParam 请求url的参数
     * @return
     */
    public <T> T post(String url, Object requestBody, Class<T> responseType, Map<String, Object> requestParam) {
        long start = System.currentTimeMillis();
        log.info("POST -->URL [{}], URL Param:{},Request:{}", url, JsonUtil.toJson(requestParam), JsonUtil.toJson(requestBody));
        T result = null;
        try {
            result = restTemplate.postForEntity(url, requestBody, responseType, requestParam).getBody();
            log.info("POST -->URL [{}], Response:{}", url, JsonUtil.toJson(result));
            return result;
        } catch (Exception exception) {
            String stackTrace = ExceptionUtil.getStackTrace(exception);
            log.error("POST -->URL [{}], Error:{}", url, stackTrace);
        }
        long end = System.currentTimeMillis();
        return result;
    }

    /**
     * POST请求(不带url参数)
     *
     * @param url          请求路径
     * @param requestBody  请求body残损
     * @param responseType 请求返回数据类型
     * @return
     */
    public <T> T post(String url, Object requestBody, ParameterizedTypeReference<T> responseType) {
        long start = System.currentTimeMillis();
        log.info("POST -->URL [{}], Request:{}", url, JsonUtil.toJson(requestBody));
        T result = null;
        try {
            HttpEntity requestEntity = new HttpEntity(requestBody);
            result = restTemplate.exchange(url,HttpMethod.POST, requestEntity, responseType).getBody();
            log.info("POST -->URL [{}], Response:{}", url, JsonUtil.toJson(result));
        } catch (Exception exception) {
            String stackTrace = ExceptionUtil.getStackTrace(exception);
            log.error("POST -->URL [{}], Error:{}", url, stackTrace);
        }
        long end = System.currentTimeMillis();
        return result;
    }

    /**
     * POST请求(带url参数)
     *
     * @param url          请求路径
     * @param requestBody  请求body残损
     * @param responseType 请求返回数据类型
     * @param requestParam 请求url的参数
     * @return
     */
    public <T> T post(String url, Object requestBody, ParameterizedTypeReference<T> responseType, Map<String, Object> requestParam) {
        long start = System.currentTimeMillis();
        log.info("POST -->URL [{}], URL Param:{},Request:{}", url, JsonUtil.toJson(requestParam), JsonUtil.toJson(requestBody));
        T result = null;
        try {
            HttpEntity requestEntity = new HttpEntity(requestBody);
            result = restTemplate.exchange(url, HttpMethod.POST, requestEntity, responseType, requestParam).getBody();
            log.info("POST -->URL [{}], Response:{}", url, JsonUtil.toJson(result));
            return result;
        } catch (Exception exception) {
            String stackTrace = ExceptionUtil.getStackTrace(exception);
            log.error("POST -->URL [{}], Error:{}", url, stackTrace);
        }
        long end = System.currentTimeMillis();
        return result;
    }

    /**
     * POST请求(不带url参数)-针对响应类型是 text/plain JSON无法自动转换成对象的使用该方法 先接收body字符串，在调用端转换成Result泛型
     * @param url          请求路径
     * @param requestBody  请求body
     * @param httpHeaders  请求头
     * @return
     */
    public String post(String url, Object requestBody, HttpHeaders httpHeaders) {
        long start = System.currentTimeMillis();
        log.info("POST -->URL [{}], Request:{}", url, JsonUtil.toJson(requestBody));
        String body = null;
        try {
            String content = JsonUtil.toJson(requestBody);

            HttpEntity<String> httpEntity = new HttpEntity<>(content, httpHeaders);

            body = restTemplate.postForEntity(url, httpEntity, String.class).getBody();
            log.info("POST -->URL [{}], Response(body):{}", url, JsonUtil.toJson(body));
        } catch (Exception exception) {
            String stackTrace = ExceptionUtil.getStackTrace(exception);
            log.error("POST -->URL [{}], Error:{}", url, stackTrace);
        }
        long end = System.currentTimeMillis();
        return body;
    }

    /**
     * GET请求(无参数)
     *
     * @param url          路径
     * @param responseType 请求返回对象类型
     * @return
     */
    public <T> T get(String url, Class<T> responseType) {
        long start = System.currentTimeMillis();
        log.info("GET -->URL [{}]", url);
        T result = null;
        try {
            result = restTemplate.getForObject(url, responseType);
            log.info("GET -->URL [{}], Response:{}", url, JsonUtil.toJson(result));
            return result;
        } catch (Exception exception) {
            String stackTrace = ExceptionUtil.getStackTrace(exception);
            log.error("GET -->URL [{}], Error:{}", url, stackTrace);
        }
        long end = System.currentTimeMillis();
        return result;
    }

    /**
     * GET请求(带参数)
     *
     * @param url          路径
     * @param requestParam 请求参数
     * @param responseType 请求返回对象类型
     * @return
     */
    public <T> T get(String url, Map<String, Object> requestParam, Class<T> responseType) {
        long start = System.currentTimeMillis();
        log.info("GET -->URL [{}], Request:{}", url, JsonUtil.toJson(requestParam));
        T result = null;
        try {
            result = restTemplate.getForObject(url, responseType, requestParam);
            log.info("GET -->URL [{}], Response:{}", url, JsonUtil.toJson(result));
            return result;
        } catch (Exception exception) {
            String stackTrace = ExceptionUtil.getStackTrace(exception);
            log.error("GET -->URL [{}], Error:{}", url, stackTrace);
        }
        long end = System.currentTimeMillis();
        return result;
    }

    /**
     * GET请求(无参数)
     *
     * @param url          路径
     * @param responseType 请求返回对象类型
     * @return
     */
    public <T> T get(String url, ParameterizedTypeReference<T> responseType) {
        long start = System.currentTimeMillis();
        log.info("GET -->URL [{}]", url);
        T result = null;
        try {
            result = restTemplate.exchange(url, HttpMethod.GET, null, responseType).getBody();
            log.info("GET -->URL [{}], Response:{}", url, JsonUtil.toJson(result));
            return result;
        } catch (Exception exception) {
            String stackTrace = ExceptionUtil.getStackTrace(exception);
            log.error("GET -->URL [{}], Error:{}", url, stackTrace);
        }
        long end = System.currentTimeMillis();
        return result;
    }

    /**
     * GET请求(带参数)
     *
     * @param url          路径
     * @param requestParam 请求参数
     * @param responseType 请求返回对象类型
     * @return
     */
    public <T> T get(String url, Map<String, Object> requestParam, ParameterizedTypeReference<T> responseType) {
        long start = System.currentTimeMillis();
        log.info("GET -->URL [{}], Request:{}", url, JsonUtil.toJson(requestParam));
        T result = null;
        try {
            result = restTemplate.exchange(url, HttpMethod.GET, null, responseType, requestParam).getBody();
            log.info("GET -->URL [{}], Response:{}", url, JsonUtil.toJson(result));
            return result;
        } catch (Exception exception) {
            String stackTrace = ExceptionUtil.getStackTrace(exception);
            log.error("GET -->URL [{}], Error:{}", url, stackTrace);
        }
        long end = System.currentTimeMillis();

        return result;
    }

    /**
     * GET请求(请求头带参数)
     * @param url
     * @param requestParam
     * @return
     */
    public Result get(String url, String requestParam) {
        long start = System.currentTimeMillis();
        log.info("GET -->URL [{}], Request:{}", url, JsonUtil.toJson(requestParam));
        Result result = null;
        try {
            result = restTemplate.getForObject(url+"?gpsUniqueCode="+requestParam,  Result.class);
            log.info("GET -->URL [{}], Response:{}", url, JsonUtil.toJson(result));
            return result;
        } catch (Exception exception) {
            String stackTrace = ExceptionUtil.getStackTrace(exception);
            log.error("GET -->URL [{}], Error:{}", url, stackTrace);
        }
        long end = System.currentTimeMillis();

        return result;
    }

}
