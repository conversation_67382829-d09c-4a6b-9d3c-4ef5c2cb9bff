package net.pingfang.util;

import cn.hutool.core.util.StrUtil;
import java.awt.Color;
import java.awt.Graphics;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Base64;
import java.util.List;
import javax.imageio.ImageIO;
import org.springframework.web.multipart.MultipartFile;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

/**
 * 此工具类用于将文件转为二进制和Base64格式的编码以及解码 base64格式可以起到图像压缩的作用
 * 二进制格式不能起到压缩作用
 * <AUTHOR>
 * @since 2023-04-10 17:04
 */
public class ImageUtil {

    static BASE64Encoder miscEncoder = new BASE64Encoder();
    static BASE64Decoder miscDecoder = new BASE64Decoder();
    static Base64.Encoder utilEncoder = Base64.getEncoder();
    static Base64.Decoder utilDecoder = Base64.getDecoder();

    /**
     * 将图片文件编码为base64格式字符串
     *
     * @param file 图片文件的路径
     * @return 返回编码后的字符串
     */
    public static String getImageBase64(File file) {
        BufferedImage bi;
        ByteArrayOutputStream baos = null;
        try {
            bi = ImageIO.read(file);
            baos = new ByteArrayOutputStream();
            List<String> split = StrUtil.split(file.getName(), ".");

            // 自动截取文件扩展名
            String formatName = split.get(split.size() - 1);

            ImageIO.write(bi, formatName, baos); // jpg
            byte[] bytes = baos.toByteArray();
            return miscEncoder.encodeBuffer(bytes).trim();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (baos != null) {
                try {
                    baos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    /**
     * 将MultipartFile 图片文件编码为base64
     * @param file
     * @return
     * @throws Exception
     */
    public static String generateBase64(MultipartFile file){
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("图片不能为空！");
        }
        String fileName = file.getOriginalFilename();
        String fileType = fileName.substring(fileName.lastIndexOf("."));
        String contentType = file.getContentType();
        byte[] imageBytes = null;
        String base64EncoderImg="";
        try {
            imageBytes = file.getBytes();
            BASE64Encoder base64Encoder =new BASE64Encoder();
            /**
             * 1.Java使用BASE64Encoder 需要添加图片头（"data:" + contentType + ";base64,"），
             *   其中contentType是文件的内容格式。
             * 2.Java中在使用BASE64Enconder().encode()会出现字符串换行问题，这是因为RFC 822中规定，
             *   每72个字符中加一个换行符号，这样会造成在使用base64字符串时出现问题，
             *   所以我们在使用时要先用replaceAll("[\\s*\t\n\r]", "")解决换行的问题。
             */
            base64EncoderImg = "data:" + contentType + ";base64," + base64Encoder.encode(imageBytes);
            base64EncoderImg = base64EncoderImg.replaceAll("[\\s*\t\n\r]", "");
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return base64EncoderImg;
    }

    /**
     * MultipartFile
     * @param multiFile
     * @return
     */
    public static String ImageToBase64(MultipartFile multiFile) {
        // 获取文件名
        String fileName = multiFile.getOriginalFilename();
        // 获取文件后缀
        String prefix = fileName.substring(fileName.lastIndexOf("."));
        // 若需要防止生成的临时文件重复,可以在文件名后添加随机码
        try {
            File file = File.createTempFile(fileName, prefix);
            multiFile.transferTo(file);
            //转换base64
            return getImageBase64(file);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    public static void draw(){
        try {
            BufferedImage image = ImageIO.read(new File("D:\\temp\\20230811\\sea_r_up12.jpg"));
            Graphics g = image.getGraphics();
            g.setColor(Color.RED);//画笔颜色
            g.drawRect(686, 414, 1154-686, 572-414);//矩形框(原点x坐标，原点y坐标，矩形的长，矩形的宽)
            //g.dispose();
            FileOutputStream out = new FileOutputStream("D:\\temp\\20230811\\sea_r_up12_new.jpg");//输出图片的地址
            ImageIO.write(image, "jpg", out);
        }catch (Exception exception){
            exception.printStackTrace();
        }


    }

    public static void main(String[] args) {
        draw();
    }


}
