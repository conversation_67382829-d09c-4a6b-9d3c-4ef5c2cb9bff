package net.pingfang.util;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 轨道吊相关配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "pingfang.picture")
public class PathConfigUtil {

    /**
     * 本地图片地址
     */
    private String localPath;

    /**
     * 吊具简称
     */
    private String rmgName;

    /**
     * iis地址（用于内部文件服务）
     */
    private String iisUrl;

    /**
     * 缩略图大小
     */
    private Integer size;

    /**
     * 前端图片访问配置
     */
    private Frontend frontend = new Frontend();

    /**
     * 前端图片访问配置类
     */
    @Data
    public static class Frontend {
        /**
         * 图片服务器基础URL（用于生成前端访问URL）
         */
        private String baseUrl = "http://localhost:8080";

        /**
         * 图片URL路径前缀
         */
        private String urlPrefix = "img";
    }

}
