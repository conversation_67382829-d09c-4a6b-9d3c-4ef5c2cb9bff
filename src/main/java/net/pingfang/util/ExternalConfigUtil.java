package net.pingfang.util;

/**
 * 第三方接口相关配置
 * <AUTHOR>
 * @since 2024-05-23 10:49
 */

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "external.api")
public class ExternalConfigUtil {

    /**
     * 第三方接口是否开启（true-开启 false-关闭）
     */
    private String sync;

    /**
     * 第三方接口，复位通知是否开启（true-开启 false-关闭）
     */
    private String restorationSync;

    /**
     * 复位通知start
     */
    private Integer restorationStart;

    /**
     * 复位通知end
     */
    private Integer restorationEnd;

    /**
     * 第三方接口：地址
     */
    private String url;

    /**
     * 第三方接口：用户名
     */
    private String user;

    /**
     * 第三方接口：密码
     */
    private String password;

    /**
     * 第三方接口：复位时通知
     */
    private String restorationUrl;

    /**
     * 第三方接口：地址（铁路局）
     */
    private String gkptUrl;
    /**
     * 提交铁路局的桥名
     */
    private String rmgName;

}
