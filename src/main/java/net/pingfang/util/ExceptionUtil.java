package net.pingfang.util;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * @Author: CM
 * @Date: 2021/6/5 14:14
 * @Description:
 */
public class ExceptionUtil {

    /**
     * 获取错误的堆栈信息
     * @param throwable 错误
     * @return 堆栈信息
     */
    public static String getStackTrace(Throwable throwable){
        StringWriter stringWriter=new StringWriter();
        try (PrintWriter printWriter=new PrintWriter(stringWriter)){
            throwable.printStackTrace(printWriter);
            return stringWriter.toString();
        }
    }
}
