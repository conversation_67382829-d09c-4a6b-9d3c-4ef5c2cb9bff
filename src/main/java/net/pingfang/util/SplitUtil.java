package net.pingfang.util;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import org.springframework.util.StringUtils;

/**
 * 切分工具
 *
 * @Author: CM
 * @Date: 2022/7/1 15:52
 */
public class SplitUtil {

    public static final String SPLIT = ",";
    public static final String HORIZONTAL_BAR = "-";

    public static List<String> stringToList(String data){
        if (StringUtils.isEmpty(data)){
            return null;
        }
        if(data.contains(SPLIT)){
            return Arrays.asList(data.split(SPLIT));
        }
        return Arrays.asList(data);
    }

    public static String localDateTimeToString(LocalDateTime createTime){
        if (null == createTime){
            return "";
        }
        String data = createTime.toString();
        if(StringUtils.isEmpty(data)){
            return "";
        }
        data = createTime.toString().replace("T"," ");
        Integer index = data.indexOf(HORIZONTAL_BAR);
        return data.substring(index+1,data.length());
    }

}
