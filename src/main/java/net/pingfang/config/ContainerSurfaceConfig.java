package net.pingfang.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 箱面识别配置
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Component
@ConfigurationProperties(prefix = "container-surface")
@Data
public class ContainerSurfaceConfig {

    /**
     * 是否启用箱面识别
     */
    private boolean enabled = true;

    /**
     * 识别高度范围配置
     */
    private Recognition recognition = new Recognition();

    /**
     * 相机配置
     */
    private Camera camera = new Camera();

    /**
     * 识别配置内部类
     */
    @Data
    public static class Recognition {
        /**
         * 最低识别高度（米）
         */
        private Float minHeight = 9.5f;

        /**
         * 最高识别高度（米）
         */
        private Float maxHeight = 10.5f;

        /**
         * 识别超时时间（秒）
         */
        private Integer timeoutSeconds = 3;

        /**
         * 最大尝试次数
         */
        private Integer maxAttempts = 6;

        /**
         * 抓拍间隔（毫秒）
         */
        private Integer captureIntervalMs = 200;

        /**
         * 获取最低识别高度，带默认值保护
         */
        public Float getMinHeight() {
            return minHeight != null && minHeight > 0 ? minHeight : 9.5f;
        }

        /**
         * 获取最高识别高度，带默认值保护
         */
        public Float getMaxHeight() {
            return maxHeight != null && maxHeight > 0 ? maxHeight : 10.5f;
        }

        /**
         * 验证配置是否合理
         */
        public boolean isValid() {
            return getMinHeight() < getMaxHeight();
        }
    }

    /**
     * 相机配置内部类
     */
    @Data
    public static class Camera {
        /**
         * 相机IP地址
         */
        private String ip = "*************";

        /**
         * 相机控制端口
         */
        private Integer port = 8000;

        /**
         * 用户名
         */
        private String username = "admin";

        /**
         * 密码
         */
        private String password = "pfkj2016";

        /**
         * 通道号
         */
        private Integer channel = 1;

        /**
         * 预置位（0表示不设置预置位）
         */
        private Integer presetLocation = 0;
    }


}
