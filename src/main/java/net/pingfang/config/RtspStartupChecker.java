package net.pingfang.config;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.util.RtspConnectionTester;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * RTSP相机启动检查器
 * 在应用启动时自动检查所有相机连接状态
 *
 * <AUTHOR>
 * @date 2025-06-26
 */
@Slf4j
@Component
@Order(100) // 确保在其他组件初始化后执行
public class RtspStartupChecker implements CommandLineRunner {

    @Autowired
    private RtspConnectionTester connectionTester;

    @Autowired
    private ProjectConfig projectConfig;

    @Override
    public void run(String... args) throws Exception {
        log.info("=== 应用启动 - 开始检查RTSP相机连接状态 ===");

        // 检查是否启用了RTSP预识别
        if (!projectConfig.getIfRtspDetect()) {
            log.info("RTSP预识别功能未启用，跳过相机连接检查");
            return;
        }

        try {
            // 延迟3秒，确保网络和其他服务已就绪
            Thread.sleep(3000);

            // 显示当前配置信息
            connectionTester.showRtspConfig();

            // 执行相机连接测试
            connectionTester.testAllCameras();

            log.info("=== RTSP相机连接检查完成 ===");

        } catch (Exception e) {
            log.error("RTSP相机连接检查时发生异常", e);
        }
    }
}
