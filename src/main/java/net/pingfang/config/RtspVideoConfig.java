package net.pingfang.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * RTSP视频录制配置 - 720P录制
 *
 * <AUTHOR>
 * @date 2025-07-03
 */
@Data
@Component
@ConfigurationProperties(prefix = "rtsp.video")
public class RtspVideoConfig {

    /**
     * 录制配置
     */
    private Recording recording = new Recording();

    /**
     * 视频编码格式
     */
    private String codec = "H264";

    /**
     * 视频比特率（bps）- 720P推荐2Mbps
     */
    private int bitrate = 2000000;

    /**
     * 视频格式
     */
    private String format = "mp4";

    /**
     * 视频帧率
     */
    private int frameRate = 25;

    /**
     * 视频分辨率宽度
     */
    private int width = 1280;

    /**
     * 视频分辨率高度
     */
    private int height = 720;

    /**
     * 录制配置内部类
     */
    @Data
    public static class Recording {
        /**
         * 是否启用视频录制功能
         */
        private boolean enabled = true;

        /**
         * 是否启用JVM关闭钩子（防止异常退出时视频文件损坏）
         */
        private boolean shutdownHook = true;
    }
}
