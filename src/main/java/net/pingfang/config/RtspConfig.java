package net.pingfang.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * rtsp视频流地址配置
 *
 * @title: RtspConfig
 * @author: cb
 * @date: 2025-05-22 17:45
 * @version: 1.0
 */
@Component
@ConfigurationProperties(prefix = "rtsp")
@Data
public class RtspConfig {
    /**
     * rtsp地址
     */
    public List<String> urlList;

    /**
     * url对应的相机list,要求一一对应的关系
     */
    public List<Integer> cameraIDList;

    public Integer fps = 5;

    //延迟endDelayTime秒后结束识别,默认1秒
    public Integer endDelayTime = 1;

    /**
     * 识别高度范围配置
     */
    private Recognition recognition = new Recognition();

    /**
     * 智能结束条件配置
     */
    private EndConditions end = new EndConditions();

    /**
     * 识别配置内部类
     */
    @Data
    public static class Recognition {
        /**
         * 最低识别高度（米）- 低于此高度停止抓拍
         */
        private Float minHeight = 5.0f;

        /**
         * 最高识别高度（米）- 高于此高度不启动抓拍
         */
        private Float maxHeight = 7.5f;

        /**
         * 获取最低识别高度，带默认值保护
         */
        public Float getMinHeight() {
            return minHeight != null && minHeight > 0 ? minHeight : 5.0f;
        }

        /**
         * 获取最高识别高度，带默认值保护
         */
        public Float getMaxHeight() {
            return maxHeight != null && maxHeight > 0 ? maxHeight : 7.5f;
        }

        /**
         * 验证配置是否合理
         */
        public boolean isValid() {
            return getMinHeight() < getMaxHeight();
        }
    }

    /**
     * 智能结束条件配置
     */
    @Data
    public static class EndConditions {
        /**
         * 高可信度结束条件
         */
        private HighConfidence highConfidence = new HighConfidence();

        /**
         * 距离过近结束条件
         */
        private TooClose tooClose = new TooClose();

        /**
         * 超时结束条件
         */
        private Timeout timeout = new Timeout();

        @Data
        public static class HighConfidence {
            /**
             * 是否启用高可信度结束
             */
            private boolean enabled = true;

            /**
             * 可信度阈值
             */
            private float threshold = 0.95f;

            /**
             * 连续识别次数
             */
            private int consecutiveCount = 3;
        }

        @Data
        public static class TooClose {
            /**
             * 是否启用距离过近结束
             */
            private boolean enabled = true;

            /**
             * 高度阈值（米）
             */
            private float heightThreshold = 3.0f;
        }

        @Data
        public static class Timeout {
            /**
             * 是否启用超时结束
             */
            private boolean enabled = true;

            /**
             * 最大识别时间（秒）
             */
            private int maxSeconds = 60;
        }
    }

}
