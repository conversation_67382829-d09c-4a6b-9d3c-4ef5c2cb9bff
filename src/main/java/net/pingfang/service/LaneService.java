package net.pingfang.service;

import net.pingfang.model.common.Result;
import net.pingfang.model.dto.QueryRecognizeConfigResDTO;
import net.pingfang.model.entity.Lane;
import net.pingfang.model.entity.RecognizeConfig;
import java.util.List;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
public interface LaneService {
    Result<List<Lane>> queryAll();

    Result<String> add(Lane lane);

    Result<String> update(Lane lane);

    Result<String> delete(Long id);

    Result<String> addConfigList(List<RecognizeConfig> param);

    Result<String> addConfig(RecognizeConfig param);

    Result<String> deleteConfig(Long id);

    Result<List<QueryRecognizeConfigResDTO>> queryConfig(Long laneId);
}
