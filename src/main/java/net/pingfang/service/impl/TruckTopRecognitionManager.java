package net.pingfang.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.core.algorithm.ailib.TopPlateAlgorithmService;
import net.pingfang.core.algorithm.ailib.model.DetectResults;
import net.pingfang.core.algorithm.ailib.model.RecTopPlate;
import net.pingfang.core.hardware.camera.hikvision.HikvisionService;
import net.pingfang.core.hardware.camera.hikvision.HikvisionServiceFactory;
import net.pingfang.model.dto.EcsIdentifyInfoDTO;
import net.pingfang.model.vo.business.RecognizeConfigVO;
import net.pingfang.repository.RecognizeConfigRepository;

import net.pingfang.service.EcsService;
import net.pingfang.util.DateUtil;
import net.pingfang.util.PathConfigUtil;
import net.pingfang.util.RedisUtil;
import net.pingfang.enums.RecognizeConfigTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import net.pingfang.service.ThreadPoolManagerService;

import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;

/**
 * 车顶号识别管理器
 * 用于处理ECS集卡到位信号，启动车顶号识别流程
 * <AUTHOR>
 */
@Slf4j
@Service
public class TruckTopRecognitionManager {

    @Autowired
    private TopPlateAlgorithmService topPlateAlgorithmService;

    @Autowired
    private HikvisionServiceFactory hikvisionServiceFactory;

    @Autowired
    private RecognizeConfigRepository recognizeConfigRepository;

    @Autowired
    private EcsService ecsService;

    @Autowired
    private PathConfigUtil pathConfigUtil;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ThreadPoolManagerService threadPoolManager;

    @Value("${picture.path:D:/pfkj/img/}")
    private String fileDirPath;

    @Value("${truck-top-recognition.timeout:30}")
    private int recognitionTimeoutSeconds;

    @Value("${truck-top-recognition.interval:2}")
    private int recognitionIntervalSeconds;

    // 存储正在进行的识别任务
    private final ConcurrentHashMap<String, Future<?>> runningTasks = new ConcurrentHashMap<>();

    /**
     * 启动车顶号识别（简化版本，去除限制条件）
     * @param craneNo 门吊号
     * @return 是否启动成功
     */
    public boolean startTruckTopRecognition(String craneNo) {
        try {
            log.info("接收到ECS集卡到位信号，开始启动车顶号识别 - 门吊号: {}", craneNo);

            // 获取车顶号识别摄像头配置
            List<RecognizeConfigVO> cameraConfigs = getCameraConfigs();
            if (CollectionUtils.isEmpty(cameraConfigs)) {
                log.error("未找到车顶号识别摄像头配置，将发送空结果到ECS");
                // 即使没有摄像头配置，也要发送空结果到ECS
                sendEmptyResultToEcs(craneNo);
                return true; // 返回true表示处理了请求
            }

            // 生成唯一的任务ID（使用时间戳）
            String taskId = "TRUCK_TOP_" + System.currentTimeMillis();

            // 检查是否已有正在运行的识别任务（避免重复启动）
            if (runningTasks.containsKey(taskId)) {
                log.warn("任务ID {} 已存在，生成新的任务ID", taskId);
                taskId = "TRUCK_TOP_" + System.currentTimeMillis() + "_" + Math.random();
            }

            log.info("开始启动车顶号识别任务 - 任务ID: {}, 门吊号: {}", taskId, craneNo);

            // 异步启动识别任务
            String finalTaskId = taskId;
            String taskDescription = String.format("车顶号识别任务 - 任务ID:%s, 门吊号:%s", taskId, craneNo);

            Future<?> task = threadPoolManager.submitTruckTopTask(() -> {
                executeSimplifiedRecognitionTask(cameraConfigs, finalTaskId, craneNo);
            }, taskDescription);

            if (task != null) {
                runningTasks.put(taskId, task);
            } else {
                log.error("车顶号识别任务提交失败 - 任务ID: {}", taskId);
                sendEmptyResultToEcs(craneNo);
                return true;
            }
            log.info("车顶号识别任务已启动 - 任务ID: {}", taskId);
            return true;

        } catch (Exception e) {
            log.error("启动车顶号识别失败，错误: {}", e.getMessage(), e);
            // 即使启动失败，也要发送空结果到ECS
            try {
                sendEmptyResultToEcs(craneNo);
            } catch (Exception ex) {
                log.error("发送空结果到ECS也失败了", ex);
            }
            return true; // 返回true表示处理了请求
        }
    }

    /**
     * 执行简化的识别任务（无限制条件）
     * @param cameraConfigs 摄像头配置列表
     * @param taskId 任务ID
     * @param craneNo 门吊号
     */
    private void executeSimplifiedRecognitionTask(List<RecognizeConfigVO> cameraConfigs, String taskId, String craneNo) {
        long startTime = System.currentTimeMillis();
        long timeoutMillis = recognitionTimeoutSeconds * 1000L;
        String recognizedTruckRoofNo = null;
        String bestImagePath = null;

        try {
            log.info("开始执行简化车顶号识别任务 - 任务ID: {}, 门吊号: {}, 超时时间: {}秒",
                    taskId, craneNo, recognitionTimeoutSeconds);

            while (System.currentTimeMillis() - startTime < timeoutMillis && recognizedTruckRoofNo == null) {
                // 检查任务是否被取消
                if (Thread.currentThread().isInterrupted()) {
                    log.info("车顶号识别任务被中断 - 任务ID: {}", taskId);
                    break;
                }

                // 遍历所有摄像头进行识别
                for (RecognizeConfigVO cameraConfig : cameraConfigs) {
                    try {
                        String[] result = recognizeFromCameraSimplified(cameraConfig, taskId);
                        if (result != null && !StringUtils.isEmpty(result[0])) {
                            recognizedTruckRoofNo = result[0];
                            bestImagePath = result[1];
                            log.info("识别到车顶号: {} - 任务ID: {}, 摄像头: {}",
                                    recognizedTruckRoofNo, taskId, cameraConfig.getName());
                            break;
                        }
                    } catch (Exception e) {
                        log.error("摄像头识别异常 - 任务ID: {}, 摄像头: {}, 错误: {}",
                                taskId, cameraConfig.getName(), e.getMessage());
                    }
                }

                // 如果识别到结果，跳出循环
                if (recognizedTruckRoofNo != null) {
                    break;
                }

                // 等待一段时间后继续识别
                try {
                    Thread.sleep(recognitionIntervalSeconds * 1000L);
                } catch (InterruptedException e) {
                    log.info("车顶号识别任务被中断 - 任务ID: {}", taskId);
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            // 发送识别结果到ECS（无论是否识别到结果）
            sendRecognitionResultToEcsSimplified(recognizedTruckRoofNo, bestImagePath, craneNo, taskId);

        } catch (Exception e) {
            log.error("执行车顶号识别任务异常 - 任务ID: {}, 错误: {}", taskId, e.getMessage(), e);
            // 发送空结果到ECS
            sendEmptyResultToEcs(craneNo);
        } finally {
            // 清理任务
            runningTasks.remove(taskId);
            log.info("车顶号识别任务已完成并清理 - 任务ID: {}", taskId);
        }
    }

    /**
     * 执行识别任务（原有方法，保留兼容性）
     * @param cameraConfigs 摄像头配置列表
     * @param seqNo 序列号
     */
    private void executeRecognitionTask(List<RecognizeConfigVO> cameraConfigs, String seqNo) {
        String taskKey = seqNo;
        long startTime = System.currentTimeMillis();
        long timeoutMillis = recognitionTimeoutSeconds * 1000L;
        String recognizedTruckRoofNo = null;

        try {
            log.info("开始执行车顶号识别任务 - seqNo: {}, 超时时间: {}秒", seqNo, recognitionTimeoutSeconds);



            while (System.currentTimeMillis() - startTime < timeoutMillis && recognizedTruckRoofNo == null) {
                // 检查任务是否被取消
                if (Thread.currentThread().isInterrupted()) {
                    log.info("车顶号识别任务被中断 - seqNo: {}", seqNo);
                    break;
                }

                // 遍历所有摄像头进行识别
                for (RecognizeConfigVO cameraConfig : cameraConfigs) {
                    try {
                        String truckRoofNo = recognizeFromCamera(cameraConfig, seqNo);
                        if (!StringUtils.isEmpty(truckRoofNo)) {
                            recognizedTruckRoofNo = truckRoofNo;
                            log.info("识别到车顶号: {} - seqNo: {}, 摄像头: {}",
                                    truckRoofNo, seqNo, cameraConfig.getName());
                            break;
                        }
                    } catch (Exception e) {
                        log.error("摄像头识别异常 - seqNo: {}, 摄像头: {}, 错误: {}",
                                seqNo, cameraConfig.getName(), e.getMessage());
                    }
                }

                // 如果识别到结果，跳出循环
                if (recognizedTruckRoofNo != null) {
                    break;
                }

                // 等待一段时间后继续识别
                try {
                    Thread.sleep(recognitionIntervalSeconds * 1000L);
                } catch (InterruptedException e) {
                    log.info("车顶号识别任务被中断 - seqNo: {}", seqNo);
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            // 将车顶号识别结果关联到seqNo（如果识别到结果）
            if (recognizedTruckRoofNo != null) {
                // 这里需要传递更多信息，包括摄像头信息和图片路径
                // 在recognizeFromCamera方法中已经处理了关联逻辑
                log.info("车顶号识别完成，结果已关联到seqNo - 车顶号: {}, seqNo: {}", recognizedTruckRoofNo, seqNo);
            }

            // 发送识别结果到ECS（包含图片信息）
            sendRecognitionResultToEcs(recognizedTruckRoofNo, seqNo);

        } catch (Exception e) {
            log.error("执行车顶号识别任务异常 - seqNo: {}, 错误: {}", seqNo, e.getMessage(), e);
        } finally {
            // 清理任务状态
            runningTasks.remove(taskKey);

            long duration = (System.currentTimeMillis() - startTime) / 1000;
            log.info("车顶号识别任务完成 - seqNo: {}, 耗时: {}秒, 识别结果: {}",
                    seqNo, duration, recognizedTruckRoofNo != null ? recognizedTruckRoofNo : "未识别到");
        }
    }

    /**
     * 从指定摄像头识别车顶号
     * @param cameraConfig 摄像头配置
     * @param seqNo 序列号
     * @return 识别到的车顶号，如果未识别到返回null
     */
    private String recognizeFromCamera(RecognizeConfigVO cameraConfig, String seqNo) {
        try {
            log.debug("开始从摄像头识别车顶号 - seqNo: {}, 摄像头: {}", seqNo, cameraConfig.getName());

            // 注册摄像头
            HikvisionService hikvisionService = hikvisionServiceFactory.getService(cameraConfig.getIp());
            boolean registerResult = hikvisionService.register(
                    cameraConfig.getIp(),
                    cameraConfig.getControlPort() != null ? cameraConfig.getControlPort() : 8000,
                    cameraConfig.getUsername(),
                    cameraConfig.getPassword()
            );

            if (!registerResult) {
                log.warn("摄像头注册失败 - seqNo: {}, 摄像头: {}", seqNo, cameraConfig.getName());
                return null;
            }

            // 设置预置位
            if (cameraConfig.getPresetLocation() != null && cameraConfig.getPresetLocation() != 0) {
                boolean setPositionResult = hikvisionService.setPosition(
                        cameraConfig.getChannel() != null ? cameraConfig.getChannel() : 1,
                        39,
                        cameraConfig.getPresetLocation()
                );
                if (!setPositionResult) {
                    log.warn("设置预置位失败 - seqNo: {}, 摄像头: {}", seqNo, cameraConfig.getName());
                }
            }

            // 抓拍图片
            String imagePath = captureImage(cameraConfig, seqNo);
            if (StringUtils.isEmpty(imagePath)) {
                log.warn("抓拍图片失败 - seqNo: {}, 摄像头: {}", seqNo, cameraConfig.getName());
                return null;
            }

            // 检测车顶号
            DetectResults detectResults = topPlateAlgorithmService.detPicture(imagePath);
            if (detectResults == null || detectResults.getDet_num() == 0) {
                log.debug("未检测到车顶号 - seqNo: {}, 摄像头: {}", seqNo, cameraConfig.getName());
                return null;
            }

            // 识别车顶号
            RecTopPlate recTopPlate = topPlateAlgorithmService.recPicture(imagePath, detectResults);
            if (recTopPlate != null && !StringUtils.isEmpty(recTopPlate.getTopPlate())) {
                String truckRoofNo = recTopPlate.getTopPlate();
                log.info("识别到车顶号: {} - seqNo: {}, 摄像头: {}, 可信度: {}",
                        truckRoofNo, seqNo, cameraConfig.getName(), recTopPlate.getTrust());

                // 立即关联车顶号识别结果到seqNo（与车牌识别逻辑一致）
                String snapTime = String.valueOf(System.currentTimeMillis());
                associateTruckRoofNoWithSeqNo(truckRoofNo, cameraConfig.getIp(), snapTime, seqNo, imagePath);

                return truckRoofNo;
            }

            return null;

        } catch (Exception e) {
            log.error("从摄像头识别车顶号异常 - seqNo: {}, 摄像头: {}, 错误: {}",
                    seqNo, cameraConfig.getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从指定摄像头识别车顶号（简化版本，不依赖seqNo）
     * @param cameraConfig 摄像头配置
     * @param taskId 任务ID
     * @return 识别结果数组 [车顶号, 图片路径]，如果未识别到返回null
     */
    private String[] recognizeFromCameraSimplified(RecognizeConfigVO cameraConfig, String taskId) {
        try {
            log.debug("开始从摄像头识别车顶号 - 任务ID: {}, 摄像头: {}", taskId, cameraConfig.getName());

            // 注册摄像头
            HikvisionService hikvisionService = hikvisionServiceFactory.getService(cameraConfig.getIp());
            boolean registerResult = hikvisionService.register(
                    cameraConfig.getIp(),
                    cameraConfig.getControlPort() != null ? cameraConfig.getControlPort() : 8000,
                    cameraConfig.getUsername(),
                    cameraConfig.getPassword()
            );

            if (!registerResult) {
                log.warn("摄像头注册失败 - 任务ID: {}, 摄像头: {}", taskId, cameraConfig.getName());
                return null;
            }

            // 设置预置位
            if (cameraConfig.getPresetLocation() != null && cameraConfig.getPresetLocation() != 0) {
                boolean setPositionResult = hikvisionService.setPosition(
                        cameraConfig.getChannel() != null ? cameraConfig.getChannel() : 1,
                        39,
                        cameraConfig.getPresetLocation()
                );
                if (!setPositionResult) {
                    log.warn("设置预置位失败 - 任务ID: {}, 摄像头: {}", taskId, cameraConfig.getName());
                }
            }

            // 抓拍图片
            String imagePath = captureImageSimplified(cameraConfig, taskId);
            if (StringUtils.isEmpty(imagePath)) {
                log.warn("抓拍图片失败 - 任务ID: {}, 摄像头: {}", taskId, cameraConfig.getName());
                return null;
            }

            // 检测车顶号
            DetectResults detectResults = topPlateAlgorithmService.detPicture(imagePath);
            if (detectResults == null || detectResults.getDet_num() == 0) {
                log.debug("未检测到车顶号 - 任务ID: {}, 摄像头: {}", taskId, cameraConfig.getName());
                return null;
            }

            // 识别车顶号
            RecTopPlate recTopPlate = topPlateAlgorithmService.recPicture(imagePath, detectResults);
            if (recTopPlate != null && !StringUtils.isEmpty(recTopPlate.getTopPlate())) {
                String truckRoofNo = recTopPlate.getTopPlate();
                log.info("识别到车顶号: {} - 任务ID: {}, 摄像头: {}, 可信度: {}",
                        truckRoofNo, taskId, cameraConfig.getName(), recTopPlate.getTrust());

                return new String[]{truckRoofNo, imagePath};
            }

            return null;

        } catch (Exception e) {
            log.error("从摄像头识别车顶号异常 - 任务ID: {}, 摄像头: {}, 错误: {}",
                    taskId, cameraConfig.getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 抓拍图片（保存到与CoreFlow一致的统一目录）
     * @param cameraConfig 摄像头配置
     * @param seqNo 序列号
     * @return 图片路径
     */
    private String captureImage(RecognizeConfigVO cameraConfig, String seqNo) {
        try {
            // 获取统一的图片保存目录和门吊号
            String baseDir = getUnifiedImageDirectory();
            String rmgName = getRmgName();

            // 获取日期（格式：yyyyMMdd）
            String dateStr = DateUtil.getDate(DateUtil.TYPE_EIGHT);

            // 构建与CoreFlow一致的目录结构：{baseDir}/{rmgName}/{日期}/{seqNo}/
            String saveDir = baseDir + "/" + rmgName + "/" + dateStr + "/" + seqNo + "/";

            // 生成文件名：车顶号识别_摄像头代码_时间戳.jpg
            String fileName = "truck_top_" + cameraConfig.getCode() + "_" +
                    System.currentTimeMillis() + ".jpg";
            String filePath = saveDir + fileName;

            File file = new File(filePath);

            // 创建目录
            if (!file.getParentFile().exists()) {
                boolean created = file.getParentFile().mkdirs();
                if (!created) {
                    log.warn("创建车顶号图片目录失败: {}", saveDir);
                }
            }

            // 抓拍图片
            HikvisionService hikvisionService = hikvisionServiceFactory.getService(cameraConfig.getIp());
            boolean snapResult = hikvisionService.snapMemory(
                    cameraConfig.getChannel() != null ? cameraConfig.getChannel() : 1,
                    file
            );

            if (snapResult) {
                log.debug("车顶号图片抓拍成功 - seqNo: {}, 摄像头: {}, 路径: {}",
                        seqNo, cameraConfig.getName(), filePath);
                return filePath;
            } else {
                log.warn("车顶号图片抓拍失败 - seqNo: {}, 摄像头: {}",
                        seqNo, cameraConfig.getName());
                return null;
            }

        } catch (Exception e) {
            log.error("车顶号图片抓拍异常 - seqNo: {}, 摄像头: {}, 错误: {}",
                    seqNo, cameraConfig.getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 抓拍图片（简化版本，使用任务ID）
     * @param cameraConfig 摄像头配置
     * @param taskId 任务ID
     * @return 图片路径
     */
    private String captureImageSimplified(RecognizeConfigVO cameraConfig, String taskId) {
        try {
            // 获取统一的图片保存目录和门吊号
            String baseDir = getUnifiedImageDirectory();
            String rmgName = getRmgName();

            // 获取日期（格式：yyyyMMdd）
            String dateStr = DateUtil.getDate(DateUtil.TYPE_EIGHT);

            // 构建目录结构：{baseDir}/truck_top/{rmgName}/{日期}/{taskId}/
            String saveDir = baseDir + "/truck_top/" + rmgName + "/" + dateStr + "/" + taskId + "/";

            // 生成文件名：车顶号识别_摄像头代码_时间戳.jpg
            String fileName = "truck_top_" + cameraConfig.getCode() + "_" +
                    System.currentTimeMillis() + ".jpg";
            String filePath = saveDir + fileName;

            File file = new File(filePath);

            // 创建目录
            if (!file.getParentFile().exists()) {
                boolean created = file.getParentFile().mkdirs();
                if (!created) {
                    log.warn("创建车顶号图片目录失败: {}", saveDir);
                }
            }

            // 抓拍图片
            HikvisionService hikvisionService = hikvisionServiceFactory.getService(cameraConfig.getIp());
            boolean snapResult = hikvisionService.snapMemory(
                    cameraConfig.getChannel() != null ? cameraConfig.getChannel() : 1,
                    file
            );

            if (snapResult) {
                log.debug("车顶号图片抓拍成功 - 任务ID: {}, 摄像头: {}, 路径: {}",
                        taskId, cameraConfig.getName(), filePath);
                return filePath;
            } else {
                log.warn("车顶号图片抓拍失败 - 任务ID: {}, 摄像头: {}",
                        taskId, cameraConfig.getName());
                return null;
            }

        } catch (Exception e) {
            log.error("抓拍车顶号图片异常 - 任务ID: {}, 摄像头: {}, 错误: {}",
                    taskId, cameraConfig.getName(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取车顶号识别摄像头配置
     * @return 摄像头配置列表
     */
    private List<RecognizeConfigVO> getCameraConfigs() {
        try {
            return recognizeConfigRepository.queryWorkConfigByType(null, RecognizeConfigTypeEnum.PLATE_TOP.getValue());
        } catch (Exception e) {
            log.error("获取车顶号识别摄像头配置异常", e);
            return null;
        }
    }

    /**
     * 发送识别结果到ECS
     * @param truckRoofNo 车顶号
     * @param seqNo 序列号，用于获取关联的图片信息
     */
    private void sendRecognitionResultToEcs(String truckRoofNo, String seqNo) {
        try {
            // 构建ECS识别信息
            EcsIdentifyInfoDTO identifyInfo = new EcsIdentifyInfoDTO();
            // 从配置获取门吊号
            String craneNo = getRmgName();
            identifyInfo.setCraneNo(craneNo);
            identifyInfo.setIdentifyType(1); // 设置识别类型为拖车
            identifyInfo.setIdentifyTruckRoofNo(truckRoofNo); // 设置车顶号

            // 获取关联的图片信息
            if (seqNo != null && !seqNo.trim().isEmpty()) {
                List<String> imgUrls = getTruckRoofImageUrls(seqNo);
                if (imgUrls != null && !imgUrls.isEmpty()) {
                    identifyInfo.setImgUrls(imgUrls);
                    log.debug("车顶号识别图片URL已添加 - 车顶号: {}, 图片数量: {}", truckRoofNo, imgUrls.size());
                }
            }

            log.info("开始发送车顶号识别结果到ECS - 车顶号: {}", truckRoofNo);

            // 异步发送到ECS
            String taskDescription = String.format("发送车顶号识别结果到ECS - 车顶号:%s", truckRoofNo);
            threadPoolManager.submitTruckTopTask(() -> {
                try {
                    ecsService.sendIdentifyInfo(identifyInfo);
                    log.info("车顶号识别结果发送到ECS成功 - 车顶号: {}", truckRoofNo);
                } catch (Exception e) {
                    log.error("发送车顶号识别结果到ECS异常 - 车顶号: {}, 错误: {}",
                            truckRoofNo, e.getMessage(), e);
                }
            }, taskDescription);

        } catch (Exception e) {
            log.error("构建ECS识别信息异常 - 车顶号: {}, 错误: {}",
                    truckRoofNo, e.getMessage(), e);
        }
    }

    /**
     * 发送识别结果到ECS（简化版本）
     * @param truckRoofNo 车顶号（可以为null）
     * @param imagePath 图片路径（可以为null）
     * @param craneNo 门吊号
     * @param taskId 任务ID
     */
    private void sendRecognitionResultToEcsSimplified(String truckRoofNo, String imagePath, String craneNo, String taskId) {
        try {
            // 构建ECS识别信息
            EcsIdentifyInfoDTO identifyInfo = new EcsIdentifyInfoDTO();
            identifyInfo.setCraneNo(craneNo);
            identifyInfo.setIdentifyType(1); // 设置识别类型为拖车

            // 设置车顶号（如果为空则设置为null或空字符串）
            identifyInfo.setIdentifyTruckRoofNo(truckRoofNo);

            // 如果有图片路径，转换为URL并添加
            if (!StringUtils.isEmpty(imagePath)) {
                List<String> imgUrls = new ArrayList<>();
                String imageUrl = convertImagePathToUrl(imagePath);
                if (!StringUtils.isEmpty(imageUrl)) {
                    imgUrls.add(imageUrl);
                    identifyInfo.setImgUrls(imgUrls);
                    log.debug("车顶号识别图片URL已添加 - 任务ID: {}, 图片URL: {}", taskId, imageUrl);
                }
            }

            log.info("开始发送车顶号识别结果到ECS - 任务ID: {}, 车顶号: {}, 门吊号: {}",
                    taskId, truckRoofNo != null ? truckRoofNo : "无", craneNo);

            // 异步发送到ECS
            String taskDescription = String.format("发送车顶号识别结果到ECS - 任务ID:%s, 车顶号:%s",
                    taskId, truckRoofNo != null ? truckRoofNo : "无");
            threadPoolManager.submitTruckTopTask(() -> {
                try {
                    ecsService.sendIdentifyInfo(identifyInfo);
                    log.info("车顶号识别结果发送到ECS成功 - 任务ID: {}, 车顶号: {}",
                            taskId, truckRoofNo != null ? truckRoofNo : "无");
                } catch (Exception e) {
                    log.error("发送车顶号识别结果到ECS异常 - 任务ID: {}, 车顶号: {}, 错误: {}",
                            taskId, truckRoofNo != null ? truckRoofNo : "无", e.getMessage(), e);
                }
            }, taskDescription);

        } catch (Exception e) {
            log.error("构建ECS识别信息异常 - 任务ID: {}, 车顶号: {}, 错误: {}",
                    taskId, truckRoofNo != null ? truckRoofNo : "无", e.getMessage(), e);
        }
    }

    /**
     * 发送空结果到ECS
     * @param craneNo 门吊号
     */
    private void sendEmptyResultToEcs(String craneNo) {
        try {
            log.info("发送空车顶号结果到ECS - 门吊号: {}", craneNo);

            // 构建ECS识别信息
            EcsIdentifyInfoDTO identifyInfo = new EcsIdentifyInfoDTO();
            identifyInfo.setCraneNo(craneNo);
            identifyInfo.setIdentifyType(1); // 设置识别类型为拖车
            identifyInfo.setIdentifyTruckRoofNo(null); // 设置为null表示未识别到
            identifyInfo.setImgUrls(new ArrayList<>()); // 空的图片列表

            // 异步发送到ECS
            String taskDescription = String.format("发送空车顶号结果到ECS - 门吊号:%s", craneNo);
            threadPoolManager.submitTruckTopTask(() -> {
                try {
                    ecsService.sendIdentifyInfo(identifyInfo);
                    log.info("空车顶号结果发送到ECS成功 - 门吊号: {}", craneNo);
                } catch (Exception e) {
                    log.error("发送空车顶号结果到ECS异常 - 门吊号: {}, 错误: {}", craneNo, e.getMessage(), e);
                }
            }, taskDescription);

        } catch (Exception e) {
            log.error("构建空车顶号ECS信息异常 - 门吊号: {}, 错误: {}", craneNo, e.getMessage(), e);
        }
    }

    /**
     * 将图片路径转换为URL
     * @param imagePath 图片路径
     * @return 图片URL
     */
    private String convertImagePathToUrl(String imagePath) {
        try {
            if (StringUtils.isEmpty(imagePath)) {
                return null;
            }

            // 如果已经是完整URL，直接返回
            if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
                return imagePath;
            }

            // 获取IIS URL配置
            String iisUrl = pathConfigUtil.getIisUrl();
            if (StringUtils.isEmpty(iisUrl)) {
                log.warn("IIS URL配置为空，无法转换图片路径为URL");
                return null;
            }

            // 处理图片路径，转换为HTTP可访问的URL
            String actualFilePath = imagePath;

            // 如果路径包含项目文件目录，则去除该部分
            String baseDir = getUnifiedImageDirectory();
            if (!StringUtils.isEmpty(baseDir) && actualFilePath.startsWith(baseDir)) {
                actualFilePath = actualFilePath.substring(baseDir.length());
            }

            // 确保路径分隔符正确（Windows -> Unix）
            actualFilePath = actualFilePath.replace("\\", "/");
            if (!actualFilePath.startsWith("/")) {
                actualFilePath = "/" + actualFilePath;
            }

            // 确保IIS URL以/结尾
            if (!iisUrl.endsWith("/")) {
                iisUrl += "/";
            }

            return iisUrl + actualFilePath.substring(1); // 去掉开头的/避免双斜杠

        } catch (Exception e) {
            log.error("转换图片路径为URL失败 - 原路径: {}, 错误: {}", imagePath, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取车顶号识别的图片URL列表
     * @param seqNo 序列号
     * @return 图片URL列表
     */
    private List<String> getTruckRoofImageUrls(String seqNo) {
        try {
            if (redisUtil == null || seqNo == null || seqNo.trim().isEmpty()) {
                return null;
            }

            // 从Redis获取车顶号数据
            String redisKey = "TRUCK_ROOF_DATA:" + seqNo;
            String truckRoofDataJson = redisUtil.get(redisKey);

            if (truckRoofDataJson != null && !truckRoofDataJson.trim().isEmpty()) {
                // 解析车顶号数据
                @SuppressWarnings("unchecked")
                Map<String, Object> truckRoofData = com.alibaba.fastjson.JSON.parseObject(truckRoofDataJson, Map.class);
                String imagePath = (String) truckRoofData.get("imagePath");

                if (imagePath != null && !imagePath.trim().isEmpty()) {
                    // 转换为相对URL
                    String relativeUrl = convertToRelativeUrl(imagePath);
                    if (relativeUrl != null) {
                        List<String> imgUrls = new java.util.ArrayList<>();
                        imgUrls.add(relativeUrl);
                        return imgUrls;
                    }
                }
            }

            return null;
        } catch (Exception e) {
            log.error("获取车顶号识别图片URL异常 - seqNo: {}, 错误: {}", seqNo, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 将绝对路径转换为前端访问URL
     * @param absolutePath 绝对路径
     * @return 前端可直接访问的完整URL，如果转换失败则返回null
     */
    private String convertToRelativeUrl(String absolutePath) {
        try {
            if (absolutePath == null || absolutePath.trim().isEmpty()) {
                return null;
            }

            // 获取基础图片目录
            String baseDir = getUnifiedImageDirectory();
            if (baseDir == null || baseDir.trim().isEmpty()) {
                return null;
            }

            // 标准化路径分隔符
            String normalizedAbsolutePath = absolutePath.replace("\\", "/");
            String normalizedBaseDir = baseDir.replace("\\", "/");

            // 确保基础目录以/结尾
            if (!normalizedBaseDir.endsWith("/")) {
                normalizedBaseDir += "/";
            }

            // 如果绝对路径以基础目录开头，则去掉基础目录前缀
            if (normalizedAbsolutePath.startsWith(normalizedBaseDir)) {
                String relativeUrl = normalizedAbsolutePath.substring(normalizedBaseDir.length());
                // 构建完整的前端访问URL
                return buildFrontendAccessUrl(relativeUrl);
            } else {
                // 如果不是以基础目录开头，尝试提取文件名
                int lastSlashIndex = normalizedAbsolutePath.lastIndexOf("/");
                if (lastSlashIndex >= 0 && lastSlashIndex < normalizedAbsolutePath.length() - 1) {
                    String fileName = normalizedAbsolutePath.substring(lastSlashIndex + 1);
                    return buildFrontendAccessUrl(fileName);
                }
            }

            return null;
        } catch (Exception e) {
            log.error("转换车顶号图片路径为相对URL失败 - 路径: {}, 错误: {}", absolutePath, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建前端访问URL
     * @param relativePath 相对路径
     * @return 前端可直接访问的完整URL
     */
    private String buildFrontendAccessUrl(String relativePath) {
        try {
            if (relativePath == null || relativePath.trim().isEmpty()) {
                return null;
            }

            // 获取基础URL和URL前缀配置
            String baseUrl = getFrontendBaseUrl();
            String urlPrefix = getImageUrlPrefix();

            // 构建完整URL
            StringBuilder urlBuilder = new StringBuilder();

            // 添加基础URL
            if (baseUrl != null && !baseUrl.trim().isEmpty()) {
                urlBuilder.append(baseUrl);
                if (!baseUrl.endsWith("/")) {
                    urlBuilder.append("/");
                }
            }

            // 添加URL前缀
            if (urlPrefix != null && !urlPrefix.trim().isEmpty()) {
                if (urlPrefix.startsWith("/")) {
                    urlPrefix = urlPrefix.substring(1);
                }
                urlBuilder.append(urlPrefix);
                if (!urlPrefix.endsWith("/")) {
                    urlBuilder.append("/");
                }
            }

            // 添加相对路径
            if (relativePath.startsWith("/")) {
                relativePath = relativePath.substring(1);
            }

            // 转换门吊号格式：TRMG01 -> rmg1, TRMG02 -> rmg2
            relativePath = convertRmgNameInPath(relativePath);

            urlBuilder.append(relativePath);

            return urlBuilder.toString();
        } catch (Exception e) {
            log.error("构建前端访问URL失败 - 相对路径: {}, 错误: {}", relativePath, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换路径中的门吊号格式
     * @param path 原始路径
     * @return 转换后的路径
     */
    private String convertRmgNameInPath(String path) {
        try {
            if (path == null || path.trim().isEmpty()) {
                return path;
            }

            // 不进行门吊号格式转换，保持原始的TRMG01格式
            // 因为Nginx配置和文件存储都使用TRMG01格式
            return path;
        } catch (Exception e) {
            log.error("转换路径中门吊号格式失败 - 路径: {}, 错误: {}", path, e.getMessage(), e);
            return path;
        }
    }

    /**
     * 获取前端基础URL配置
     * @return 前端基础URL，如果未配置则返回默认值
     */
    private String getFrontendBaseUrl() {
        try {
            // 优先使用专门的前端图片服务器配置
            if (pathConfigUtil != null && pathConfigUtil.getFrontend() != null) {
                String frontendBaseUrl = pathConfigUtil.getFrontend().getBaseUrl();
                if (frontendBaseUrl != null && !frontendBaseUrl.trim().isEmpty()) {
                    // 去掉末尾的斜杠
                    if (frontendBaseUrl.endsWith("/")) {
                        frontendBaseUrl = frontendBaseUrl.substring(0, frontendBaseUrl.length() - 1);
                    }
                    return frontendBaseUrl;
                }
            }

            // 如果前端配置为空，回退到IIS地址
            String iisUrl = pathConfigUtil.getIisUrl();
            if (iisUrl != null && !iisUrl.trim().isEmpty()) {
                // 去掉末尾的斜杠
                if (iisUrl.endsWith("/")) {
                    iisUrl = iisUrl.substring(0, iisUrl.length() - 1);
                }
                return iisUrl;
            }

            return "http://localhost:8080"; // 默认值
        } catch (Exception e) {
            log.error("获取前端基础URL配置异常", e);
            return "http://localhost:8080";
        }
    }

    /**
     * 获取图片URL前缀配置
     * @return URL前缀，如果未配置则返回默认值
     */
    private String getImageUrlPrefix() {
        try {
            // 优先使用专门的前端图片URL前缀配置
            if (pathConfigUtil != null && pathConfigUtil.getFrontend() != null) {
                String urlPrefix = pathConfigUtil.getFrontend().getUrlPrefix();
                if (urlPrefix != null && !urlPrefix.trim().isEmpty()) {
                    return urlPrefix.trim();
                }
            }

            // 默认使用img作为URL前缀
            return "img";
        } catch (Exception e) {
            log.error("获取图片URL前缀配置异常", e);
            return "img";
        }
    }

    /**
     * 检查是否在CoreFlow流程中
     * @return 如果在流程中返回true，否则返回false
     */
    private boolean isInCoreFlowProcess() {
        try {
            if (redisUtil == null) {
                log.warn("Redis工具未初始化，无法检查CoreFlow流程状态");
                return false;
            }

            // 检查是否有当前活跃的seqNo
            String currentSeqNo = redisUtil.get("CURRENT_SEQ_NO");
            if (currentSeqNo != null && !currentSeqNo.trim().isEmpty()) {
                // 进一步检查流程状态（可选）
                String processStatus = redisUtil.get("PROCESS_STATUS:" + currentSeqNo.trim());
                if (processStatus != null && ("LOCKING".equals(processStatus) || "UNLOCKING".equals(processStatus) || "ACTIVE".equals(processStatus))) {
                    log.debug("当前在CoreFlow流程中 - seqNo: {}, 状态: {}", currentSeqNo, processStatus);
                    return true;
                } else if (processStatus == null) {
                    // 如果没有具体状态，但有seqNo，也认为在流程中
                    log.debug("当前在CoreFlow流程中 - seqNo: {}", currentSeqNo);
                    return true;
                }
            }
            log.debug("当前不在CoreFlow流程中");
            return false;
        } catch (Exception e) {
            log.error("检查CoreFlow流程状态异常", e);
            return false;
        }
    }

    /**
     * 获取当前活跃的seqNo（从Redis缓存中获取）
     * @return 当前活跃的seqNo，如果没有则返回null
     */
    private String getCurrentActiveSeqNo() {
        try {
            if (redisUtil == null) {
                log.warn("Redis工具未初始化，无法获取当前seqNo");
                return null;
            }

            // 从Redis获取当前活跃的seqNo
            String currentSeqNo = redisUtil.get("CURRENT_SEQ_NO");
            if (currentSeqNo != null && !currentSeqNo.trim().isEmpty()) {
                log.debug("获取到当前活跃seqNo: {}", currentSeqNo);
                return currentSeqNo.trim();
            } else {
                log.debug("当前没有活跃的seqNo");
                return null;
            }
        } catch (Exception e) {
            log.error("获取当前活跃seqNo异常", e);
            return null;
        }
    }

    /**
     * 获取统一的图片保存目录（与CoreFlow一致）
     * @return 统一图片目录路径
     */
    private String getUnifiedImageDirectory() {
        try {
            if (pathConfigUtil != null) {
                // 使用PathConfigUtil获取图片保存路径
                String imagePath = pathConfigUtil.getLocalPath();
                if (imagePath != null && !imagePath.trim().isEmpty()) {
                    return imagePath.trim();
                }
            }

            // 如果无法获取配置，使用默认路径
            return fileDirPath != null ? fileDirPath : "D:/pfkj/img";
        } catch (Exception e) {
            log.error("获取统一图片目录异常", e);
            return fileDirPath != null ? fileDirPath : "D:/pfkj/img";
        }
    }

    /**
     * 获取门吊号（与CoreFlow一致）
     * @return 门吊号
     */
    private String getRmgName() {
        try {
            if (pathConfigUtil != null) {
                // 使用PathConfigUtil获取门吊号
                String rmgName = pathConfigUtil.getRmgName();
                if (rmgName != null && !rmgName.trim().isEmpty()) {
                    return rmgName.trim();
                }
            }

            // 如果无法获取配置，使用默认门吊号
            return "402";
        } catch (Exception e) {
            log.error("获取门吊号异常", e);
            return "402";
        }
    }

    /**
     * 将车顶号识别结果关联到seqNo（与车牌识别逻辑一致）
     * @param truckRoofNo 车顶号
     * @param cameraIp 摄像头IP
     * @param snapTime 抓拍时间
     * @param seqNo 序列号
     * @param imagePath 图片路径
     */
    private void associateTruckRoofNoWithSeqNo(String truckRoofNo, String cameraIp, String snapTime,
                                               String seqNo, String imagePath) {
        try {
            if (redisUtil == null) {
                log.warn("Redis工具未初始化，无法关联车顶号与seqNo");
                return;
            }

            // 构建车顶号数据（与车牌识别数据结构保持一致，移除冗余的craneNo）
            Map<String, Object> truckRoofData = new HashMap<>();
            truckRoofData.put("truckRoofNo", truckRoofNo);
            truckRoofData.put("cameraIp", cameraIp);
            truckRoofData.put("snapTime", snapTime);
            truckRoofData.put("seqNo", seqNo);
            truckRoofData.put("imagePath", imagePath);
            truckRoofData.put("recognizeTime", System.currentTimeMillis());
            truckRoofData.put("recognizeTimeStr", DateUtil.getTime(System.currentTimeMillis()));

            if (seqNo != null && !seqNo.trim().isEmpty()) {
                // 如果有seqNo，将车顶号信息关联到该seqNo（与车牌识别逻辑一致）
                String redisKey = "TRUCK_ROOF_DATA:" + seqNo;

                // 将车顶号数据存储到Redis，设置过期时间为1小时
                redisUtil.setEx(redisKey, com.alibaba.fastjson.JSON.toJSONString(truckRoofData), 1, TimeUnit.HOURS);

                // 同时存储车顶号到seqNo的映射，便于后续查询
                String truckRoofToSeqKey = "TRUCK_ROOF_TO_SEQ:" + truckRoofNo;
                redisUtil.setEx(truckRoofToSeqKey, seqNo, 1, TimeUnit.HOURS);

                log.info("车顶号识别结果已关联到seqNo - 车顶号: {}, seqNo: {}", truckRoofNo, seqNo);
            } else {
                // 如果没有seqNo，将车顶号信息存储为待关联状态（与车牌识别逻辑一致）
                String pendingKey = "PENDING_TRUCK_ROOF:" + truckRoofNo + ":" + System.currentTimeMillis();
                redisUtil.setEx(pendingKey, com.alibaba.fastjson.JSON.toJSONString(truckRoofData), 30, TimeUnit.MINUTES);

                log.info("车顶号识别结果已存储为待关联状态 - 车顶号: {}", truckRoofNo);
            }

        } catch (Exception e) {
            log.error("关联车顶号与seqNo异常 - 车顶号: {}, seqNo: {}, 错误: {}", truckRoofNo, seqNo, e.getMessage(), e);
        }
    }

    /**
     * 取消所有正在运行的任务（在应用关闭时调用）
     */
    public void cancelAllRunningTasks() {
        if (!runningTasks.isEmpty()) {
            log.info("取消{}个正在运行的车顶号识别任务", runningTasks.size());
            for (Map.Entry<String, Future<?>> entry : runningTasks.entrySet()) {
                try {
                    Future<?> task = entry.getValue();
                    if (!task.isDone()) {
                        task.cancel(true);
                        log.debug("已取消任务: {}", entry.getKey());
                    }
                } catch (Exception e) {
                    log.warn("取消任务异常 - 任务ID: {}, 错误: {}", entry.getKey(), e.getMessage());
                }
            }
            runningTasks.clear();
        }
    }

    /**
     * 获取线程池状态（用于监控和调试）
     */
    public String getThreadPoolStatus() {
        return threadPoolManager.getTruckTopPoolStatus();
    }

}
