package net.pingfang.service.impl;

import net.pingfang.constant.ErrorMessageConstant.LaneErrorMessage;
import net.pingfang.core.flow.CoreFlow;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.QueryRecognizeConfigResDTO;
import net.pingfang.model.entity.Lane;
import net.pingfang.model.entity.RecognizeConfig;
import net.pingfang.model.vo.business.RecognizeConfigVO;
import net.pingfang.repository.LaneRepository;
import net.pingfang.repository.RecognizeConfigRepository;
import net.pingfang.service.LaneService;
import net.pingfang.util.SnowFlakeUtil;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
@Service
@Slf4j
public class LaneServiceImpl implements LaneService {
    @Autowired
    private LaneRepository laneRepository;
    @Autowired
    private RecognizeConfigRepository recognizeConfigRepository;

    @Override
    public Result<List<Lane>> queryAll() {
        List<Lane> result = laneRepository.queryAll();
        return Result.success(result);
    }

    @Override
    public Result<String> add(Lane lane) {
        lane.setId(SnowFlakeUtil.getDefaultSnowFlakeId());
        laneRepository.add(lane);
        return Result.success();
    }

    @Override
    public Result<String> update(Lane lane) {
        laneRepository.update(lane);
        return Result.success();
    }

    @Override
    public Result<String> delete(Long id) {
        laneRepository.delete(id);
        return Result.success();
    }

    @Override
    public Result<String> addConfigList(List<RecognizeConfig> param) {
        if (CollectionUtils.isEmpty(param)){
            return Result.success();
        }

        // 1.删除数据
        List<Long> idList = param.stream().map(RecognizeConfig::getId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(idList)){
            recognizeConfigRepository.deleteExId(idList);
        }


        // 2.新增数据
        param.forEach(item -> {
                    item.setId(SnowFlakeUtil.getDefaultSnowFlakeId());
                }
        );
        recognizeConfigRepository.addBatch(param);
        List<RecognizeConfigVO> rclist = recognizeConfigRepository.queryAll();
        if (!CollectionUtils.isEmpty(rclist)){
            CoreFlow.rcMap = rclist.stream().collect(Collectors.groupingBy(item->item.getType()+"_"+item.getLaneId()));

        }

        return Result.success();
    }

    @Override
    public Result<String> addConfig(RecognizeConfig param) {
        Long id = param.getId();
        Long laneId = param.getLaneId();
        Long cameraId = param.getCameraId();
        if(null == laneId){
            return Result.fail(LaneErrorMessage.LANE_NULL);
        }
        if(null == cameraId){
            return Result.fail(LaneErrorMessage.CAMERA_NULL);
        }
        //新增或修改
        if(null == id){
            recognizeConfigRepository.add(param);
        }else{
            recognizeConfigRepository.updateById(param);
        }
        List<RecognizeConfigVO> rclist = recognizeConfigRepository.queryAll();
        if (!CollectionUtils.isEmpty(rclist)){
            CoreFlow.rcMap = rclist.stream().collect(Collectors.groupingBy(item->item.getType()+"_"+item.getLaneId()));
        }
        return Result.success();
    }

    @Override
    public Result<String> deleteConfig(Long id) {
        if(null == id){
            return Result.fail(LaneErrorMessage.CONFIG_NULL);
        }
        recognizeConfigRepository.deleteById(id);
        List<RecognizeConfigVO> rclist = recognizeConfigRepository.queryAll();
        if (!CollectionUtils.isEmpty(rclist)){
            CoreFlow.rcMap = rclist.stream().collect(Collectors.groupingBy(item->item.getType()+"_"+item.getLaneId()));
        }
        return Result.success();
    }

    @Override
    public Result<List<QueryRecognizeConfigResDTO>> queryConfig(Long laneId) {
        List<QueryRecognizeConfigResDTO> result = recognizeConfigRepository.queryByLaneId(laneId);
        return Result.success(result);
    }
}
