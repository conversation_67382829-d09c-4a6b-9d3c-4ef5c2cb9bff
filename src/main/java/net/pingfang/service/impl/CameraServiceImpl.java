package net.pingfang.service.impl;

import net.pingfang.core.hardware.camera.hikvision.HikvisionService;
import net.pingfang.core.hardware.camera.hikvision.HikvisionServiceFactory;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.CameraControlDTO;
import net.pingfang.model.dto.CameraPrePositionControlDTO;
import net.pingfang.model.dto.CamersToneDTO;
import net.pingfang.model.entity.Camera;
import net.pingfang.model.entity.RecognizeConfig;
import net.pingfang.repository.CameraRepository;
import net.pingfang.repository.RecognizeConfigRepository;
import net.pingfang.service.CameraService;
import net.pingfang.util.SnowFlakeUtil;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
@Service
@Slf4j
public class CameraServiceImpl implements CameraService {
    @Autowired
    private CameraRepository cameraRepository;
    @Autowired
    private HikvisionServiceFactory hikvisionServiceFactory;

    @Autowired
    private RecognizeConfigRepository recognizeConfigRepository;

    private int DEFAULT_PORT = 8000;

    @Override
    public Result<String> add(Camera camera) {

        camera.setId(SnowFlakeUtil.getDefaultSnowFlakeId());
        cameraRepository.add(camera);

        return Result.success();
    }

    @Override
    public Result<List<Camera>> queryAll() {
        List<Camera> cameraList = cameraRepository.queryAll();
        return Result.success(cameraList);
    }

    @Override
    public Result<String> update(Camera camera) {

        cameraRepository.update(camera);

        return Result.success();
    }

    @Override
    public Result<String> delete(Long id) {
        cameraRepository.delete(id);
        return Result.success();
    }

    @Override
    public Result<String> control(CameraControlDTO param) {
        Camera camera = cameraRepository.queryById(param.getId());
        // 摄像头持续抓拍识别
        // 注册摄像头
        HikvisionService hikvisionService = hikvisionServiceFactory.getService(camera.getIp());
        boolean registerResult = hikvisionService.register(camera.getIp(), camera.getControlPort(), camera.getUsername(), camera.getPassword());
        if (!registerResult) {
            return Result.fail("摄像头注册失败");
        }
        // TODO 暂时默认通道1,后面放页面配置
        hikvisionService.control(1, param.getCommand(), param.getSpeed(), param.getTime());
        return Result.success();
    }

    @Override
    public Result<String> restart(Long id) {
        Camera camera = cameraRepository.queryById(id);

        // 摄像头持续抓拍识别
        // 注册摄像头
        HikvisionService hikvisionService = hikvisionServiceFactory.getService(camera.getIp());
        boolean registerResult = hikvisionService.register(camera.getIp(), camera.getControlPort(), camera.getUsername(), camera.getPassword());
        if (!registerResult) {
            return Result.fail("摄像头注册失败");
        }
        boolean result = hikvisionService.restart();

        return result ? Result.success() : Result.fail("重启失败");
    }

    @Override
    public Result<String> controlPrePosition(CameraPrePositionControlDTO param) {
        if (param.getType() == null || param.getCommand() == null || param.getLaneId() == null) {
            return Result.fail("预置位类型或指令类型或车道为空");
        }
        Camera camera = cameraRepository.queryById(param.getId());

        // 1.设置预置位
        if (param.getCommand() == 1) {
            // 注册摄像头
            HikvisionService hikvisionService = hikvisionServiceFactory.getService(camera.getIp());
            boolean registerResult = hikvisionService.register(camera.getIp(), camera.getControlPort(), camera.getUsername(), camera.getPassword());
            if (!registerResult) {
                return Result.fail("摄像头注册失败");
            }
            // 更新到车道配置中
            // 查询当前设置的最大预置位值
            RecognizeConfig recognizeConfig = recognizeConfigRepository.queryConfig(param.getLaneId(), camera.getId(), param.getType());
            if (recognizeConfig != null ) {
                if (recognizeConfig.getPresetLocation() == null || recognizeConfig.getPresetLocation() == 0){
                    recognizeConfigRepository.updatePreset(param.getLaneId(), camera.getId(), param.getType());
                    recognizeConfig = recognizeConfigRepository.queryConfig(param.getLaneId(), camera.getId(), param.getType());
                }

                boolean result = hikvisionService.setPosition(camera.getChannel() == null ? 1 : camera.getChannel(), 8, recognizeConfig.getPresetLocation());
                return result ? Result.success() : Result.fail("设置失败");
            }

        }
        // 2.调用相机预置位
        if (param.getCommand() == 2) {
            // 注册摄像头
            HikvisionService hikvisionService = hikvisionServiceFactory.getService(camera.getIp());
            boolean registerResult = hikvisionService.register(camera.getIp(), camera.getControlPort(), camera.getUsername(), camera.getPassword());
            if (!registerResult) {
                return Result.fail("摄像头注册失败");
            }
            RecognizeConfig recognizeConfig = recognizeConfigRepository.queryConfig(param.getLaneId(), camera.getId(), param.getType());
            if (recognizeConfig != null && recognizeConfig.getPresetLocation() != null && recognizeConfig.getPresetLocation() != 0) {
                boolean result = hikvisionService.setPosition(camera.getChannel() == null ? 1 : camera.getChannel(), 39, recognizeConfig.getPresetLocation());
                return result ? Result.success() : Result.fail("设置失败");
            }
        }

        // 3.全部相机转到预置位
        if (param.getCommand() == 3) {
            if (param.getLaneId() == null) {
                return Result.fail("车道不能为空");
            }
            List<Camera> cameraList = cameraRepository.queryAll();
            if (CollectionUtils.isEmpty(cameraList)) {
                return Result.fail("摄像头列表为空");
            }
            boolean result = true;
            for (Camera cam : cameraList) {
                // 注册摄像头
                HikvisionService hikvisionService = hikvisionServiceFactory.getService(cam.getIp());
                boolean registerResult = hikvisionService.register(cam.getIp(), cam.getControlPort(), cam.getUsername(), cam.getPassword());
                if (!registerResult) {
                    return Result.fail("摄像头注册失败");
                }
                RecognizeConfig recognizeConfig = recognizeConfigRepository.queryConfig(param.getLaneId(), cam.getId(), param.getType());
                if (recognizeConfig != null && recognizeConfig.getPresetLocation() != null && recognizeConfig.getPresetLocation() != 0) {
                    result = hikvisionService.setPosition(camera.getChannel() == null ? 1 : camera.getChannel(), 39, recognizeConfig.getPresetLocation());
                }
                if (!result) {
                    break;
                }
            }
            return result ? Result.success() : Result.fail("设置失败");
        }
        return Result.fail("指令类型无法识别");
    }

    @Override
    public Result<String> tone(CamersToneDTO param) {
        return Result.fail("暂不支持调整色温");

        /*Camera camera = cameraRepository.queryById(param.getId());
        HikvisionService hikvisionService = hikvisionServiceFactory.getService(camera.getIp());
        // 注册摄像头
        boolean registerResult = hikvisionService.register(camera.getIp(), camera.getControlPort(), camera.getUsername(), camera.getPassword());
        if (!registerResult) {
            return Result.fail("摄像头注册失败");
        }
        // 摄像头设置色调
        boolean result = hikvisionService.tone(param.getDwDeviceNum(), param.getByColorType(),param.getByScale());
        return result?Result.success():Result.fail("设置失败");*/
    }

    @Override
    public Result<List<RecognizeConfig>> queryPresetConfig(Long laneId, Long cameraId) {

        List<RecognizeConfig> result = recognizeConfigRepository.queryPresetConfig(laneId, cameraId);

        return Result.success(result);
    }
}
