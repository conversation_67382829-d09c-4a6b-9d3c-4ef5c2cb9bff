package net.pingfang.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.enums.*;
import net.pingfang.model.dto.ImgResDTO;
import net.pingfang.model.dto.message.MessageIdentifyDTO;
import net.pingfang.model.dto.message.MessageImgDTO;
import net.pingfang.model.dto.message.MessageLockDTO;
import net.pingfang.model.dto.message.MessageUnLockDTO;
import net.pingfang.model.entity.*;
import net.pingfang.model.vo.message.MessageOneVO;
import net.pingfang.model.vo.message.MessageThreeVO;
import net.pingfang.model.vo.message.MessageTwoVO;
import net.pingfang.model.vo.message.child.*;
import net.pingfang.repository.ImgRepository;
import net.pingfang.repository.RecognitionRepository;
import net.pingfang.service.IdentifyService;
import net.pingfang.util.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 报文-业务处理
 * 注意事项：发送给理货平台的数据要跟应用组之前发的一致，理货系统才能做到改动最小；
 * <AUTHOR>
 * @Date 2023/9/12 16:41
 * @Description:
*/
@Slf4j
@Service
public class IdentifyServiceImpl implements IdentifyService {

    /**
     * 上锁
     */
    private String LOCK_URL = "/identify/lock";
    /**
     * 识别
     */
    private String IDENTIFY_URL = "/identify/identify";
    /**
     * 解锁
     */
    private String UN_LOCK_URL = "/identify/unLock";
    /**
     * 图片
     */
    private String IMG_URL = "/identify/receiveImg";
    /*
    * 上传识别结果到安全系统
    */
   private String WORK_URL = "/ocr/receive-workrecord";
   /*
    * 上传图片到安全系统
    */
   private String WORK_IMG_URL = "/ocr/receive-WorkImage";
    /**
     * 理货平台登录header
     */
    private String HEADERS = "Authorization";
    private String HEADERS_BASIC = "Basic ";

    @Value("${picture.path:D:/pfkj/img/}")
    public String fileDirPath;

    @Autowired
    private PathUtil pathUtil;
    @Autowired
    private PathConfigUtil pathConfigUtil;
    @Autowired
    private RestTemplateUtil restTemplateUtil;

    @Autowired
    private ImgRepository imgRepository;
  
    @Autowired
    private RecognitionRepository recognitionRepository;




    @Override
    public void lock(MessageLockDTO lockDTO) {
        MessageTwoVO twoVO = new MessageTwoVO();
        //赋值
        twoVO.setSeq_no(lockDTO.getSeqNo());
        twoVO.setPasstime(lockDTO.getPasstime());
        twoVO.setWork_type(WorkTypeEnum.getNetValueByValue(lockDTO.getWorkType()));
        twoVO.setContainer_type(lockDTO.getContainerType());
        twoVO.setCrane_num(pathConfigUtil.getRmgName());
        twoVO.setMessage_type(MessageTypeEnum.LOCK.getType());

        //可空
        twoVO.setArea_num("");
        twoVO.setServiceID("");
        twoVO.setCmd_type("start");
        twoVO.setBay_result(new BayResultVO());

        //PLC
        PlcDataVO plcDataVo = new PlcDataVO();
        plcDataVo.setX(Double.valueOf("0"));
        plcDataVo.setY(Double.valueOf("0"));
        twoVO.setPlc_data(plcDataVo);

        //发送
        HttpHeaders httpHeaders = this.headers();
        String url = pathUtil.getTallyPath()+LOCK_URL;
        restTemplateUtil.post(url,twoVO,httpHeaders);
    }

    @Override
    public void identify(MessageIdentifyDTO identifyDTO) {
        String seqNo = identifyDTO.getSeqNo();
        List<Long> idList = identifyDTO.getIdList();

        //图片处理
        List<Img> imgList = new ArrayList<>();
        if(StringUtils.isEmpty(seqNo) && !CollectionUtils.isEmpty(idList)){
            imgList = imgRepository.queryByRecordIds(idList);
        }else{
            imgList = imgRepository.queryBySeqNo(seqNo);
        }

        Map<String, List<Img>> imgMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(imgList) && imgList.size() > 0){
            imgMap = imgList.stream().collect(Collectors.groupingBy(Img::getSeqNo));
        }
        //识别数据处理
        List<Record> recordList = recognitionRepository.queryByIds(idList,seqNo);
        Map<String,List<Record>> recordMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(recordList) && recordList.size() > 0){
            recordMap = recordList.stream().collect(Collectors.groupingBy(Record::getSeqNo));
        }
        //数据整合以及发送
        HttpHeaders httpHeaders = this.headers();

        for (Map.Entry<String,List<Record>> headKey : recordMap.entrySet()){
            String currentSeqNo = headKey.getKey();
            List<Record> currentRecordList = headKey.getValue();
            if(StringUtils.isEmpty(currentSeqNo) || CollectionUtils.isEmpty(currentRecordList) || currentRecordList.size() == 0){
                continue;
            }
            List<Img> imgs = imgMap.containsKey(currentSeqNo) ? imgMap.get(currentSeqNo) : new ArrayList<>();
            //获取识别数据
            MessageThreeVO threeVO = setIdentifyMsg(currentSeqNo,imgs,currentRecordList.get(0));
            if(null == threeVO){
                continue;
            }
            //上传作业信息
            try {
//                log.info("识别报文：seq_no:[{}];信息:{}",seqNo,threeVO);
                String url = pathUtil.getTallyPath()+IDENTIFY_URL;
                restTemplateUtil.post(url,threeVO,httpHeaders);
            }catch (Exception e){
                log.error("同步识别数据异常：{}", ExceptionUtil.getStackTrace(e));
            }
        }
    }

    @Override
    public void unLock(MessageUnLockDTO unLockDTO) {
        MessageOneVO oneVO = new MessageOneVO();
        //赋值
        oneVO.setSeq_no(unLockDTO.getSeqNo());
        oneVO.setPasstime(unLockDTO.getPasstime());
        oneVO.setWork_type(WorkTypeEnum.getNetValueByValue(unLockDTO.getWorkType()));
        oneVO.setContainer_type(unLockDTO.getContainerType());
        oneVO.setCrane_num(pathConfigUtil.getRmgName());
        oneVO.setMessage_type(MessageTypeEnum.UNLOCK.getType());

        //可空
        oneVO.setArea_num("");
        oneVO.setServiceID("");
        oneVO.setCmd_type("stop");
        oneVO.setBay_result(new BayResultVO());

        //PLC
        PlcDataVO plcDataVo = new PlcDataVO();
        plcDataVo.setX(Double.valueOf("0"));
        plcDataVo.setY(Double.valueOf("0"));
        oneVO.setPlc_data(plcDataVo);

        //发送  20230919164811113
        HttpHeaders httpHeaders = this.headers();
        String url = pathUtil.getTallyPath()+UN_LOCK_URL;
        restTemplateUtil.post(url,oneVO,httpHeaders);
    }

    @Override
    public void img(MessageImgDTO imgDTO) {
        List<ImgResDTO> imgResDTOList = new ArrayList<>();

        List<Img> imgList = imgRepository.queryByRecordIds(imgDTO.getIdList());
        if (CollectionUtils.isEmpty(imgList) && imgList.size() == 0){
            return;
        }
        //整合数据、导出
        for (Img img : imgList) {
            File file = new File(pathConfigUtil.getLocalPath()+img.getImgUrl());//todo 待完善
            if(!file.exists()){
                continue;
            }
            ImgResDTO imgRes = new ImgResDTO();
            imgRes.setImgPath(img.getImgUrl());
            imgRes.setImgData(ImageUtil.getImageBase64(file));
            imgResDTOList.add(imgRes);
        }
        //发送
        if(!CollectionUtils.isEmpty(imgList) && imgList.size() > 0){
            HttpHeaders httpHeaders = this.headers();
            String url = pathUtil.getTallyPath()+IMG_URL;
            restTemplateUtil.post(url,imgResDTOList,httpHeaders);
        }
    }

    /**
     * 识别数据整合
     * @param seqNo 唯一需要
     * @param imgs 图片数据
     * @param record 识别数据集合
     * @return
     */
    private MessageThreeVO setIdentifyMsg(String seqNo,List<Img> imgs,Record record){
        Integer containerType = record.getContainerType();

        MessageThreeVO threeVO = new MessageThreeVO();
        //公共数据
        threeVO.setSeq_no(seqNo);
        threeVO.setWork_type(WorkTypeEnum.getNetValueByValue(record.getWorkType()));
        threeVO.setMessage_type(MessageTypeEnum.IDENTIFY.getType());
        threeVO.setPasstime(LocalDateTime.now().toString());
        threeVO.setCrane_num(pathConfigUtil.getRmgName());
        threeVO.setContainer_type(containerType);

        threeVO.setArea_num("");//场站
        threeVO.setState("0");

        threeVO.setQcLockTime("");
        threeVO.setQcUnLockTime("");

        //箱号识别数据节点
        CcrResultVO ccrResultVO = new CcrResultVO();
        ccrResultVO.setConta_weight(StringUtils.isEmpty(record.getWeight()) ? 0 : Integer.parseInt(record.getWeight()));
        //图片对象
        FileInfoVO fileInfoVO = new FileInfoVO();
        if(!CollectionUtils.isEmpty(imgs) && imgs.size() > 0){
            File file;
            List<String> locations = new ArrayList<>();
            List<String> imgPaths = new ArrayList<>();
            for (Img img : imgs) {
                if(StringUtils.isEmpty(img.getImgUrl())){
                    continue;
                }
                //判断文件是否存在
                file = new File(fileDirPath+img.getImgUrl());
                if(!file.exists()){
                    continue;
                }

                locations.add(img.getCameraName());
                imgPaths.add(img.getImgUrl().substring(img.getImgUrl().lastIndexOf("/")+1,img.getImgUrl().length()));// TODO 后面要查摄像头的名称
            }
            fileInfoVO.setImg_path_name(imgPaths);
            fileInfoVO.setLocation(locations);
            fileInfoVO.setImg_num(imgs.size());
            fileInfoVO.setSnap_img_type(ImgTypeEnum.CONTAINER.getValue());
        }
        ccrResultVO.setFile_info(fileInfoVO);
        //箱号相关信息
        List<String> doorDirs = new ArrayList<>();
        List<String> doorLocks = new ArrayList<>();
        List<String> dangerous = new ArrayList<>();
        List<ContaResultVO> contaResults = new ArrayList<>();
        ContaResultVO contaResultVO;
        if(ContainerTypeEnum.SL.getValue().equals(containerType) || ContainerTypeEnum.SS.getValue().equals(containerType) || ContainerTypeEnum.TD.getValue().equals(containerType)){
            contaResultVO = new ContaResultVO();
            contaResultVO.setId(record.getCtnNoA());
            contaResultVO.setUpdateId(record.getCtnNoA());
            contaResultVO.setTrust(!StringUtils.isEmpty(record.getTrustA()) ? Double.parseDouble(record.getTrustA()) : 0);
            contaResultVO.setNote(record.getPlateLocationA());
            contaResultVO.setIso(record.getIsoNoA());
            contaResultVO.setUpdate_top_plate(!StringUtils.isEmpty(record.getPlateNumberA()) ? record.getPlateNumberA() : "");//todo 车号取值待定
            contaResultVO.setLane_num(record.getLaneNumA());//todo 车道号待定
            contaResults.add(contaResultVO);

            doorDirs.add(record.getDoorDirA());
            doorLocks.add(IsSealNoEnum.getCodeDescOneByCode(record.getSealA()));
            dangerous.add(IsDangerousLableEnum.getCodeDescOneByValue(record.getDangerousLableA()));
        }
        if(ContainerTypeEnum.TD.getValue().equals(containerType)){
            contaResultVO = new ContaResultVO();
            contaResultVO.setId(record.getCtnNoB());
            contaResultVO.setUpdateId(record.getCtnNoB());
            contaResultVO.setTrust(!StringUtils.isEmpty(record.getTrustB()) ? Double.parseDouble(record.getTrustB()) : 0);
            contaResultVO.setNote(record.getPlateLocationB());
            contaResultVO.setIso(record.getIsoNoB());
            contaResultVO.setUpdate_top_plate(!StringUtils.isEmpty(record.getPlateNumberA()) ? record.getPlateNumberA() : "");//todo 车号取值待定
            contaResultVO.setLane_num(record.getLaneNumA());//todo 车道号待定
            contaResults.add(contaResultVO);

            doorDirs.add(record.getDoorDirB());
            doorLocks.add(IsSealNoEnum.getCodeDescOneByCode(record.getSealB()));
            dangerous.add(IsDangerousLableEnum.getCodeDescOneByValue(record.getDangerousLableB()));
        }
        ccrResultVO.setConta_result(contaResults);
        ccrResultVO.setDoor_dir(doorDirs);
        ccrResultVO.setDoor_lock(doorLocks);
        ccrResultVO.setDangerous(dangerous);
        threeVO.setCcr_result(ccrResultVO);


        //车号识别数据节点
        TpplateResultVO tpplateResultVO = new TpplateResultVO();
        tpplateResultVO.setCar_dir("");

        TpResultVO tpResultVO = new TpResultVO();
        tpResultVO.setTop_plate("");
        tpResultVO.setUpdate_top_plate("");
        tpResultVO.setNote("");
        tpplateResultVO.setTp_result(tpResultVO);

        FileInfoVO tpFileInfoVO = new FileInfoVO();
        tpFileInfoVO.setLocation(new ArrayList<>());
        tpFileInfoVO.setImg_path_name(new ArrayList<>());
        tpplateResultVO.setFile_info(tpFileInfoVO);

        threeVO.setTpplate_result(tpplateResultVO);


        //残损数据
        DamagedResultVO damagedResultVO = new DamagedResultVO();

        FileInfoVO damagedFileInfoVO = new FileInfoVO();
        damagedFileInfoVO.setLocation(new ArrayList<>());
        damagedFileInfoVO.setImg_path_name(new ArrayList<>());
        damagedResultVO.setFile_info(damagedFileInfoVO);

        threeVO.setDamaged_result(damagedResultVO);


        //bay位相机节点
        BayResultVO bayResultVo = new BayResultVO();
        FileInfoVO bayFileInfoVO = new FileInfoVO();
        bayFileInfoVO.setLocation(new ArrayList<>());
        bayFileInfoVO.setImg_path_name(new ArrayList<>());

        bayResultVo.setFile_info(bayFileInfoVO);
        threeVO.setBay_result(bayResultVo);


        //车牌相机数据节点
        PlateResultVO plateResultVo = new PlateResultVO();
        FileInfoVO plateFileInfoVO = new FileInfoVO();
        plateFileInfoVO.setLocation(new ArrayList<>());
        plateFileInfoVO.setImg_path_name(new ArrayList<>());

        plateResultVo.setFile_info(plateFileInfoVO);
        threeVO.setPlate_result(plateResultVo);


        //plc数据节点
        PlcDataVO plcDataVo = new PlcDataVO();
        plcDataVo.setX(Double.valueOf("0"));
        plcDataVo.setY(Double.valueOf("0"));
        threeVO.setPlc_data(plcDataVo);


        return threeVO;
    }

    /**
     * 设置请求头信息
     * @return
     */
    private HttpHeaders headers(){
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.add(HEADERS,HEADERS_BASIC+Base64.getEncoder().encodeToString(("pfkjnet:pfkjnet2022").getBytes(StandardCharsets.UTF_8)));
        return httpHeaders;
    }

	@Override
	public void workRecord(Record recordSync) {
		WorkRecord workRecord = new WorkRecord();
        workRecord.setLaneNumA(recordSync.getLaneNumA());
        workRecord.setLaneNumB(recordSync.getLaneNumB());
		workRecord.setId(recordSync.getId());
		workRecord.setCraneNo(pathConfigUtil.getRmgName());
		workRecord.setSeqNo(recordSync.getSeqNo());
        if (!StringUtils.isEmpty(recordSync.getLaneNumA()) && !StringUtils.isEmpty(recordSync.getLaneNumB())) {
            recordSync.setLaneNumA(LaneUtils.normalizeLane(recordSync.getLaneNumA()));
            recordSync.setLaneNumB(LaneUtils.normalizeLane(recordSync.getLaneNumB()));
            workRecord.setWorkType(SafeWorkTypeEnum.getValueByLanes(recordSync.getLaneNumA(), recordSync.getLaneNumB()).get());
        }
		workRecord.setCtnNo(recordSync.getCtnNoA());
		workRecord.setIso(recordSync.getIsoNoA());
		workRecord.setPlateNumber(recordSync.getPlateNumberA());
		workRecord.setTopPlateNumber(recordSync.getTopPlateA());
		workRecord.setTrainNo(null);
		workRecord.setTrainModel(null);
		workRecord.setTrainSeq(null);
		workRecord.setStationTrack(null);
		workRecord.setYardPosition(null);
		workRecord.setLockTime(recordSync.getLockTime());
		workRecord.setUnLockTime(recordSync.getUnlockTime());
		
		List<WorkRecordImage> submitImgList = new ArrayList<>();
		
		List<Img> imgList = imgRepository.queryBySeqNo(recordSync.getSeqNo());
		
		List<ImageDTO> imgResDTOList = new ArrayList<>();
		
		if (!CollectionUtils.isEmpty(imgList) && imgList.size() > 0) {
            String imgUrl = "";
            File file;
            for (Img img : imgList) {
                if (null == img) {
                    continue;
                }
                imgUrl = img.getImgUrl();
                if (StringUtils.isEmpty(imgUrl)) {
                    continue;
                }
                //判断文件是否存在
                String filePath = fileDirPath + imgUrl;
                log.info("上传安全平台图片路径：{}",filePath);
                file = new File(filePath);
                if (file.exists()) {
                	WorkRecordImage workRecordImage = new WorkRecordImage();
                	workRecordImage.setId(null);
                	workRecordImage.setImgUrl(imgUrl);
                	workRecordImage.setWorkrecordId(workRecord.getId());
                	submitImgList.add(workRecordImage);
                	ImageDTO imgRes = new ImageDTO();
                    imgRes.setImgUrl(img.getImgUrl());
                    imgRes.setImageData(ImageToBase64Util.imgToBase64(filePath));
                    imgResDTOList.add(imgRes);
                }
            }
        }
		workRecordImg(imgResDTOList);
		workRecord.setImgList(submitImgList);
	   // HttpHeaders httpHeaders = new HttpHeaders();
	    String url = pathUtil.getTallyPath()+WORK_URL;
	    //restTemplateUtil.post(url,workRecord);
	     RestTemplate restTemplate = new RestTemplate();
		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.valueOf(MediaType.APPLICATION_JSON_VALUE));
//		log.info("上传平台作业地址：{}", url);
		log.info("上传平台作业报文：{}", JsonUtil.toJson(workRecord));
		try {
			HttpEntity<String> entity = new HttpEntity<String>(JsonUtil.toJson(workRecord), headers);
			ResponseEntity<String> respMsg = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
//			log.info("上传安全作业返回信息：{}", respMsg);
		} catch (Exception e) {
			log.info("上传安全作业数据失败：{}", e.getMessage());
		}		
	}

	@Override
	public void workRecordImg(List<ImageDTO> imgList) {
		//发送
        if(!CollectionUtils.isEmpty(imgList) && imgList.size() > 0){
           // HttpHeaders httpHeaders = new HttpHeaders();
            String url = pathUtil.getTallyPath()+WORK_IMG_URL;
            log.info("上传平台作业图片地址：{}", url);
            for (ImageDTO imageDTO : imgList) {
            	// restTemplateUtil.post(url,imgList,httpHeaders);
                RestTemplate restTemplate = new RestTemplate();
        		HttpHeaders headers = new HttpHeaders();
        		headers.setContentType(MediaType.valueOf(MediaType.APPLICATION_JSON_VALUE));
        		log.info("上传平台作业图片地址：{}", url);
        		try {
        			HttpEntity<String> entity = new HttpEntity<String>(JsonUtil.toJson(imageDTO), headers);
        			ResponseEntity<String> respMsg = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
        			log.info("上传安全作业图片返回信息：{}", respMsg);
        		} catch (Exception e) {
        			log.info("上传安全作业图片数据失败：{}", e.getMessage());
        		}	
			}        
        }
	}
}
