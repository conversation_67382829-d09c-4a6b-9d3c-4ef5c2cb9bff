package net.pingfang.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.model.dto.HeartBeatDTO;
import net.pingfang.service.HeartBeatService;
import net.pingfang.util.PathConfigUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PreDestroy;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 心跳服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class HeartBeatServiceImpl implements HeartBeatService, CommandLineRunner {

    @Autowired
    private PathConfigUtil pathConfigUtil;

    @Autowired
    @Qualifier("heartBeatRestTemplate")
    private RestTemplate restTemplate;

    /**
     * ECS系统基础URL
     */
    @Value("${ecs.api.url:http://localhost:8080}")
    private String ecsBaseUrl;

    /**
     * ECS API认证信息
     */
    @Value("${ecs.api.auth:Basic b2NyQ3JhbmU6MTIzNDU2}")
    private String ecsApiAuth;

    /**
     * 心跳上报间隔时间（秒）
     */
    @Value("${pingfang.heartbeat.interval:2}")
    private int heartBeatInterval;

    /**
     * 是否启用心跳功能
     */
    @Value("${pingfang.heartbeat.enabled:true}")
    private boolean heartBeatEnabled;

    /**
     * 心跳值计数器，从1-255循环累加
     */
    private final AtomicInteger heartBeatCounter = new AtomicInteger(1);

    /**
     * 定时任务执行器
     */
    private final ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread thread = new Thread(r, "HeartBeat-Thread");
        thread.setDaemon(true);
        return thread;
    });

    /**
     * 心跳-任务-Future
     */
    private ScheduledFuture<?> heartBeatTask;

    /**
     * 心跳服务运行状态
     */
    private volatile boolean isRunning = false;

    /**
     * Spring Boot启动时自动启动心跳
     */
    @Override
    public void run(String... args) throws Exception {
        if (heartBeatEnabled) {
            log.debug("心跳功能已启用，将在系统启动后开始心跳上报");
            // 延迟5秒启动，确保系统完全启动
            scheduler.schedule(this::startHeartBeat, 5, TimeUnit.SECONDS);
        } else {
            log.debug("心跳功能已禁用");
        }
    }

    /**
     * 启动心跳上报
     */
    @Override
    public void startHeartBeat() {
        if (isRunning) {
            log.debug("心跳服务已经在运行中");
            return;
        }

        try {
            log.debug("开始启动心跳上报服务...");

            // 启动定时任务，每2秒执行一次
            heartBeatTask = scheduler.scheduleAtFixedRate(
                this::executeHeartBeat,
                0, // 立即开始
                heartBeatInterval,
                TimeUnit.SECONDS
            );

            isRunning = true;
            log.debug("心跳上报服务启动成功，间隔时间: {}秒", heartBeatInterval);

        } catch (Exception e) {
            log.error("启动心跳上报服务失败", e);
            isRunning = false;
        }
    }

    /**
     * 停止心跳上报
     */
    @Override
    public void stopHeartBeat() {
        if (!isRunning) {
            log.debug("心跳服务未在运行中");
            return;
        }

        try {
            log.debug("正在停止心跳上报服务...");

            if (heartBeatTask != null && !heartBeatTask.isCancelled()) {
                heartBeatTask.cancel(false);
            }

            isRunning = false;
            log.debug("心跳上报服务已停止");

        } catch (Exception e) {
            log.error("停止心跳上报服务失败", e);
        }
    }



    /**
     * 执行心跳上报
     */
    private boolean executeHeartBeat() {
        try {
            log.debug("开始执行心跳上报...");

            // 获取系统名称（门吊号）
            String systemName = getSystemName();
            log.debug("获取到系统名称: {}", systemName);

            // 获取当前心跳值并递增
            int currentHeartBeat = getNextHeartBeatValue();
            log.debug("当前心跳值: {}", currentHeartBeat);

            // 构建心跳数据
            HeartBeatDTO heartBeatData = new HeartBeatDTO(systemName, currentHeartBeat);
            log.debug("构建心跳数据: {}", heartBeatData);

            // 检查RestTemplate是否可用
            if (restTemplate == null) {
                log.error("RestTemplate未初始化！");
                return false;
            }

            // 发送心跳请求
            boolean success = sendHeartBeatToEcs(heartBeatData);

            if (success) {
                log.debug("心跳上报成功 - 系统: {}, 心跳值: {}", systemName, currentHeartBeat);
            } else {
                log.debug("心跳上报失败 - 系统: {}, 心跳值: {}", systemName, currentHeartBeat);
            }

            return success;

        } catch (Exception e) {
            log.error("执行心跳上报异常", e);
            return false;
        }
    }

    /**
     * 向ECS系统发送心跳数据
     */
    private boolean sendHeartBeatToEcs(HeartBeatDTO heartBeatData) {
        try {
            // 构建请求URL
            String url = ecsBaseUrl + "/common/heartBeat";
            log.debug("准备发送心跳请求 - URL: {}", url);

            // 设置请求头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            // 添加ECS认证头（与ECS上报接口保持一致）
            headers.add("Authorization", ecsApiAuth);
            log.debug("设置请求头: {}", headers);

            // 创建请求实体
            HttpEntity<HeartBeatDTO> requestEntity = new HttpEntity<>(heartBeatData, headers);
            log.debug("创建请求实体 - 数据: {}", heartBeatData);

            // 发送POST请求
            log.debug("开始发送POST请求...");
            ResponseEntity<String> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                requestEntity,
                String.class
            );

            // 检查响应状态
            log.debug("收到响应 - 状态码: {}, 响应体: {}", response.getStatusCode(), response.getBody());
            if (response.getStatusCode().is2xxSuccessful()) {
                log.debug("心跳请求发送成功 - URL: {}, 响应: {}", url, response.getBody());
                return true;
            } else {
                log.debug("心跳请求响应异常 - URL: {}, 状态码: {}, 响应: {}",
                    url, response.getStatusCode(), response.getBody());
                return false;
            }

        } catch (Exception e) {
            log.error("发送心跳请求异常 - ECS URL: {}, 心跳数据: {}, 错误: {}",
                ecsBaseUrl, heartBeatData, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取系统名称（门吊号）
     * 返回格式：龙门吊OCR(TRMG01)
     */
    private String getSystemName() {
        try {
            String trmgCode = "TRMG01"; // 默认值

            if (pathConfigUtil != null) {
                String rmgName = pathConfigUtil.getRmgName();
                if (rmgName != null && !rmgName.trim().isEmpty()) {
                    String cleanRmgName = rmgName.trim().toLowerCase();

                    // 处理不同格式的门吊号
                    if (cleanRmgName.startsWith("rmg")) {
                        // 如果是 "rmg1", "rmg2" 格式，提取数字部分
                        String numberPart = cleanRmgName.substring(3);
                        if (numberPart.matches("\\d+")) {
                            int rmgNumber = Integer.parseInt(numberPart);
                            if (rmgNumber < 10) {
                                trmgCode = "TRMG0" + rmgNumber;
                            } else {
                                trmgCode = "TRMG" + rmgNumber;
                            }
                        }
                    } else if (cleanRmgName.matches("\\d+")) {
                        // 如果是纯数字格式，如 "1", "2", "402"
                        int rmgNumber = Integer.parseInt(cleanRmgName);
                        if (rmgNumber < 10) {
                            trmgCode = "TRMG0" + rmgNumber;
                        } else {
                            trmgCode = "TRMG" + rmgNumber;
                        }
                    } else if (cleanRmgName.startsWith("trmg")) {
                        // 如果已经是 "trmg01", "trmg02" 格式，直接转换为大写
                        trmgCode = cleanRmgName.toUpperCase();
                    } else {
                        // 如果无法解析，记录警告并使用原值构造
                        log.debug("无法解析门吊号格式: {}, 使用默认值", rmgName);
                        trmgCode = "TRMG01";
                    }
                }
            }

            // 返回完整的系统名称格式：龙门吊OCR(TRMG01)
            return "龙门吊OCR(" + trmgCode + ")";

        } catch (Exception e) {
            log.debug("获取系统名称异常，使用默认值 - 门吊号: {}, 错误: {}",
                pathConfigUtil != null ? pathConfigUtil.getRmgName() : "null", e.getMessage());
            return "龙门吊OCR(TRMG01)";
        }
    }

    /**
     * 获取下一个心跳值（1-255循环累加）
     */
    private int getNextHeartBeatValue() {
        int current = heartBeatCounter.getAndIncrement();

        // 如果超过255，重置为1
        if (current > 255) {
            heartBeatCounter.set(2); // 设置为2，因为当前返回1
            return 1;
        }

        return current;
    }





    /**
     * 系统关闭时停止心跳服务
     */
    @PreDestroy
    public void destroy() {
        log.debug("系统正在关闭，停止心跳服务...");
        stopHeartBeat();

        try {
            scheduler.shutdown();
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
            log.debug("心跳服务已完全停止");
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
            log.debug("心跳服务停止时被中断", e);
        }
    }
}
