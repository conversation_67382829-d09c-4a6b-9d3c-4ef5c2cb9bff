package net.pingfang.service.impl;

import net.pingfang.core.hardware.plc.Plc;
import net.pingfang.core.hardware.plc.PlcCoreService;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.QueryPlcResDTO;
import net.pingfang.model.dto.UpdatePlcReqDTO;
import net.pingfang.repository.PlcRepository;
import net.pingfang.service.PlcService;
import net.pingfang.util.WebSocketUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023-07-20 17:29
 */
@Slf4j
@Service
public class PlcServiceImpl implements PlcService {

    @Autowired
    private PlcRepository plcRepository;

    @Autowired
    private PlcCoreService plcCoreService;
    @Autowired
    private WebSocketUtil webSocketUtil;

    @Override
    public Result<QueryPlcResDTO> query() {
        QueryPlcResDTO plcResDTO = plcRepository.queryDetail();
        return Result.success(plcResDTO);
    }

    @Override
    public Result<String> update(UpdatePlcReqDTO param) {
        if(null == param.getId()){
            return Result.fail("唯一值不能为空");
        }
        param.setUpdateBy("admin");
        plcRepository.updateById(param);
        return Result.success();
    }

    @Override
    public Result<String> send(Plc plc) {
        plcCoreService.dealPlcData(plc);
        return Result.success();
    }
}
