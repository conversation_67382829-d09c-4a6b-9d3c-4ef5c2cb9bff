package net.pingfang.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.constant.CommonConstant;
import net.pingfang.constant.ErrorMessageConstant.RecognitionErrorMessage;
import net.pingfang.core.algorithm.ailib.ContainerAlgorithmService;
import net.pingfang.core.algorithm.ailib.TopPlateAlgorithmService;
import net.pingfang.core.algorithm.ailib.model.DetectResults;
import net.pingfang.core.algorithm.ailib.model.RecTopPlate;
import net.pingfang.core.hardware.camera.hikvision.HikvisionService;
import net.pingfang.core.hardware.camera.hikvision.HikvisionServiceFactory;
import net.pingfang.core.hardware.plc.Plc;
import net.pingfang.core.hardware.plc.PlcCoreService;
import net.pingfang.enums.*;
import net.pingfang.model.common.Page;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.*;
import net.pingfang.model.dto.message.MessageIdentifyDTO;
import net.pingfang.model.dto.message.MessageImgDTO;
import net.pingfang.model.entity.Img;
import net.pingfang.model.entity.Lane;
import net.pingfang.model.vo.business.RecognizeConfigVO;
import net.pingfang.repository.ImgRepository;
import net.pingfang.repository.LaneRepository;
import net.pingfang.repository.RecognitionRepository;
import net.pingfang.repository.RecognizeConfigRepository;
import net.pingfang.service.IdentifyService;
import net.pingfang.service.RecognitionService;
import net.pingfang.util.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
@Service
@Slf4j
public class RecognitionServiceImpl implements RecognitionService {


    @Autowired
    private ImgRepository imgRepository;
    @Autowired
    private LaneRepository laneRepository;
    @Autowired
    private RecognitionRepository recognitionRepository;

    @Autowired
    private PlcCoreService plcCoreService;
    @Autowired
    private IdentifyService identifyService;

    @Autowired
    private PathUtil pathUtil;
    @Autowired
    private PathConfigUtil pathConfigUtil;
    @Autowired
    private RestTemplateUtil restTemplateUtil;


    @Value("${picture.path:D:/pfkj/img/}")
    public String fileDirPath;

    /**
     * 理货平台接口地址
     */
    public static String UP_LOAD_IMG_URL = "/identify/upload";
    public static String IDENTIFY_URL = "/identify/identify";

    @Override
    public Result<String> manualRecognize(ManualRecognizeDTO param) {
        Integer spreaderType = param.getSpreaderType();
        Integer workType = param.getWorkType();
        Integer type = param.getType();
        Integer flag = param.getFlag();
        Long laneId = param.getLaneId();
        //校验
        if (null == spreaderType) {
            return Result.fail(RecognitionErrorMessage.SPREADER_TYPE_NULL);
        }
        if (null == workType) {
            return Result.fail(RecognitionErrorMessage.WORK_TYPE_NULL);
        }
        if (null == type) {
            return Result.fail(RecognitionErrorMessage.TYPE_NULL);
        }
        if (null == flag) {
            return Result.fail(RecognitionErrorMessage.FLAG_NULL);
        }
        if (null == laneId) {
            return Result.fail(RecognitionErrorMessage.LANE_NULL);
        }
        //数据查询
        Lane lane = laneRepository.queryById(laneId);
        //todo 设置默认PLC对象
        Plc plc = new Plc();
        plc.setPlcX(100);
        plc.setPlcY(100);
        plc.setWeight(1024);
        plc.setLock(FlagEnum.START.getValue().equals(flag) ? LockSignEnum.END.getValue() : LockSignEnum.START.getValue());
        plc.setPresetType(type);
        plc.setContainerType(spreaderType);
        plc.setSeaLand(workType == 1 ? 0 : 1);
        //控制逻辑
        plcCoreService.dealPlcData(plc);
        return Result.success();
    }

    @Override
    public Result<QueryRecordResDTO> queryRecord(QueryRecordReqDTO param) {
        //1-数量
        Integer total = recognitionRepository.queryPageCount(param);

        //2-详情
        param.setStartIndex(PageUtil.getStartIndex(param.getCurrent(), param.getPageSize()));
        List<QueryRecordDTO> recordList = recognitionRepository.queryPage(param);
        Page<QueryRecordDTO> recordResult = new Page<>();
        recordResult.setTotal(total);
        recordResult.setRecords(recordList);
        //3-处理
        QueryRecordResDTO recordResDTO = new QueryRecordResDTO();
        Long shortCtnCount = recordList.stream().filter(record -> null != record.getContainerType() && ContainerTypeEnum.SS.getValue().equals(record.getContainerType())).count();
        Long longCtnCount = recordList.stream().filter(record -> null != record.getContainerType() && ContainerTypeEnum.SL.getValue().equals(record.getContainerType())).count();
        Long otherCtnCount = recordList.stream().filter(record -> null != record.getContainerType() && !ContainerTypeEnum.SS.getValue().equals(record.getContainerType()) && !ContainerTypeEnum.SL.getValue().equals(record.getContainerType())).count();
        recordResDTO.setCtnTotal(recordList.size());
        recordResDTO.setShortCtnCount(Long.bitCount(shortCtnCount));
        recordResDTO.setLongCtnCount(Long.bitCount(longCtnCount));
        recordResDTO.setOtherCtnCount(Long.bitCount(otherCtnCount));
        recordResDTO.setPageResult(recordResult);
        return Result.success(recordResDTO);
    }

    @Override
    public Result<String> manualUpload(ManualUploadReqDTO param) {
        //1-校验
        Integer command = param.getCommand();
        List<Long> idList = param.getIdList();
        if (null == command) {
            log.info("指令不能为空！");
            return Result.fail("");
        }
        if (CollectionUtils.isEmpty(idList) || idList.size() == 0) {
            log.info("记录id不能为空！");
            return Result.fail("");
        }

        //2-上传
        if ((CommandEnum.ALL.getValue().equals(command) || CommandEnum.IMG.getValue().equals(command))) {
            //图片上传
            MessageImgDTO imgDTO = new MessageImgDTO();
            imgDTO.setIdList(idList);

            identifyService.img(imgDTO);
        }
        if (CommandEnum.ALL.getValue().equals(command) || CommandEnum.WORK_INFO.getValue().equals(command)) {
            //识别数据上传
            MessageIdentifyDTO identifyDTO = new MessageIdentifyDTO();
            identifyDTO.setIdList(idList);

            identifyService.identify(identifyDTO);
        }
        return Result.success();
    }

    @Override
    public void download(DownloadReqDTO param, HttpServletResponse response) {
        //1-校验
        Integer command = param.getCommand();
        List<Long> idList = param.getIdList();
        if (null == command) {
            log.info("指令不能为空！");
            return;
        }
        if (CollectionUtils.isEmpty(idList) || idList.size() == 0) {
            log.info("记录id不能为空！");
            return;
        }

        //2-数据信息查询 todo  暂时只对图片数据进行下载处理
        List<Img> imgList = imgRepository.queryByRecordIds(idList);
        if (CollectionUtils.isEmpty(imgList) || imgList.size() == 0) {
            log.info("未找到对应数据信息！");
            return;
        }

        //3-组装下载地址
        String filePath = "";
        List<String> pathList = new ArrayList<>();
        for (Img img : imgList) {
            filePath = fileDirPath + img.getImgUrl();
            pathList.add(filePath);
        }

        //4、下载并压缩
        String zipPath = fileDirPath;
        String zipFileName = "视频或文件" + System.currentTimeMillis() + CommonConstant.FILE_ZIP;
        DownloadUtil.DownloadAndCompression(zipFileName, pathList, response);
        return;
    }

    @Override
    public void export(QueryRecordReqDTO param, HttpServletResponse response) {
        //1-导出数据查询
        List<QueryRecordDTO> data = recognitionRepository.queryExportRecordList(param);
        if (CollectionUtils.isEmpty(data)) {
            log.info("未找到数据！");
            return;
        }
        //2-赋值到导出实体类中
        List<ExportRecordExcelDTO> exportList = new ArrayList<>();
        ExportRecordExcelDTO recordExcel = new ExportRecordExcelDTO();
        for (QueryRecordDTO recordDTO : data) {
            recordExcel = new ExportRecordExcelDTO();
            BeanUtils.copyProperties(recordDTO, recordExcel);
            recordExcel.setNum(exportList.size() == 0 ? 1 : exportList.size() + 1);
            exportList.add(recordExcel);
        }

        //3-响应头
        String fileName = "记录数据_" + System.currentTimeMillis() + CommonConstant.FILE_XLSX;
        DownloadUtil.DownloadExcel(fileName, exportList, response);
    }

    @Override
    public Result<List<Img>> queryImg(String seqNo) {
        List<Img> list = new ArrayList<>();
        if (StringUtils.isEmpty(seqNo)) {
            return Result.success(list);
        }
        //查询
        List<Img> imgList = imgRepository.queryBySeqNo(seqNo);
        if (!CollectionUtils.isEmpty(imgList) && imgList.size() > 0) {
            String imgUrl = "";
            File file;
            for (Img img : imgList) {
                if (null == img) {
                    continue;
                }
                imgUrl = img.getImgUrl();
                if (StringUtils.isEmpty(imgUrl)) {
                    continue;
                }
                //判断文件是否存在
                file = new File(fileDirPath + imgUrl);
                if (file.exists()) {
                    img.setImgUrl(pathConfigUtil.getIisUrl() + imgUrl);
                    list.add(img);
                }
            }
        }
        return Result.success(list);
    }

    @Autowired
    private HikvisionServiceFactory hikvisionServiceFactory;

    @Autowired
    private ContainerAlgorithmService containerAlgorithmService;

    @Autowired
    private TopPlateAlgorithmService topPlateAlgorithmService;

    @Autowired
    private RecognizeConfigRepository recognizeConfigRepository;

    @Override
    public Result<Integer> copyImagesByCameraKeyWordToAbstractPath(String cameraKeyWord, String newPath) {
        List<String> imgUrlList = imgRepository.queryUrlByCameraKeyWord(cameraKeyWord);
        int i = 0;
        File folder = new File(fileDirPath + newPath);

        if (!folder.exists()){
            log.info("即将要写入的文件夹不存在，现创建该文件夹");
            folder.mkdirs();
        }

        for (String imgUrl : imgUrlList){
            try{
                String oldPath = fileDirPath + imgUrl;
                File oldFile = new File(oldPath);
                int bytesum = 0;
                int byteread = 0;

                if (oldFile.exists()) {
                    i++;
                    InputStream inStream = new FileInputStream(oldPath);
                    FileOutputStream fs = new FileOutputStream(fileDirPath+newPath+"/"+oldFile.getName());
                    byte[] buffer = new byte[1024];
                    int length;
                    while ((byteread = inStream.read(buffer)) != -1) {
                        bytesum += byteread; //字节数 文件大小
                        fs.write(buffer, 0, byteread);
                    }
                    inStream.close();
                }
            }catch (Exception e){
                log.info(e.getMessage());
            }
        }
        return Result.success(i);
    }

    @Override
    public Result<String> manualRecognizeCtn(Long cameraId) {
        return null;
    }


    @Override
    public Result<String> manualRecognizeTop(Long cameraId) {


        List<RecognizeConfigVO> rcList = recognizeConfigRepository.queryWorkConfigByType(null, RecognizeConfigTypeEnum.PLATE_TOP.getValue());
        if (cameraId != null) {
            rcList = rcList.stream().filter(item -> item.getCameraId() == cameraId).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(rcList)) {
            return Result.fail("未查询到任何车顶号相机配置,无法抓拍车顶号");
        }
        String topPlate = null;

        for (RecognizeConfigVO item : rcList) {

            try {
                log.info("{}开始抓拍车顶号", item.getName());
                // 相机注册
                HikvisionService service = hikvisionServiceFactory.getService(item.getIp());
                boolean registerResult = service.register(item.getIp(), item.getControlPort() == null ? 8000 : item.getControlPort(), item.getUsername(), item.getPassword());
                if (!registerResult) {
                    continue;
                }
                // 跳转预置位
                if (item.getPresetLocation() != null && item.getPresetLocation() != 0) {
                    boolean setPositionResult = service.setPosition(item.getChannel() == null ? 1 : item.getChannel(), 39, item.getPresetLocation());
                } else {
                    log.info("相机:{}未设置预置位", item.getName());
                }
                // 抓拍
                // 抓拍图片
                // 生成基于时间戳的临时seqNo，避免使用硬编码的"test"
                String tempSeqNo = "TOP_PLATE_" + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
                String packagePath = pathConfigUtil.getRmgName() + "/" + DateUtil.getDate(DateUtil.TYPE_EIGHT) + "/" + tempSeqNo + "/" + item.getCode() + "_" + UUID.randomUUID() + ".jpg";

                String filePath = this.fileDirPath + packagePath;
                File file = new File(filePath);
                if (!file.getParentFile().exists()) {
                    //创建上级目录
                    file.getParentFile().mkdirs();
                }
                boolean snapResult = service.snapMemory(item.getChannel() == null ? 1 : item.getChannel(), file);
                if (!snapResult) {
                    continue;
                }

                // 检测
                DetectResults detectResults = topPlateAlgorithmService.detPicture(filePath);
                log.debug("摄像头:{}获取车顶号算法检测结果：{}", item.getName(), JsonUtil.toJson(detectResults));

                if (detectResults == null || detectResults.det_num == 0) {
                    log.debug("摄像头:{}未获取到车顶号算法检测结果", item.getName());
                    continue;
                }

                // 识别
                RecTopPlate recTopPlate = topPlateAlgorithmService.recPicture(filePath, detectResults);
                if (recTopPlate != null && StringUtils.isEmpty(recTopPlate.getTopPlate())) {
                    topPlate = recTopPlate.getTopPlate();
                }
            } catch (Exception ex) {
                log.error("{}车顶号手动抓拍流程异常:{}", item.getName(), ExceptionUtil.getStackTrace(ex));
            }
        }

        return Result.success(topPlate);
    }

}

