package net.pingfang.service.external.gkpt.impl;

import javax.annotation.Resource;

import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.model.common.Result;
import net.pingfang.service.external.gkpt.GkptService;
import net.pingfang.service.external.gkpt.dto.SendIdentifyReqDTO;
import net.pingfang.util.ExternalConfigUtil;
import net.pingfang.util.RestTemplateUtil;

/**
 * <AUTHOR>
 * @since 2024-10-23 16:51
 */
@Slf4j
@Service
public class GkptServiceImpl implements GkptService {

    /**
     * 同步识别数据
     */
    private static final String PUSH_OCR_URL = "/ts/pushOcr";

    @Resource
    private RestTemplateUtil restTemplate;
    @Resource
    private ExternalConfigUtil externalConfigUtil;


    @Override
    public Result<String> sendIdentify(SendIdentifyReqDTO resDTO) {
        resDTO.setCraneNum(externalConfigUtil.getRmgName());
        ParameterizedTypeReference<Result<String>> responseType = new ParameterizedTypeReference<Result<String>>() {};
        Result<String> result = restTemplate.post(externalConfigUtil.getGkptUrl() + PUSH_OCR_URL, resDTO, responseType);
        return result;
    }
}
