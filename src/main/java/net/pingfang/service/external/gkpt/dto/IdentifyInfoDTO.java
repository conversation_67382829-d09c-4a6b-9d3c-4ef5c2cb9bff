package net.pingfang.service.external.gkpt.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 识别数据DTO
 *
 * @title: IdentifyInfoDTO
 * @author: cb
 * @date: 2025-06-03 11:34
 * @version: 1.0
 */
@Data
@Accessors(chain = true)
public class IdentifyInfoDTO {
    /**
     * 设备号
     */
    private String craneId;
    /**
     * 堆场号
     */
    private String yardId;
    /**
     * 任务号
     */
    private String taskId;

    /**
     * 唯一编号
     */
    private String seqNo;

    /**
     * 上锁时间 (yyyy-MM-dd HH:mm:ss)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lockTime;

    /**
     * 解锁时间 (yyyy-MM-dd HH:mm:ss)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime unLockTime;

    /**
     * 箱号信息
     */
    private ContainerInfo containerInfo;

    /**
     * 车号信息
     */
    private TruckInfo truckInfo;

    /**
     * 车顶号信息
     */
    private TopPlateInfo topPlateInfo;

    /**
     * 车牌号信息
     */
    private PlateNumberInfo plateNumberInfo;

    /**
     * 火车车厢信息
     */
    private TrainCarriageInfo trainCarriageInfo;

    public IdentifyInfoDTO() {
        this.containerInfo = new ContainerInfo();
        this.truckInfo = new TruckInfo();
        this.topPlateInfo = new TopPlateInfo();
        this.plateNumberInfo = new PlateNumberInfo();
        this.trainCarriageInfo = new TrainCarriageInfo();
    }

    @Data
    @Accessors(chain = true)
    public static class ContainerInfo {
        /**
         * 箱号
         */
        private String containerNo;
        /**
         * ISO
         */
        private String iso;
        /**
         * 箱号图片
         */
        private String containerPic;
    }

    @Data
    @Accessors(chain = true)
    public static class TruckInfo {
        /**
         * 车号
         */
        private String truckNo;
        /**
         * 车号图片
         */
        private String truckPic;
    }

    @Data
    @Accessors(chain = true)
    public static class TopPlateInfo {
        /**
         * 车顶号
         */
        private String topPlateNo;
        /**
         * 车顶号图片
         */
        private String topPlatePic;
    }

    @Data
    @Accessors(chain = true)
    public static class PlateNumberInfo {
        /**
         * 车牌号
         */
        private String plateNumber;
        /**
         * 车牌号图片
         */
        private String plateNumberPic;
    }

    @Data
    @Accessors(chain = true)
    public static class TrainCarriageInfo {
        /**
         * 火车车厢号
         */
        private String trainCarriageNo;
        /**
         * 火车车厢图片
         */
        private String trainCarriagePic;
    }
}
