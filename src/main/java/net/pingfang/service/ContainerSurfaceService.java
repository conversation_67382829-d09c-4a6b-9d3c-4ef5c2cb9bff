package net.pingfang.service;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.config.ContainerSurfaceConfig;
import net.pingfang.core.algorithm.ailib.ContainerAlgorithmService;
import net.pingfang.core.algorithm.ailib.model.DetectResults;
import net.pingfang.core.algorithm.ailib.model.DetectResult;
import net.pingfang.core.hardware.camera.hikvision.HikvisionService;
import net.pingfang.core.hardware.camera.hikvision.HikvisionServiceFactory;
import net.pingfang.core.hardware.plc.Plc;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.EcsIdentifyInfoDTO;
import net.pingfang.service.EcsService;
import net.pingfang.util.DateUtil;
import net.pingfang.util.PathConfigUtil;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 箱面识别服务
 * 负责箱门朝向识别的具体实现
 *
 * <AUTHOR>
 * @date 2025-01-08
 */
@Service
@Slf4j
public class ContainerSurfaceService {

    @Resource
    private ContainerSurfaceConfig containerSurfaceConfig;

    @Resource
    private EcsService ecsService;

    @Resource
    private PathConfigUtil pathConfigUtil;

    @Resource
    private HikvisionServiceFactory hikvisionServiceFactory;

    @Resource
    private ContainerAlgorithmService containerAlgorithmService;

    // 预初始化的相机服务
    private HikvisionService preInitializedCameraService;

    // 存储识别成功的图片路径，用于ECS上报
    private String successImagePath = null;

    /**
     * 应用启动时初始化箱门相机
     */
    @PostConstruct
    public void initializeCameraOnStartup() {
        if (!containerSurfaceConfig.isEnabled()) {
            log.info("箱门识别功能未启用，跳过相机初始化");
            return;
        }

        try {
            log.info("开始初始化箱门识别相机...");
            preInitializedCameraService = initializeCamera();

            if (preInitializedCameraService != null) {
                log.info("✅ 箱门识别相机初始化成功 - IP: {}",
                        containerSurfaceConfig.getCamera().getIp());
            } else {
                log.error("❌ 箱门识别相机初始化失败");
            }

        } catch (Exception e) {
            log.error("箱门识别相机初始化异常", e);
            preInitializedCameraService = null;
        }
    }

    /**
     * 执行箱门朝向识别
     * @param plc PLC数据
     * @return 识别结果：0-朝向左且识别成功，1-朝向右且识别成功，2-识别失败继续尝试，3-最终超时默认朝向右
     */
    public Integer recognizeDoorOrientation(Plc plc) {
        try {
            log.info("开始箱门朝向识别 - 车道:{}, 高度:{}米",
                    plc.getLane(), plc.getSpreaderFromContainerHeight());

            // 重置图片路径
            successImagePath = null;

            // 连续抓拍识别
            Integer result = performRecognition();

            switch (result) {
                case 0:
                    log.info("✅ 箱门朝向识别成功 - 结果: 朝向左");
                    return 0; // 识别成功，朝向左
                case 1:
                    log.info("✅ 箱门朝向识别成功 - 结果: 朝向右");
                    return 1; // 识别成功，朝向右
                case 2:
                    log.info("❌ 箱门朝向识别失败 - 继续尝试");
                    return 2; // 识别失败，继续尝试
                default:
                    log.info("⏰ 箱门朝向识别超时 - 默认朝向右");
                    return 3; // 超时，默认朝向右
            }

        } catch (Exception e) {
            log.error("箱门朝向识别异常，默认返回朝向右", e);
            return 3; // 异常默认朝右
        }
    }

    /**
     * 连续抓拍识别
     * @return 识别结果：0-朝向左且成功，1-朝向右且成功，2-识别失败，3-超时默认朝向右
     */
    private Integer performRecognition() {
        // 针对1~2秒时间窗口优化的参数
        final int maxAttempts = containerSurfaceConfig.getRecognition().getMaxAttempts();
        final int captureInterval = containerSurfaceConfig.getRecognition().getCaptureIntervalMs();

        HikvisionService hikvisionService = null;

        try {
            // 1. 使用预初始化的相机服务
            hikvisionService = preInitializedCameraService;
            if (hikvisionService == null) {
                log.warn("预初始化相机服务不可用，尝试重新初始化...");
                hikvisionService = initializeCamera();
                if (hikvisionService == null) {
                    log.error("相机初始化失败，默认朝向右");
                    return 1;
                }
            } else {
                log.info("使用预初始化的箱门识别相机服务");
            }

            // 2. 连续抓拍识别循环
            for (int attempt = 1; attempt <= maxAttempts; attempt++) {
                long attemptStart = System.currentTimeMillis();

                try {
                    log.info("箱门识别第{}次尝试 (共{}次)", attempt, maxAttempts);

                    // 2.1 抓拍图片
                    String imagePath = captureWithRegisteredCamera(hikvisionService);
                    if (imagePath == null) {
                        log.info("第{}次抓拍失败", attempt);
                        continue;
                    }

                    // 2.2 算法识别
                    DetectResults results = containerAlgorithmService.detDoorDirectionFeatures(imagePath);
                    if (results == null) {
                        log.info("第{}次算法调用失败", attempt);
                        continue;
                    }

                    // 2.3 分析结果
                    if (analyzeDoorFeatures(results)) {
                        log.info("✅ 第{}次识别成功 -> 朝向左", attempt);
                        // 保存成功识别的图片路径，用于ECS上报
                        successImagePath = imagePath;
                        return 0; // 识别成功，立即返回
                    }

                    log.info("第{}次未检测到箱门特征", attempt);

                    // 2.4 控制抓拍间隔
                    long attemptTime = System.currentTimeMillis() - attemptStart;
                    long sleepTime = captureInterval - attemptTime;
                    if (sleepTime > 0) {
                        Thread.sleep(sleepTime);
                    }

                } catch (Exception e) {
                    log.info("第{}次识别异常: {}", attempt, e.getMessage());
                }
            }

            // 3. 超时未识别到，默认朝右
            log.info("识别超时！{}次尝试均未识别到箱门特征，默认朝向右", maxAttempts);
            return 3; // 超时状态

        } finally {
            // 4. 不清理预初始化的相机服务，只清理临时创建的相机服务
            if (hikvisionService != null && hikvisionService != preInitializedCameraService) {
                cleanupCamera(hikvisionService);
            }
        }
    }

    /**
     * 相机初始化（一次性注册）
     */
    private HikvisionService initializeCamera() {
        try {
            String cameraIp = containerSurfaceConfig.getCamera().getIp();
            Integer port = containerSurfaceConfig.getCamera().getPort();
            String username = containerSurfaceConfig.getCamera().getUsername();
            String password = containerSurfaceConfig.getCamera().getPassword();

            HikvisionService hikvisionService = hikvisionServiceFactory.getService(cameraIp);
            boolean registerResult = hikvisionService.register(cameraIp, port, username, password);

            if (registerResult) {
                log.info("箱门识别相机注册成功 - IP: {}, port {},user {}, password {}", cameraIp,port,username,password);
                return hikvisionService;
            } else {
                log.info("箱门识别相机注册失败 - IP: {}, port {},user {}, password {}", cameraIp,port,username,password);
                return null;
            }

        } catch (Exception e) {
            log.info("相机初始化异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 使用已注册相机抓拍
     */
    private String captureWithRegisteredCamera(HikvisionService hikvisionService) {
        try {
            String imagePath = generateImagePath();
            File file = new File(imagePath);
            if (!file.getParentFile().exists()) {
                file.getParentFile().mkdirs();
            }

            Integer channel = containerSurfaceConfig.getCamera().getChannel();
            boolean snapResult = hikvisionService.snapMemory(channel, file);

            if (snapResult) {
                log.info("抓拍成功: {}", imagePath);
                return imagePath;
            } else {
                log.info("抓拍失败");
                return null;
            }

        } catch (Exception e) {
            log.info("抓拍异常: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 生成图片路径
     */
    private String generateImagePath() {
        String timestamp = String.valueOf(System.currentTimeMillis());
        String fileName = "door_direction_" + timestamp + ".jpg";
        String dateDir = DateUtil.getDate(DateUtil.TYPE_EIGHT); // yyyyMMdd格式

        return pathConfigUtil.getLocalPath() + "door_direction/" + dateDir + "/" + fileName;
    }

    /**
     * 资源清理
     */
    private void cleanupCamera(HikvisionService hikvisionService) {
        try {
            if (hikvisionService != null) {
                // 海康威视SDK的清理逻辑（如果需要）
                log.debug("清理相机资源");
            }
        } catch (Exception e) {
            log.warn("相机资源清理异常", e);
        }
    }

    /**
     * 分析是否检测到箱门特征
     */
    private boolean analyzeDoorFeatures(DetectResults results) {
        log.info("分析箱门特征 - 检测到目标数量: {}", results.getDet_num());

        if (results.getDet_num() == 0) {
            log.info("未检测到任何目标");
            return false;
        }

        int doorFeatureCount = 0;

        for (int i = 0; i < results.getDet_num(); i++) {
            DetectResult result = results.getDr()[i];
            int type = result.getDet_type();
            float confidence = result.getDet_fr();

            log.info("检测框{}: 类型={}, 置信度={}", i + 1, type, confidence);

            // 箱门特征：类型5,6,7,16
            if (type == 5 || type == 6 || type == 7 || type == 16) {
                doorFeatureCount++;
                log.info("  -> 检测到箱门特征");
            }
        }

        if (doorFeatureCount > 0) {
            log.info("识别成功: 检测到{}个箱门特征", doorFeatureCount);
            return true;
        } else {
            log.info("未检测到箱门特征");
            return false;
        }
    }

    /**
     * 上报识别结果到ECS（使用现有的EcsService）
     * @param plc PLC数据
     * @param doorOrientation 箱门朝向（中文：左、右）
     * @param identifyType 识别类型
     * @return 上报是否成功
     */
    public boolean reportToECS(Plc plc, String doorOrientation, Integer identifyType) {
        try {
            // 构建ECS识别信息DTO
            EcsIdentifyInfoDTO identifyInfo = buildEcsIdentifyInfo(plc, doorOrientation, identifyType);

            log.info("上报ECS - 识别类型:{}, 箱门朝向:{}, 车道:{}",
                    identifyType, doorOrientation, plc.getLane());

            // 调用现有的ECS服务
            Result<String> result = ecsService.sendIdentifyInfo(identifyInfo);

            if (result.getCode() != null && result.getCode() == 2000) {
                log.info("ECS上报成功");
                return true;
            } else {
                log.error("ECS上报失败: {}", result.getMsg());
                return false;
            }

        } catch (Exception e) {
            log.error("ECS上报异常", e);
            return false;
        }
    }

    /**
     * 构建ECS识别信息DTO
     * @param plc PLC数据
     * @param doorOrientation 箱门朝向
     * @param identifyType 识别类型
     * @return EcsIdentifyInfoDTO
     */
    private EcsIdentifyInfoDTO buildEcsIdentifyInfo(Plc plc, Integer doorOrientation, Integer identifyType) {
        EcsIdentifyInfoDTO identifyInfo = new EcsIdentifyInfoDTO();

        // 设置门吊号
        identifyInfo.setCraneNo(pathConfigUtil.getRmgName());

        // 设置识别类型和箱门朝向
        identifyInfo.setIdentifyType(identifyType); // 4-箱门朝向
        identifyInfo.setCtnDoorOrientation(doorOrientation); // 0-朝向左，1-朝向右

        // 添加图片URL（如果有成功识别的图片）
        if (successImagePath != null) {
            List<String> imgUrls = new ArrayList<>();
            String imageUrl = convertImagePathToUrl(successImagePath);
            if (imageUrl != null) {
                imgUrls.add(imageUrl);
                identifyInfo.setImgUrls(imgUrls);
                log.info("箱门朝向识别图片已添加到ECS上报 - 图片: {}", imageUrl);
            }
        }

        return identifyInfo;
    }

    /**
     * 将图片路径转换为URL（参考CoreFlow的实现）
     * @param imagePath 图片路径
     * @return 图片URL
     */
    private String convertImagePathToUrl(String imagePath) {
        try {
            if (imagePath == null || imagePath.trim().isEmpty()) {
                return null;
            }

            // 如果已经是完整URL，直接返回
            if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
                return imagePath;
            }

            // 获取基础图片目录
            String baseDir = pathConfigUtil.getLocalPath();
            if (baseDir == null || baseDir.trim().isEmpty()) {
                baseDir = "D:/pfkj/img";
            }

            // 标准化路径分隔符
            String normalizedImgPath = imagePath.replace("\\", "/");
            String normalizedBaseDir = baseDir.replace("\\", "/");

            // 确保基础目录以/结尾
            if (!normalizedBaseDir.endsWith("/")) {
                normalizedBaseDir += "/";
            }

            // 如果图片路径包含基础目录，则去除该部分得到相对路径
            String relativePath;
            if (normalizedImgPath.startsWith(normalizedBaseDir)) {
                relativePath = normalizedImgPath.substring(normalizedBaseDir.length());
            } else {
                // 如果不包含基础目录，直接使用文件名部分
                relativePath = normalizedImgPath.substring(normalizedImgPath.lastIndexOf("/") + 1);
            }

            // 获取IIS URL配置
            String iisUrl = pathConfigUtil.getIisUrl();
            if (iisUrl != null && !iisUrl.trim().isEmpty()) {
                // 确保IIS URL以/结尾
                if (!iisUrl.endsWith("/")) {
                    iisUrl += "/";
                }
                return iisUrl + relativePath;
            }

            // 如果没有IIS URL配置，返回相对路径
            return "/" + relativePath;

        } catch (Exception e) {
            log.error("转换图片路径为URL异常 - 路径: {}, 错误: {}", imagePath, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查并重新初始化相机（如果需要）
     */
    public void checkAndReinitializeCamera() {
        if (!containerSurfaceConfig.isEnabled()) {
            return;
        }

        try {
            // 检查预初始化的相机是否还有效
            if (preInitializedCameraService == null) {
                log.warn("预初始化相机服务为null，尝试重新初始化...");
                preInitializedCameraService = initializeCamera();

                if (preInitializedCameraService != null) {
                    log.info("箱门识别相机重新初始化成功");
                } else {
                    log.error("箱门识别相机重新初始化失败");
                }
            }

        } catch (Exception e) {
            log.error("相机重新初始化异常", e);
            preInitializedCameraService = null;
        }
    }

    /**
     * 检查服务状态
     * @return 服务是否正常
     */
    public boolean isServiceHealthy() {
        try {
            // 检查配置
            if (!containerSurfaceConfig.isEnabled()) {
                return false;
            }

            // 检查相机配置
            String cameraIp = containerSurfaceConfig.getCamera().getIp();
            if (cameraIp == null || cameraIp.trim().isEmpty()) {
                return false;
            }

            // 检查算法服务
            if (containerAlgorithmService == null) {
                return false;
            }

            // 检查预初始化的相机服务
            if (preInitializedCameraService == null) {
                log.warn("预初始化相机服务不可用");
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("服务健康检查异常", e);
            return false;
        }
    }
}
