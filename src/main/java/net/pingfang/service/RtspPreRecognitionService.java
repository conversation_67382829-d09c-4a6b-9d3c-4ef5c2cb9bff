package net.pingfang.service;

import net.pingfang.core.hardware.rtsp.RtspBestResult;
import net.pingfang.model.dto.RtspPreRecognitionQueryDTO;
import net.pingfang.model.dto.RtspPreRecognitionListDTO;
import net.pingfang.model.dto.RtspPreRecognitionDetailDTO;
import net.pingfang.model.common.Page;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * RTSP预识别服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-07
 */
public interface RtspPreRecognitionService {
    
    /**
     * 保存预识别结果
     * 
     * @param seqNo 序列号
     * @param bestResult 最佳识别结果
     * @param allCameraResults 所有相机识别结果
     * @param endReason 结束原因
     * @param startTime 开始时间
     */
    void savePreRecognitionResult(String seqNo, 
                                 RtspBestResult bestResult,
                                 Map<Integer, RtspBestResult> allCameraResults,
                                 String endReason, 
                                 LocalDateTime startTime);
    
    /**
     * 保存预识别结果（ECS推送失败的情况）
     *
     * @param seqNo 序列号
     * @param bestResult 最佳识别结果
     * @param allCameraResults 所有相机识别结果
     * @param endReason 结束原因
     * @param startTime 开始时间
     * @param errorMessage 错误信息
     */
    void savePreRecognitionResultWithError(String seqNo,
                                          RtspBestResult bestResult,
                                          Map<Integer, RtspBestResult> allCameraResults,
                                          String endReason,
                                          LocalDateTime startTime,
                                          String errorMessage);

    /**
     * 分页查询预识别记录列表
     *
     * @param queryDTO 查询条件
     * @return 分页结果
     */
    Page<RtspPreRecognitionListDTO> getPreRecognitionList(RtspPreRecognitionQueryDTO queryDTO);

    /**
     * 根据ID获取预识别详情（包含所有相机图片）
     *
     * @param id 记录ID
     * @return 详情信息
     */
    RtspPreRecognitionDetailDTO getPreRecognitionDetail(Long id);

    /**
     * 根据ID获取预识别记录的图片列表
     *
     * @param id 记录ID
     * @return 图片列表
     */
    List<Map<String, Object>> getPreRecognitionImages(Long id);
}
