package net.pingfang.service;

import java.io.IOException;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.LogReqDTO;
import net.pingfang.model.dto.QueryLogListResDTO;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
public interface LogService {
    Result<List<QueryLogListResDTO>> queryLogList(LogReqDTO param) throws IOException;

    Result<List<String>> queryLogFile(String filePath);

    void downloadLogFile(String filePath, HttpServletResponse response);
}
