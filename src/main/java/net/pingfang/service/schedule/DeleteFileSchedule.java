package net.pingfang.service.schedule;

import java.io.File;
import net.pingfang.util.ExceptionUtil;
import net.pingfang.util.PathConfigUtil;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.BasicFileAttributes;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 定时任务
 * <AUTHOR>
 * @Date 2023/9/8 16:34
 * @Description:
*/
@Slf4j
public class DeleteFileSchedule {

    @Autowired
    private PathConfigUtil pathConfigUtil;

    /**
     * 图片保存近30天
     * 每天凌晨执行任务
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void cleanUpImg() {
        int deleteCount = 0;
        LocalDateTime dateTime = LocalDateTime.now().minusDays(7);
        // 获取文件夹下的所有文件名
        File folder = new File(pathConfigUtil.getLocalPath()+pathConfigUtil.getRmgName());
        String[] fileNames = folder.list();

        if (fileNames == null) {
            return;
        }

        // 遍历文件名列表，删除文件
        for (String fileName : fileNames) {
            File file = new File(folder, fileName);
            if(!file.isDirectory()){
                continue;
            }
            try {
                // 图片时间是否超过30天，则删除
                Path filePath = file.toPath();
                BasicFileAttributes attrs = Files.readAttributes(filePath, BasicFileAttributes.class);
                long lastModifiedTime = attrs.lastModifiedTime().toInstant().toEpochMilli();
                long currentTime = new Date().getTime();
                long timeInterval = 30 * 24 * 60 * 60 * 1000;
                if (currentTime - lastModifiedTime > timeInterval) {
                    Files.delete(filePath);
                    deleteCount++;
                }
            } catch (Exception e) {
                log.error("定时任务，图片删除异常：{}", ExceptionUtil.getStackTrace(e));
            }
        }
        log.info("时间：[{}]--> 删除图片数量：{}",dateTime,deleteCount);
    }

}
