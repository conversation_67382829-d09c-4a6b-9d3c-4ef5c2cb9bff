package net.pingfang.repository;

import net.pingfang.model.entity.Camera;
import java.util.List;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/26
 */
public interface CameraRepository {

    void add(Camera camera);

    List<Camera> queryAll();

    void update(Camera camera);

    void delete(Long id);

    Camera queryById(Long id);

    List<Camera> queryByIds(List<Long> ids);

    List<Camera> queryByType(Integer type);
}
