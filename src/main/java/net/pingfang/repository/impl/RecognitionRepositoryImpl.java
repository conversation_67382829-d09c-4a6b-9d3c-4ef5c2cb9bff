package net.pingfang.repository.impl;

import net.pingfang.mapper.RecognitionMapper;
import net.pingfang.model.dto.QueryRecordDTO;
import net.pingfang.model.dto.QueryRecordReqDTO;
import net.pingfang.model.dto.QueryRecordSyncReqDTO;
import net.pingfang.model.entity.Record;
import net.pingfang.repository.RecognitionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/26
 */
@Repository
public class RecognitionRepositoryImpl implements RecognitionRepository {
    @Autowired
    private RecognitionMapper recognitionMapper;

    @Override
    public Integer queryTotal() {
        return recognitionMapper.queryTotal();
    }

    @Override
    public Integer queryCtnTotalByType(Integer... containerType) {
        return recognitionMapper.queryCtnTotalByType(containerType);
    }

    @Override
    public Integer queryRecoAvgTimeConsuming() {
        return recognitionMapper.queryRecoAvgTimeConsuming();
    }

    @Override
    public Integer querySnapAvgTimeConsuming() {
        return recognitionMapper.querySnapAvgTimeConsuming();
    }

    @Override
    public Integer queryCtnTotal() {
        return recognitionMapper.queryCtnTotal();
    }

    @Override
    public Integer queryCtnCheckSuccess() {
        return recognitionMapper.queryCtnCheckSuccess();
    }

    @Override
    public Integer queryPlateNumberCheckSuccess() {
        return recognitionMapper.queryPlateNumberCheckSuccess();
    }

    @Override
    public Integer queryPageCount(QueryRecordReqDTO param) {
        return recognitionMapper.queryPageCount(param);
    }

    @Override
    public List<QueryRecordDTO> queryPage(QueryRecordReqDTO param) {
        return recognitionMapper.queryPage(param);
    }

    @Override
    public List<QueryRecordDTO> queryExportRecordList(QueryRecordReqDTO param) {
        return recognitionMapper.queryExportRecordList(param);
    }

    @Override
    public List<Record> queryByIds(List<Long> ids,String seqNo) {
        return recognitionMapper.queryByIds(ids,seqNo);
    }

    @Override
    public void add(Record record) {
        recognitionMapper.add(record);
    }

    @Override
    public void update(Record record) {
        recognitionMapper.update(record);
    }

    @Override
    public void updateBySeqNo(Record record) {
        recognitionMapper.updateBySeqNo(record);
    }

    @Override
    public void deleteBySeqNo(String seqNo) {
        recognitionMapper.deleteBySeqNo(seqNo);
    }

    @Override
    public Record queryBySeqNo(String seqNo) {
        if (seqNo == null) {
            return null;
        }
        return recognitionMapper.queryBySeqNo(seqNo);
    }

    @Override
    public void updateTopPlate(String topPlate, String seqNo) {
        recognitionMapper.updateTopPlate(topPlate, seqNo);
    }

    @Override
    public QueryRecordSyncReqDTO querySyncBySeqNo(String seqNo) {
        return recognitionMapper.querySyncBySeqNo( seqNo);
    }

}
