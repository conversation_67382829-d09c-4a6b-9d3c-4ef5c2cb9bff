package net.pingfang.repository;

import net.pingfang.model.dto.QueryRecordDTO;
import net.pingfang.model.dto.QueryRecordReqDTO;
import net.pingfang.model.dto.QueryRecordSyncReqDTO;
import net.pingfang.model.entity.Record;

import java.util.List;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/26
 */
public interface RecognitionRepository {
    Integer queryTotal();

    Integer queryCtnTotalByType(Integer... containerType);

    Integer queryRecoAvgTimeConsuming();

    Integer querySnapAvgTimeConsuming();

    Integer queryCtnTotal();

    Integer queryCtnCheckSuccess();

    Integer queryPlateNumberCheckSuccess();

    Integer queryPageCount(QueryRecordReqDTO param);

    List<QueryRecordDTO> queryPage(QueryRecordReqDTO param);

    List<QueryRecordDTO> queryExportRecordList(QueryRecordReqDTO param);

    List<Record> queryByIds(List<Long> ids,String seqNo);

    void add(Record record);

    void update(Record record);

    void updateBySeqNo(Record record);

    void deleteBySeqNo(String seqNo);

    Record queryBySeqNo(String seqNo);

    void updateTopPlate(String topPlate, String seqNo);

    QueryRecordSyncReqDTO querySyncBySeqNo(String seqNo);

}
