package net.pingfang.repository;

import net.pingfang.model.entity.Img;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-07-19 13:54
 */
public interface ImgRepository {

    int insertBatch(List<Img> list);

    List<Img> queryByRecordIds(List<Long> ids);

    List<Img> queryBySeqNo(String seqNo);

    void insert(Img img);

    List<Img> queryBySeqNoAndType(String seqNo, Integer type);

    List<String> queryUrlByCameraKeyWord(String cameraKeyWord);
}
