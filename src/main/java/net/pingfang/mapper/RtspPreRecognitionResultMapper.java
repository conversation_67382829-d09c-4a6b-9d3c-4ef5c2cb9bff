package net.pingfang.mapper;

import net.pingfang.model.entity.RtspPreRecognitionResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * RTSP预识别结果Mapper
 * <AUTHOR>
 * @Date 2025/06/27
 * @Description: RTSP预识别结果数据访问层
 */
@Mapper
public interface RtspPreRecognitionResultMapper {
    
    /**
     * 插入预识别结果
     * @param result 预识别结果
     * @return 影响行数
     */
    int insert(@Param("result") RtspPreRecognitionResult result);
    
    /**
     * 根据ID查询
     * @param id 主键ID
     * @return 预识别结果
     */
    RtspPreRecognitionResult selectById(@Param("id") Long id);
    
    /**
     * 查询所有记录
     * @return 预识别结果列表
     */
    List<RtspPreRecognitionResult> selectAll();
    
    /**
     * 根据序列号查询
     * @param seqNo 序列号
     * @return 预识别结果列表
     */
    List<RtspPreRecognitionResult> selectBySeqNo(@Param("seqNo") String seqNo);
    
    /**
     * 根据吊机号查询
     * @param craneNo 吊机号
     * @return 预识别结果列表
     */
    List<RtspPreRecognitionResult> selectByCraneNo(@Param("craneNo") String craneNo);
    
    /**
     * 根据状态查询
     * @param status 状态
     * @return 预识别结果列表
     */
    List<RtspPreRecognitionResult> selectByStatus(@Param("status") String status);
}
