package net.pingfang.mapper;

import net.pingfang.model.entity.Lane;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/26
 */
@Mapper
public interface LaneMapper {
    List<Lane> queryAll();

    void add(@Param("param") Lane lane);

    void update(@Param("param") Lane lane);

    void delete(@Param("id") Long id);

    Lane queryByPlcX(@Param("plcX") int plcX);

    Lane queryById(@Param("id") Long id);
}
