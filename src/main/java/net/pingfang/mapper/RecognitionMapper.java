package net.pingfang.mapper;

import net.pingfang.model.dto.QueryRecordDTO;
import net.pingfang.model.dto.QueryRecordReqDTO;
import net.pingfang.model.dto.QueryRecordSyncReqDTO;
import net.pingfang.model.entity.Record;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/26
 */
@Mapper
public interface RecognitionMapper {
    int queryTotal();

    Integer queryCtnTotalByType(@Param("containerType") Integer... containerType);

    Integer queryRecoAvgTimeConsuming();

    Integer querySnapAvgTimeConsuming();

    Integer queryCtnTotal();

    Integer queryCtnCheckSuccess();

    Integer queryPlateNumberCheckSuccess();

    Integer queryPageCount(@Param("queryParam") QueryRecordReqDTO param);

    List<QueryRecordDTO> queryPage(@Param("queryParam") QueryRecordReqDTO param);

    List<QueryRecordDTO> queryExportRecordList(@Param("queryParam") QueryRecordReqDTO param);

    List<Record> queryByIds(@Param("ids")List<Long> ids,@Param("seqNo")String seqNo);

    void add(@Param("param") Record record);

    void update(@Param("param") Record record);

    void updateBySeqNo(@Param("param") Record record);

    void deleteBySeqNo(@Param("seqNo")String seqNo);

    Record queryBySeqNo(@Param("seqNo") String seqNo);

    void updateTopPlate(@Param("topPlate") String topPlate, @Param("seqNo") String seqNo);

    QueryRecordSyncReqDTO querySyncBySeqNo(@Param("seqNo")String seqNo);

}
