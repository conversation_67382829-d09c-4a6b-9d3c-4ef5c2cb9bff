package net.pingfang.mapper;

import net.pingfang.model.entity.Camera;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/26
 */
@Mapper
public interface CameraMapper {
    void add(@Param("param") Camera camera);

    List<Camera> queryAll();

    void update(@Param("param") Camera camera);

    void delete(@Param("id") Long id);

    Camera queryById(@Param("id") Long id);

    List<Camera> queryByIds(@Param("ids") List<Long> ids);

    List<Camera> queryByType(@Param("type") Integer type);
}
