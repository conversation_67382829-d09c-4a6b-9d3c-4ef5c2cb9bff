package net.pingfang.model.business.dto;

import lombok.Data;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @CreateTime 2024/11/19 16:30
 * @Description
 */
@Data
public class ReceiveIdentifyInfoReqDTO {
    /**
     * 门吊号 TRMG01、TRMG02、TRMG03
     */
    private String craneNo;
    /**
     * 识别数据类型（1-拖车2-集装箱3-火车车厢）
     */
    private Integer identifyType;
    /**
     * 识别箱号
     */
    private String identifyCtnNo;
    /**
     * ISO 20GP
     */
    private String identifyCtnNoIso;
    /**
     * 识别空重 0空箱，1重箱
     */
    private String identifyStuffingStatus;
    /**
     * 识别箱型 ：20/40
     */
    private String identifyContainerType;
    /**
     * 识别箱类：敞顶箱、通用箱等
     */
    private String identifyContainerClass;
    /**
     * 识别集装箱重量（单位kg）
     */
    private BigDecimal identifyContainerWeight;
    /**
     * 识别火车车厢顺位号： 1、2、3
     */
    private String identifyCarriageSeq;
    /**
     * 识别火车车厢类型:平板车、敞车
     */
    private String identifyCarriageType;
    /**
     * 识别火车的车厢号
     */
    private String identifyCarriageNo;
    /**
     * 识别的拖车号
     */
    private String identifyTruckNo;
    /**
     * 图片列表
     */
    private List<String> imgUrls;
    /**
     * 识别的车顶号
     */
    private String identifyTruckRoofNo;
}
