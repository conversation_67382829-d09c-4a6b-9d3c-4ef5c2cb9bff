package net.pingfang.model.entity;

import lombok.Data;

/**
 * @description: Gps定位数据信息
 * @title: Gps
 * @author: cb
 * @date: 2024-06-12 15:12:10
 * @version: 1.0
 */
@Data
public class Gps {
    /**
     * id
     */
    private Long id;
    /**
     * 消息id
     */
    private String infoId;
    /**
     * 消息体长度
     */
    private String msgLength;
    /**
     * 设备唯一号
     */
    private String gpsUniqueCode;
    /**
     * 消息流水号
     */
    private String seq;
    /**
     * 纬度
     */
    private String latitude;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 海拔高度
     */
    private String altitude;

    /**
     * 速度
     */
    private String speed;

    /**
     * 方向
     */
    private String direction;

    /**
     * 日期时间
     */
    private String dateTime;

    /**
     * 信号强度
     */
    private Integer signalStrength;

    /**
     * 电量
     */
    private Integer quantityOfElectricity;

    /**
     * 定位状态
     */
    private Integer positioningStatus;

    /**
     * 经度扩展
     */
    private Integer extendLongitude;
    /**
     * 纬度扩展
     */
    private Integer extendLatitude;
}
