package net.pingfang.model.entity;

import lombok.Data;

import java.util.List;

/**
 * 作业记录
 *
 * @Author: CM
 * @Date: 2022/11/4 13:59
 */
@Data
public class WorkRecord {
    /**
     * 主键
     */
    private Long id;
    /**
     * 轨道吊id
     */
    private Long craneId;
    /**
     * 作业编号
     */
    private String seqNo;
    /**
     * 轨道吊编号
     */
    private String craneNo;
    /**
     * 作业类型
     */
    private Integer workType;
    /**
     * 箱号
     */
    private String ctnNo;
    /**
     * iso
     */
    private String iso;
    /**
     * 拖车车牌
     */
    private String plateNumber;
    /**
     * 车顶号
     */
    private String topPlateNumber;
    /**
     * 火车车号
     */
    private String trainNo;
    /**
     * 车型
     */
    private String trainModel;
    /**
     * 火车车顺
     */
    private Integer trainSeq;
    /**
     * 火车股道
     */
    private String stationTrack;
    /**
     * 堆场堆位
     */
    private String yardPosition;

    /**
     * 加锁车道
     */
    private String laneNumA;
    /**
     * 解锁车道
     */
    private String laneNumB;

    /**
     * 上锁时间
     */
    private String lockTime;

    /**
     * 解锁时间
     */
    private String unLockTime;
    
    private List<WorkRecordImage> imgList;
}
