package net.pingfang.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 图片表
 * <AUTHOR>
 * @Date 2023/7/19 11:45
 * @Description:
*/
@Data
public class Img {

    /**
     * 主键
     */
    private Long id;

    /**
     * 唯一任务编号
     */
    private String seqNo;

    /**
     * 图片类型(1-箱,2-残损，3-车顶号，4-车号，5-,全景)
     */
    private Integer imgType;

    /**
     * 图片路径
     */
    private String imgUrl;

    /**
     * 相机名称
     */
    private String cameraName;
    /**
     * 相机编号
     */
    private String cameraCode;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime createTime;

    /**
     * 识别区域
     */
    private String dectRect;

}
