package net.pingfang.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
@Data
public class Lane {
    /**
     * 	主键id
     */
    private Long id;
    /**
     * 	车道编号
     */
    private String lane;

    /**
     * 排序号
     */
    private Integer seq;

    /**
     * 最低识别高度
     */
    private Integer minAltitude;

    /**
     * 	x坐标范围开始
     */
    private Integer rectXFrom;
    /**
     * X坐标范围结束
     */
    private Integer rectXTo;
    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
}
