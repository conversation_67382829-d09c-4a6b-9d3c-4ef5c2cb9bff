package net.pingfang.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * RTSP预识别结果表
 * <AUTHOR>
 * @Date 2025/06/27
 * @Description: RTSP预识别结果记录
 */
@Data
public class RtspPreRecognitionResult {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 序列号
     */
    private String seqNo;
    
    /**
     * 吊机号
     */
    private String craneNo;
    
    /**
     * 识别状态 (SUCCESS, FAILED, PROCESSING)
     */
    private String status;
    
    /**
     * 识别的箱号
     */
    private String containerNumber;
    
    /**
     * 识别置信度 (0.0-1.0)
     */
    private Float confidence;
    
    /**
     * 图片数量
     */
    private Integer imageCount;
    
    /**
     * 任务状态 (1-作业中)
     */
    private Integer taskStatus;
    
    /**
     * 作业类型 (1-8)
     */
    private Integer workType;
    
    /**
     * 箱面高度
     */
    private Float containerHeight;
    
    /**
     * 位置差异
     */
    private Float positionDifference;
    
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;
    
    /**
     * 识别耗时(毫秒)
     */
    private Long duration;
    
    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 所有相机图片信息(JSON格式)
     */
    private String allCameraImages;

    /**
     * 最佳结果相机ID
     */
    private Integer bestCameraId;

    /**
     * 最佳结果图片路径
     */
    private String bestImagePath;

    /**
     * 最佳结果图片URL
     */
    private String bestImageUrl;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createTime;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 更新时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
}
