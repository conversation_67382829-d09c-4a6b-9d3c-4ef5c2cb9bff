package net.pingfang.model.dto;

import net.pingfang.enums.RecognizeConfigTypeEnum;
import net.pingfang.enums.TrueFalseEnum;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-08-23 15:00
 */
@Data
public class QueryRecognizeConfigResDTO {

    /**
     * 	主键id
     */
    private Long id;
    /**
     * 	车道id
     */
    private Long laneId;
    /**
     * 	摄像头id
     */
    private Long cameraId;

    /**
     * 名称
     */
    private String name;
    /**
     * ip
     */
    private String ip;
    /**
     * 	预置位
     */
    private Integer presetLocation;
    /**
     * 	类型
     */
    private Integer type;
    /**
     * 类型（展示）
     */
    private String typeStr;
    public void setType(Integer type) {
        this.type = type;
        this.typeStr = RecognizeConfigTypeEnum.getDescByCode(type);
    }
    /**
     * 	roi区域(left,right,top,bottom)
     */
    private String roiRect;
    /**
     * 	视频录制（0-否 1-是）
     */
    private Integer videoRecording;
    /**
     * 视频录制（展示）
     */
    private String videoRecordingStr;
    public void setVideoRecording(Integer videoRecording) {
        this.videoRecording = videoRecording;
        this.videoRecordingStr = TrueFalseEnum.getDescByValue(videoRecording);
    }




}
