package net.pingfang.model.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * RTSP预识别列表DTO
 * 
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
public class RtspPreRecognitionListDTO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 序列号
     */
    private String seqNo;
    
    /**
     * 吊机号
     */
    private String craneNo;
    
    /**
     * 最佳结果相机ID
     */
    private Integer bestCameraId;
    
    /**
     * 推送ECS的箱号
     */
    private String bestContainerNo;
    
    /**
     * 推送ECS的ISO号
     */
    private String bestContainerIso;
    
    /**
     * 最佳结果可信度
     */
    private BigDecimal bestConfidence;
    
    /**
     * 参与识别的相机总数
     */
    private Integer cameraCount;
    
    /**
     * 预识别开始时间
     */
    private LocalDateTime startTime;
    
    /**
     * 预识别结束时间
     */
    private LocalDateTime endTime;
    

    
    /**
     * 结束原因
     */
    private String endReason;
    
    /**
     * ECS推送状态
     */
    private String ecsPushStatus;
    
    /**
     * ECS推送时间
     */
    private LocalDateTime ecsPushTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdTime;
}
