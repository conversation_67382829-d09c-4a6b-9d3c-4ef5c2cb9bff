package net.pingfang.model.dto;

import lombok.Data;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
@Data
public class SystemInfoDTO {
    /**
     * 总记录数
     */
    private Integer totalRecordCount;

    /**
     * 短箱数量
     */
    private Integer shortCtnCount;

    /**
     * 长箱数量
     */
    private Integer longCtnCount;
    /**
     * 其他箱数量
     */
    private Integer otherCtnCount;

    /**
     * 识别平均耗时(ms)
     */
    private Integer recognizeAvgTime;

    /**
     * 抓拍平均耗时(ms)
     */
    private Integer snapAvgTime;

    /**
     * 箱号校验率
     */
    private String ctnNoCheckRate;

    /**
     * 车号校验率
     */
    private String plateNumberCheckRate;


}
