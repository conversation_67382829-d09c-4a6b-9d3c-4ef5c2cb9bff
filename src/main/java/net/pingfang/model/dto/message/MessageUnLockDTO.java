package net.pingfang.model.dto.message;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-09-12 17:50
 */
@Data
public class MessageUnLockDTO {

    /**
     * 工作任务编号,每一吊都是唯一的
     */
    private String seqNo;

    /**
     * 报文发送时间
     */
    private String passtime;

    /**
     * 工作类型
     */
    private Integer workType;

    /**
     * 箱类型  0-单吊长箱  1-单吊短箱   2-孖吊 两个20尺  3-双吊 两个40尺  4-三箱吊(岸侧两个20尺,海侧一个40尺) 5-三箱吊(岸侧一个40尺,海侧两个20尺) 6-四箱吊 7-未知
     */
    private Integer containerType;

}
