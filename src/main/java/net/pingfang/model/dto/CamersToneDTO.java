package net.pingfang.model.dto;

import lombok.Data;

/**
 * 相机色调入参
 * <AUTHOR>
 * @Date 2023/7/20 18:00
 * @Description:
*/
@Data
public class CamersToneDTO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 分布式多屏控制器中，高的2个字节表示串口号，低2字节表示屏幕序号（0xffff表示所有屏幕）；集中式控制器中高2字节无效，低2字节屏幕序号从0开始
     * 暂时为空
     */
    private int dwDeviceNum;

    /**
     * 类型：1- 亮度，2- 对比度，3- 饱和度，4- 清晰度
     */
    private int byColorType;

    /**
     * 控制取值：对应类型 -1或者+1
     */
    private int byScale;

}
