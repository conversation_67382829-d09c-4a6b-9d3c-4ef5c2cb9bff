package net.pingfang.model.vo.tos;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import net.pingfang.model.dto.GpsDto;
import java.util.List;
import lombok.Data;
import lombok.ToString;

/**
 * XXX
 *
 * @author: CM
 * @date: 2024/5/22
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class TosRecordVO {
    // {"CNTR":"TBBU5196227","MACH":"1301","LON":"108.3746392","truckNo":"A02","Truck":"桂P17205","LAT":"59.012343","HEOGHT":"6","LENGHT":"12",}
    /**
     * 箱号
     */
    @JSONField(name = "CNTR")
    private String cntr;
    /**
     * 箱数量
     */
    private int ctnNum;
    /**
     * 机械编号
     */
    @JSONField(name = "MACH")
    private String mach;
    /**
     * 经度
     */
    @JSONField(name = "LON")
    private String lon;
    /**
     * 纬度
     */
    @JSONField(name = "LAT")
    private String lat;
    /**
     * 车顶编号
     */
    @JSONField(name = "TruckNo")
    private String truckNo;
    /**
     * 拖车号码
     */
    @JSONField(name = "Truck")
    private String truck;
    /**
     * 小车移动的高度(y)
     */
    @JSONField(name = "HEIGHT")
    private double height;
    /**
     * 小车移动的距离(x)
     */
    @JSONField(name = "LENGHT")
    private double lenght;
    /**
     * 车道
     */
    @JSONField(name = "PAYNO")
    private String payno;
    /**
     * 层高 1,2,3,4,5
     */
    @JSONField(name = "FLOORNO")
    private String floorno;

    /**
     * Message
     */
    @JSONField(name = "Message")
    private String message;

    /**
     * GpsList
     */
    @JSONField(name = "GpsList")
    private List<GpsDto> gpsList;

}
