package net.pingfang.model.vo.message.child;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: CM
 * @Date: 2021/5/29 11:34
 * @Description:
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class BayResultVO {
    /**
     * plc位置对象
     */
    private List<PositionVO> bay_info;

    /**
     * 文件对象
     */
    private FileInfoVO file_info;
}
