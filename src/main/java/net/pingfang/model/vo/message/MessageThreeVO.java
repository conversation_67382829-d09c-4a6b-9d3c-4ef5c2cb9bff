package net.pingfang.model.vo.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import net.pingfang.model.vo.message.child.BayResultVO;
import net.pingfang.model.vo.message.child.CcrResultVO;
import net.pingfang.model.vo.message.child.DamagedResultVO;
import net.pingfang.model.vo.message.child.PlateResultVO;
import net.pingfang.model.vo.message.child.PlcDataVO;
import net.pingfang.model.vo.message.child.TpplateResultVO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: CM
 * @Date: 2021/5/29 10:51
 * @Description: 3号报文实体类
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class MessageThreeVO {

    /**
     * 工作任务编号,每一吊都是唯一的
     */
    private String seq_no;

    /**
     * 站点名称
     */
    private String area_num;

    /**
     * 排架上锁时间
     */
    private String qcLockTime;

    /**
     * 排架解锁时间
     */
    private String qcUnLockTime;

    /**
     * 理货员
     */
    private String tally_clerk;

    /**
     * 是否报警（0为不报警，1为报警）
     */
    private String alarm_state;

    /**
     * 工作类型
     */
    private Integer work_type;

    /**
     * 报文种类
     */
    private String message_type;

    /**
     * 上锁人员(没有用)
     */
    private String lock_user;

    /**
     * 船舶航次(没有用)
     */
    private String vessel_number;

    /**
     * 上锁时间(没有用)
     */
    private String lock_time;

    /**
     * 报文发送时间
     */
    private String passtime;

    /**
     * 船名(没有用)
     */
    private String vessel_name_cn;

    /**
     * 箱类型  0-单吊长箱  1-单吊短箱   2-孖吊 两个20尺  3-双吊 两个40尺  4-三箱吊(岸侧两个20尺,海侧一个40尺) 5-三箱吊(岸侧一个40尺,海侧两个20尺) 6-四箱吊 7-未知
     */
    private Integer container_type;

    /**
     * 是否上锁(没有用)
     */
    private String is_lock;

    /**
     * 作业机械编号(岸桥/门机编号)
     */
    private String crane_num;

    /**
     * 状态(没有用)
     */
    private String state;

    /**
     * (没有用)
     */
    private String serviceID;

    /**
     * 车道(废弃, 改为用箱节点下面的lane_num)
     */
    private String lane_num;

    /**
     * 箱号识别数据节点
     */
    private CcrResultVO ccr_result;

    /**
     * 车号识别数据节点
     */
    private TpplateResultVO tpplate_result;

    /**
     * 残损数据节点
     */
    private DamagedResultVO damaged_result;

    /**
     * bay位相机节点
     */
    private BayResultVO bay_result;

    /**
     * 车牌相机数据节点
     */
    private PlateResultVO plate_result;

    /**
     * plc数据节点
     */
    private PlcDataVO plc_data;

}
