package net.pingfang.model.vo.message.child;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: CM
 * @Date: 2021/5/29 11:07
 * @Description:
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class ContaResultVO {
    /**
     * 可信度(没有用)
     */
    private Double trust;

    /**
     * 箱号
     */
    private String updateId;

    /**
     * 车上位置  A  M   F
     */
    private String note;

    /**
     * iso
     */
    private String iso;

    /**
     * 箱号(没有用)
     */
    private String id;

    /**
     * 没有用
     */
    private Boolean check;

    /**
     * 箱序号,bay位识别用, 靠岸侧左边开始数, 从左到右, 从岸侧到海侧依次为1,2,3,4(四个箱), 1,2,3(三个箱), 1,2(两个箱),1(一个箱)
     */
    private String seq;

    /**
     * 车顶号
     */
    private String update_top_plate;

    /**
     * 车道号
     */
    private String lane_num;
}
