package net.pingfang.model.vo.message.child;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: CM
 * @Date: 2021/5/29 11:31
 * @Description:
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class DamagedResultVO {
    /**
     * 残损信息
     */
    private List<DamageInfoVO> damaged_info;

    /**
     * 文件对象
     */
    private FileInfoVO file_info;

    /**
     * 残损图片数量
     */
    private Integer com_damaged_num;
}
