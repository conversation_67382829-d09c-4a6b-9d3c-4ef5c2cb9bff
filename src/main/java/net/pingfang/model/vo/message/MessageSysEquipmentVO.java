package net.pingfang.model.vo.message;

import net.pingfang.model.vo.message.sys.child.SysEquipmentVO;
import java.util.List;
import lombok.Data;

/**
 * 界面测试-设备
 * <AUTHOR>
 * @since 2022-11-09 13:46
 */
@Data
public class MessageSysEquipmentVO {

    /**
     * 岸桥编号
     */
    private String craneNo;

    /**
     * 码头名称
     */
    private String wharf;

    /**
     * 设备信息
     */
    private List<SysEquipmentVO> equipmentList;
}
