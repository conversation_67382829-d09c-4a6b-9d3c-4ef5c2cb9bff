package net.pingfang.model.vo.message.child;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: CM
 * @Date: 2021/5/29 11:06
 * @Description:
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class CcrResultVO {
    /**
     * 箱号结果集
     */
    private List<ContaResultVO> conta_result;

    /**
     * 箱号识别图片对象
     */
    private FileInfoVO file_info;

    /**
     * plc重量
     */
    private Integer conta_weight;

    /**
     * 箱门方向  A->朝向车尾  F->朝向车头  UNKNOWN--未知
     */
    private List<String> door_dir;

    /**
     * 铅封有无  true-有, false-无  UNKNOWN--未知
     */
    private List<String> door_lock;

    /**
     * 危标有无  true-有, false-无  UNKNOWN--未知
     */
    private List<String> dangerous;
}
