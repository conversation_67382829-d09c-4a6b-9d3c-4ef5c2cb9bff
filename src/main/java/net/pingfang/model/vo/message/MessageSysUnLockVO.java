package net.pingfang.model.vo.message;

import net.pingfang.model.vo.message.sys.child.SysImgVO;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022-11-09 9:28
 */
@Data
public class MessageSysUnLockVO {

    /**
     * 唯一任务编号
     */
    private String seqNo;

    /**
     * 码头名称
     */
    private String wharf;

    /**
     * 作业类型
     */
    private Integer workType;

    /**
     * 岸桥编号
     */
    private String craneNo;

    /**
     * plc x坐标
     */
    private Double plcX;

    /**
     * plc y坐标
     */
    private Double plcY;

    /**
     * 车道
     */
    private String lane;

    /**
     * 箱型
     */
    private Integer containerType;

    /**
     * 图片信息
     */
    private List<SysImgVO> imgList;

}
