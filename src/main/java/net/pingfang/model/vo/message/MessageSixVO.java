package net.pingfang.model.vo.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import net.pingfang.model.vo.message.child.CameraVO;
import net.pingfang.model.vo.message.child.PlcVO;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: CM
 * @Date: 2021/5/31 9:42
 * @Description: 06号报文对象
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class MessageSixVO {
    /**
     * 站点名称
     */
    private String areaName;

    /**
     * 作业机械编号(岸桥/门机编号)
     */
    private String craneName;

    /**
     * plc对象
     */
    private PlcVO plc;

    /**
     * 报文种类 01-上锁报文, 02-解锁报文, 03- 识别报文, 06-设备监控  16-预识别
     */
    private String message_type;

    /**
     * 时间
     */
    private String time;

    /**
     * 状态(没有用)1-正常  2-异常 默认1就行
     */
    private Integer status;

    /**
     * 摄像头集合
     */
    private List<CameraVO> cameraList;
}
