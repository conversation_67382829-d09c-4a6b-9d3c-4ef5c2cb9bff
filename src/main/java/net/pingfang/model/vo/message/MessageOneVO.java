package net.pingfang.model.vo.message;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import net.pingfang.model.vo.message.child.BayResultVO;
import net.pingfang.model.vo.message.child.PlcDataVO;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: CM
 * @Date: 2021/5/29 11:51
 * @Description:
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class MessageOneVO {
    /**
     * 工作任务编号,每一吊都是唯一的
     */
    private String seq_no;

    /**
     * 站点名称
     */
    private String area_num;

    /**
     * 工作类型
     */
    private Integer work_type;

    /**
     * 报文种类 01-上锁报文, 02-解锁报文, 03- 识别报文, 06-设备监控  16-预识别
     */
    private String message_type;

    /**
     * 报文发送时间
     */
    private String passtime;

    /**
     * 箱类型  0-单吊长箱  1-单吊短箱   2-孖吊 两个20尺  3-双吊 两个40尺  4-三箱吊(岸侧两个20尺,海侧一个40尺) 5-三箱吊(岸侧一个40尺,海侧两个20尺) 6-四箱吊 7-未知
     */
    private Integer container_type;

    /**
     * 作业机械编号(岸桥/门机编号)
     */
    private String crane_num;

    /**
     * (没有用)
     */
    private String serviceID;

    /**
     * bay位相机节点
     */
    private BayResultVO bay_result;

    /**
     * plc对象
     */
    private PlcDataVO plc_data;

    /**
     * 没有用
     */
    private String cmd_type;

    /**
     * 没有用
     */
    private String qcLockTime;

    /**
     * 没有用
     */
    private String qcUnLockTime;

    /**
     * 车道号？
     */
    private String lane_num;

}
