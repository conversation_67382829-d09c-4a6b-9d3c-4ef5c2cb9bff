package net.pingfang.model.vo.message.child;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: CM
 * @Date: 2021/5/31 9:46
 * @Description:
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class CameraVO {
    /**
     * 摄像头ip
     */
    private String ip;

    /**
     * 摄像头名称
     */
    private String name;

    /**
     * 摄像头状态  1-正常  2-异常
     */
    private Integer status;
}
