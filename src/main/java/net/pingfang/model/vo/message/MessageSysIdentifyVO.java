package net.pingfang.model.vo.message;

import net.pingfang.model.vo.message.sys.child.SysContainerVO;
import net.pingfang.model.vo.message.sys.child.SysImgVO;
import java.util.List;
import lombok.Data;

/**
 * 界面测试-识别报文
 * <AUTHOR>
 * @since 2022-11-09 10:01
 */
@Data
public class MessageSysIdentifyVO {

    /**
     * 作业编号
     */
    private String seqNo;

    /**
     * 码头名称
     */
    private String wharf;
    /**
     * 岸桥编号
     */
    private String craneNo;

    /**
     * 作业类型
     */
    private Integer workType;

    /**
     * 箱类型
     */
    private Integer containerType;

    /**
     * 重量
     */
    private Double weight;

    /**
     * 箱数据
     */
    private List<SysContainerVO> containerList;

    /**
     * 图片数据
     */
    private List<SysImgVO> imgList;

}
