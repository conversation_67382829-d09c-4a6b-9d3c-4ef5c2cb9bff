package net.pingfang.model.vo.message.sys.child;

import java.util.List;
import lombok.Data;

/**
 * 界面测试-箱数据
 * <AUTHOR>
 * @since 2022-11-09 10:06
 */
@Data
public class SysContainerVO {

    /**
     * 箱号
     */
    private String ctnNo;

    /**
     * iso
     */
    private String iso;

    /**
     * 车号
     */
    private String truckNo;

    /**
     * 车道
     */
    private String lane;

    /**
     * 箱门朝向
     */
    private String doorDir;

    /**
     * 是否有铅封
     */
    private Integer isSealNo;

    /**
     * 是否有危标
     */
    private Integer isDanger;

    /**
     * 车上位置
     */
    private String truckPosition;

    /**
     * 箱序号
     */
    private Integer seq;

    /**
     * 残损数据
     */
    private List<SysDamageVO> damageList;

}
