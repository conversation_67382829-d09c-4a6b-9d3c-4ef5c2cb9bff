package net.pingfang.model.vo.business;

import net.pingfang.model.entity.RecognizeConfig;
import lombok.Data;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/8/18
 */
@Data
public class RecognizeConfigVO extends RecognizeConfig {

    /**
     * 名称
     */
    private String name;
    /**
     * 编号
     */
    private String code;
    /**
     * ip
     */
    private String ip;
    /**
     * 端口
     */
    private Integer port;
    /**
     * 品牌
     */
    private String brand;

    /**
     * 通道
     */
    private Integer channel;

    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;

    /**
     * 相机控制端口
     */
    private Integer controlPort;


}
