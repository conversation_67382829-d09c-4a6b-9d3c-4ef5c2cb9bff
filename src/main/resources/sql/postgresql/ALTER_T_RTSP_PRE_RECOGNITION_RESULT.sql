-- 快速修复：为现有表添加缺失字段
-- <AUTHOR>
-- @Date 2025/06/27
-- @Description: 为T_RTSP_PRE_RECOGNITION_RESULT表添加缺失的字段

-- 添加缺失的字段
ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "status" VARCHAR(20) NOT NULL DEFAULT 'PROCESSING';

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "container_number" VARCHAR(20);

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "confidence" REAL;

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "image_count" INTEGER DEFAULT 0;

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "task_status" INTEGER;

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "work_type" INTEGER;

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "container_height" REAL;

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "position_difference" REAL;

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "start_time" TIMESTAMP;

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "end_time" TIMESTAMP;

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "duration" BIGINT;

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "error_message" VARCHAR(500);

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "create_by" VARCHAR(50);

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "update_by" VARCHAR(50);

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "update_time" TIMESTAMP DEFAULT CURRENT_TIMESTAMP;

-- 添加索引（如果不存在）
CREATE INDEX IF NOT EXISTS "idx_rtsp_result_seq_no" ON "T_RTSP_PRE_RECOGNITION_RESULT"("seq_no");
CREATE INDEX IF NOT EXISTS "idx_rtsp_result_crane_no" ON "T_RTSP_PRE_RECOGNITION_RESULT"("crane_no");
CREATE INDEX IF NOT EXISTS "idx_rtsp_result_status" ON "T_RTSP_PRE_RECOGNITION_RESULT"("status");
CREATE INDEX IF NOT EXISTS "idx_rtsp_result_create_time" ON "T_RTSP_PRE_RECOGNITION_RESULT"("create_time");

-- 添加字段注释
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."status" IS '识别状态(SUCCESS,FAILED,PROCESSING)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."container_number" IS '识别的箱号';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."confidence" IS '识别置信度(0.0-1.0)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."image_count" IS '图片数量';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."task_status" IS '任务状态(1-作业中)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."work_type" IS '作业类型(1-8)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."container_height" IS '箱面高度';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."position_difference" IS '位置差异';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."start_time" IS '开始时间';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."end_time" IS '结束时间';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."duration" IS '识别耗时(毫秒)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."error_message" IS '错误信息';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."create_by" IS '创建人';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."update_by" IS '更新人';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."update_time" IS '更新时间';

-- 创建更新时间触发器函数（如果不存在）
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."update_time" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为表创建更新时间触发器（如果不存在）
DROP TRIGGER IF EXISTS update_rtsp_result_updated_time ON "T_RTSP_PRE_RECOGNITION_RESULT";
CREATE TRIGGER update_rtsp_result_updated_time 
    BEFORE UPDATE ON "T_RTSP_PRE_RECOGNITION_RESULT" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();
