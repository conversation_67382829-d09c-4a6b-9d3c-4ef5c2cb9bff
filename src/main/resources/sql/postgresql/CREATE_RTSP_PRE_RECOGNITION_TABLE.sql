-- RTSP预识别结果表建表语句 (PostgreSQL)
-- <AUTHOR>
-- @Date 2025/06/27
-- @Description: RTSP预识别结果表，包含图片信息JSON字段

-- 删除表（如果存在）
DROP TABLE IF EXISTS "T_RTSP_PRE_RECOGNITION_RESULT";

-- 创建主表：RTSP预识别结果表
CREATE TABLE "T_RTSP_PRE_RECOGNITION_RESULT" (
    "id" BIGSERIAL PRIMARY KEY,
    "seq_no" VARCHAR(50) NOT NULL,
    "crane_no" VARCHAR(20) NOT NULL,
    
    -- 识别结果信息
    "status" VARCHAR(20) NOT NULL DEFAULT 'PROCESSING',
    "container_number" VARCHAR(20),
    "confidence" REAL,
    "image_count" INTEGER DEFAULT 0,
    
    -- 触发条件信息
    "task_status" INTEGER,
    "work_type" INTEGER,
    "container_height" REAL,
    "position_difference" REAL,
    
    -- 时间信息
    "start_time" TIMESTAMP,
    "end_time" TIMESTAMP,
    "duration" BIGINT,
    
    -- 错误信息
    "error_message" VARCHAR(500),
    
    -- 图片信息 (JSON格式存储所有相机图片信息)
    "all_camera_images" TEXT,
    
    -- 最佳结果信息
    "best_camera_id" INTEGER,
    "best_image_path" VARCHAR(500),
    "best_image_url" VARCHAR(500),
    
    -- 标准字段
    "create_by" VARCHAR(50),
    "create_time" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "update_by" VARCHAR(50),
    "update_time" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX "idx_rtsp_result_seq_no" ON "T_RTSP_PRE_RECOGNITION_RESULT"("seq_no");
CREATE INDEX "idx_rtsp_result_crane_no" ON "T_RTSP_PRE_RECOGNITION_RESULT"("crane_no");
CREATE INDEX "idx_rtsp_result_status" ON "T_RTSP_PRE_RECOGNITION_RESULT"("status");
CREATE INDEX "idx_rtsp_result_create_time" ON "T_RTSP_PRE_RECOGNITION_RESULT"("create_time");
CREATE INDEX "idx_rtsp_result_container_number" ON "T_RTSP_PRE_RECOGNITION_RESULT"("container_number");

-- 添加表注释
COMMENT ON TABLE "T_RTSP_PRE_RECOGNITION_RESULT" IS 'RTSP预识别结果表';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."id" IS '主键ID';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."seq_no" IS '序列号';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."crane_no" IS '吊机号';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."status" IS '识别状态(SUCCESS,FAILED,PROCESSING)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."container_number" IS '识别的箱号';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."confidence" IS '识别置信度(0.0-1.0)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."image_count" IS '图片数量';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."task_status" IS '任务状态(1-作业中)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."work_type" IS '作业类型(1-8)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."container_height" IS '箱面高度';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."position_difference" IS '位置差异';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."start_time" IS '开始时间';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."end_time" IS '结束时间';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."duration" IS '识别耗时(毫秒)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."error_message" IS '错误信息';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."all_camera_images" IS '所有相机图片信息(JSON格式)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."best_camera_id" IS '最佳结果相机ID';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."best_image_path" IS '最佳结果图片路径';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."best_image_url" IS '最佳结果图片URL';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."create_by" IS '创建人';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."create_time" IS '创建时间';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."update_by" IS '更新人';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."update_time" IS '更新时间';

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."update_time" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为表创建更新时间触发器
CREATE TRIGGER update_rtsp_result_updated_time 
    BEFORE UPDATE ON "T_RTSP_PRE_RECOGNITION_RESULT" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

-- 插入测试数据
INSERT INTO "T_RTSP_PRE_RECOGNITION_RESULT" 
(
    "seq_no", 
    "crane_no", 
    "status", 
    "container_number", 
    "confidence", 
    "image_count", 
    "task_status", 
    "work_type", 
    "container_height", 
    "position_difference", 
    "start_time", 
    "end_time", 
    "duration", 
    "all_camera_images",
    "best_camera_id",
    "best_image_path",
    "best_image_url",
    "create_by"
)
VALUES 
(
    'SEQ20250627001', 
    'RMG01', 
    'SUCCESS', 
    'ABCD1234567', 
    0.95, 
    4, 
    1, 
    2, 
    12.5, 
    1.2, 
    '2025-06-27 14:30:00', 
    '2025-06-27 14:30:15', 
    15000,
    '{
        "cameras": [
            {
                "cameraId": 1,
                "cameraName": "相机1",
                "imageName": "IMG_20250627_143001_CAM1.jpg",
                "imagePath": "/rtsp/images/2025/06/27/IMG_20250627_143001_CAM1.jpg",
                "imageUrl": "http://localhost:8080/api/images/rtsp/2025/06/27/IMG_20250627_143001_CAM1.jpg",
                "fileSize": 1024000,
                "imageWidth": 1920,
                "imageHeight": 1080,
                "containerNo": "ABCD1234567",
                "containerIso": "22G1",
                "confidence": 0.95,
                "checkPassed": true,
                "captureTime": "2025-06-27 14:30:01"
            },
            {
                "cameraId": 2,
                "cameraName": "相机2",
                "imageName": "IMG_20250627_143002_CAM2.jpg",
                "imagePath": "/rtsp/images/2025/06/27/IMG_20250627_143002_CAM2.jpg",
                "imageUrl": "http://localhost:8080/api/images/rtsp/2025/06/27/IMG_20250627_143002_CAM2.jpg",
                "fileSize": 1056000,
                "imageWidth": 1920,
                "imageHeight": 1080,
                "containerNo": "ABCD1234567",
                "containerIso": "22G1",
                "confidence": 0.92,
                "checkPassed": true,
                "captureTime": "2025-06-27 14:30:02"
            },
            {
                "cameraId": 3,
                "cameraName": "相机3",
                "imageName": "IMG_20250627_143003_CAM3.jpg",
                "imagePath": "/rtsp/images/2025/06/27/IMG_20250627_143003_CAM3.jpg",
                "imageUrl": "http://localhost:8080/api/images/rtsp/2025/06/27/IMG_20250627_143003_CAM3.jpg",
                "fileSize": 998000,
                "imageWidth": 1920,
                "imageHeight": 1080,
                "containerNo": "ABCD1234567",
                "containerIso": "22G1",
                "confidence": 0.88,
                "checkPassed": true,
                "captureTime": "2025-06-27 14:30:03"
            },
            {
                "cameraId": 4,
                "cameraName": "相机4",
                "imageName": "IMG_20250627_143004_CAM4.jpg",
                "imagePath": "/rtsp/images/2025/06/27/IMG_20250627_143004_CAM4.jpg",
                "imageUrl": "http://localhost:8080/api/images/rtsp/2025/06/27/IMG_20250627_143004_CAM4.jpg",
                "fileSize": 1120000,
                "imageWidth": 1920,
                "imageHeight": 1080,
                "containerNo": "ABCD1234567",
                "containerIso": "22G1",
                "confidence": 0.90,
                "checkPassed": true,
                "captureTime": "2025-06-27 14:30:04"
            }
        ],
        "totalCount": 4,
        "bestCameraId": 1,
        "averageConfidence": 0.9125
    }',
    1,
    '/rtsp/images/2025/06/27/IMG_20250627_143001_CAM1.jpg',
    'http://localhost:8080/api/images/rtsp/2025/06/27/IMG_20250627_143001_CAM1.jpg',
    'SYSTEM'
),
(
    'SEQ20250627002', 
    'RMG01', 
    'FAILED', 
    'NO_RESULT', 
    0.0, 
    2, 
    1, 
    3, 
    10.2, 
    0.8, 
    '2025-06-27 15:15:00', 
    '2025-06-27 15:15:30', 
    30000,
    '{
        "cameras": [
            {
                "cameraId": 1,
                "cameraName": "相机1",
                "imageName": "IMG_20250627_151501_CAM1.jpg",
                "imagePath": "/rtsp/images/2025/06/27/IMG_20250627_151501_CAM1.jpg",
                "imageUrl": "http://localhost:8080/api/images/rtsp/2025/06/27/IMG_20250627_151501_CAM1.jpg",
                "fileSize": 890000,
                "imageWidth": 1920,
                "imageHeight": 1080,
                "containerNo": null,
                "containerIso": null,
                "confidence": 0.0,
                "checkPassed": false,
                "captureTime": "2025-06-27 15:15:01"
            },
            {
                "cameraId": 3,
                "cameraName": "相机3",
                "imageName": "IMG_20250627_151503_CAM3.jpg",
                "imagePath": "/rtsp/images/2025/06/27/IMG_20250627_151503_CAM3.jpg",
                "imageUrl": "http://localhost:8080/api/images/rtsp/2025/06/27/IMG_20250627_151503_CAM3.jpg",
                "fileSize": 920000,
                "imageWidth": 1920,
                "imageHeight": 1080,
                "containerNo": null,
                "containerIso": null,
                "confidence": 0.0,
                "checkPassed": false,
                "captureTime": "2025-06-27 15:15:03"
            }
        ],
        "totalCount": 2,
        "bestCameraId": null,
        "averageConfidence": 0.0
    }',
    null,
    null,
    null,
    'SYSTEM'
);

-- 建表完成提示
SELECT 'RTSP预识别结果表创建完成！包含测试数据。' AS message;
