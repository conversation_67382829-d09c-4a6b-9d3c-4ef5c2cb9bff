server:
  port: 8080

spring:
  application:
    name: pingfang-rmg-identify
  datasource:
    driver-class-name: org.postgresql.Driver
    url: ***************************************
    username: postgres
    password: password
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false

# 平方科技配置
pingfang:
  # 图片存储配置
  picture:
    localPath: D:/PFKJ/image/
    rmgName: TRMG01
    iisUrl: http://localhost:8080
    size: 200
    frontend:
      baseUrl: http://localhost:8080
      urlPrefix: img
  
  # 车牌识别配置
  license-plate:
    enabled: true
    crane-no: TRMG01
    camera-ips:
      - *************
      - *************
    port: 30000
    reconnect-interval: 5000
    connection-timeout: 5
    image-save-path: D:/license_plate_images/
    encryption-enabled: false



# 相机配置
camera:
  # 侧边梁相机配置（用于集卡到堆场-2，集卡到火车-6）
  side-beam:
    enabled: true
    cameras:
      - name: 侧边梁相机1
        ip: *************
        port: 8000
        control-port: 8000
        username: admin
        password: pfkj2016
        channel: 1
        type: 2  # 枪机
        preset-location: 1
      - name: 侧边梁相机2
        ip: *************
        port: 8000
        control-port: 8000
        username: admin
        password: pfkj2016
        channel: 1
        type: 2  # 枪机
        preset-location: 2
  
  # 吊具摄像机配置（用于其他作业类型1,3,4,5,7,8）
  spreader:
    enabled: true
    cameras:
      - name: 吊具摄像机1
        ip: *************
        port: 8000
        control-port: 8000
        username: admin
        password: pfkj2016
        channel: 1
        type: 1  # 球机
        preset-location: 10
      - name: 吊具摄像机2
        ip: *************
        port: 8000
        control-port: 8000
        username: admin
        password: pfkj2016
        channel: 1
        type: 1  # 球机
        preset-location: 11
      - name: 吊具摄像机3
        ip: *************
        port: 8000
        control-port: 8000
        username: admin
        password: pfkj2016
        channel: 1
        type: 1  # 球机
        preset-location: 12
      - name: 吊具摄像机4
        ip: *************
        port: 8000
        control-port: 8000
        username: admin
        password: pfkj2016
        channel: 1
        type: 1  # 球机
        preset-location: 13

# RTSP视频流配置
rtsp:
  # RTSP地址列表（4个吊具摄像机）
  url-list:
    - rtsp://admin:pfkj2016@*************:554/h264/ch1/main/av_stream
    - rtsp://admin:pfkj2016@*************:554/h264/ch1/main/av_stream
    - rtsp://admin:pfkj2016@*************:554/h264/ch1/main/av_stream
    - rtsp://admin:pfkj2016@*************:554/h264/ch1/main/av_stream
  
  # 对应的相机ID列表
  camera-id-list:
    - 15
    - 13
    - 14
    - 12
  
  fps: 5
  end-delay-time: 1
  
  # 识别高度范围配置
  recognition:
    min-height: 5.0
    max-height: 7.5
  
  # 智能结束条件配置
  end:
    # 高可信度结束条件
    high-confidence:
      enabled: true
      threshold: 0.95
      consecutive-count: 3
    
    # 距离过近结束条件
    too-close:
      enabled: true
      height-threshold: 3.0
    
    # 超时结束条件
    timeout:
      enabled: true
      max-seconds: 60
  
  # 视频录制配置
  video:
    recording:
      enabled: true
      shutdown-hook: true
    codec: H264
    bitrate: 2000000
    format: mp4
    frame-rate: 25
    width: 1280
    height: 720

# 识别功能开关
identity:
  # 是否开启RTSP预识别
  if-rtsp-detect: true
  # 是否开启车顶号持续抓拍识别
  if-capture-recognition-top-plate: false
  # 是否开启车牌识别
  if-recognition-plate-number: false
  # 是否开启箱号持续抓拍识别
  if-capture-recognition-container: false

# 车顶号识别配置
truck-top-recognition:
  timeout: 30
  interval: 2

# PLC配置
plc:
  # 是否修正PLC值
  if-fix-plc-value: true
  # PLC变化幅度临界值
  change-value: 20000

# ECS系统配置
ecs:
  api:
    url: http://10.66.230.101:8882
    identify:
      path: /ocrCrane/receiveIdentifyInfo

# 算法动态库文件地址
dll:
  # 海康动态库文件地址
  hk: D:\\PFKJ\\application\\lib\\HCNetSDK.dll
  # 箱号识别算法
  ocr-container: D:\\PFKJ\\ai\\algorithm\\AILib_container.dll
  # 车顶号识别算法
  top-plate: D:\\PFKJ\\ai\\algorithm\\AILib_vehicle.dll
  # 车牌识别算法
  plate-three: D:\\PFKJ\\ai\\algorithm\\PFLPRDevice_x64.dll

# 日志清理配置
logging:
  cleanup:
    enabled: true
    retention-days: 30
    path: D:/log/rmg

# 项目版本
project:
  version: 1.0.0
