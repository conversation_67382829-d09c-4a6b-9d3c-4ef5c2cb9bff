<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>吊具摄像头识别测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, select, button {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .btn-group {
            display: flex;
            gap: 10px;
        }
        .btn-group button {
            flex: 1;
        }
    </style>
</head>
<body>
    <h1>🔍 吊具摄像头箱号识别测试</h1>
    
    <div class="container">
        <h2>测试参数</h2>
        <div class="form-group">
            <label for="imagePath">图片路径:</label>
            <input type="text" id="imagePath" placeholder="例如: D:/test/container.jpg" 
                   value="D:/test/container.jpg">
        </div>
        
        <div class="form-group">
            <label for="cameraID">相机ID:</label>
            <select id="cameraID">
                <option value="15">15 (IP: *************)</option>
                <option value="13">13 (IP: *************)</option>
                <option value="14">14 (IP: *************)</option>
                <option value="12">12 (IP: *************)</option>
            </select>
        </div>
        
        <div class="btn-group">
            <button onclick="testFileMethod()">测试文件方式</button>
            <button onclick="testMemoryMethod()">测试内存方式</button>
            <button onclick="testCompare()">对比测试</button>
        </div>
    </div>
    
    <div class="container">
        <h2>测试结果</h2>
        <div id="result" class="result">等待测试...</div>
    </div>

    <script>
        const API_BASE = '/api/test/spreader';
        
        function getParams() {
            return {
                imagePath: document.getElementById('imagePath').value,
                cameraID: document.getElementById('cameraID').value
            };
        }
        
        function showResult(data, isSuccess = true) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result ' + (isSuccess ? 'success' : 'error');
            resultDiv.textContent = JSON.stringify(data, null, 2);
        }
        
        function showLoading() {
            const resultDiv = document.getElementById('result');
            resultDiv.className = 'result';
            resultDiv.textContent = '测试中，请稍候...';
        }
        
        async function testFileMethod() {
            showLoading();
            try {
                const params = getParams();
                const response = await fetch(`${API_BASE}/file`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams(params)
                });
                
                const result = await response.json();
                showResult(result, result.success);
            } catch (error) {
                showResult({error: error.message}, false);
            }
        }
        
        async function testMemoryMethod() {
            showLoading();
            try {
                const params = getParams();
                const response = await fetch(`${API_BASE}/memory`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams(params)
                });
                
                const result = await response.json();
                showResult(result, result.success);
            } catch (error) {
                showResult({error: error.message}, false);
            }
        }
        
        async function testCompare() {
            showLoading();
            try {
                const params = getParams();
                const response = await fetch(`${API_BASE}/compare`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: new URLSearchParams(params)
                });
                
                const result = await response.json();
                showResult(result, result.success);
            } catch (error) {
                showResult({error: error.message}, false);
            }
        }
    </script>
</body>
</html>
