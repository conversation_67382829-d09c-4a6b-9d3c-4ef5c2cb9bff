<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter" />

    <!-- 日志文件路径配置 -->
    <property name="LOG_PATH" value="D:/log/rmg" />
    <property name="LOG_PATTERN" value="%d{yyyy/MM/dd HH:mm:ss.SSS} %p[TraceId:%3X{traceId}] [%logger][%M][%L] : %m%n" />

    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder charset="UTF-8">
            <!-- 输出日志记录格式 -->
            <pattern>%d{yyyy/MM/dd HH:mm:ss.SSS} %clr(%p)[TraceId:%3X{traceId}] [%logger][%M][%L] : %m%n</pattern>
        </encoder>
    </appender>

    <!-- 应用日志文件输出 - 按天滚动，保留30天 -->
    <appender name="APP_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/app/app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 按天滚动，文件名格式：app-2025-07-03.log -->
            <fileNamePattern>${LOG_PATH}/app/app-%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 保留30天的日志文件 -->
            <maxHistory>30</maxHistory>
            <!-- 总日志文件大小限制 -->
            <totalSizeCap>20GB</totalSizeCap>
            <!-- 启动时清理过期文件 -->
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 错误日志文件输出 - 单独记录ERROR级别日志 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/error/error.log</file>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/error/error-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>60</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- RTSP相关日志 - 单独记录RTSP操作日志 -->
    <appender name="RTSP_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/rtsp/rtsp.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/rtsp/rtsp-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- PLC相关日志 - 单独记录PLC操作日志 -->
    <appender name="PLC_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/plc/plc.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/plc/plc-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>5GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 识别相关日志 - 单独记录识别操作日志 -->
    <appender name="RECOGNITION_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/recognition/recognition.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/recognition/recognition-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>10GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder charset="UTF-8">
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 特定包的日志配置 -->
    <!-- RTSP相关类的日志 -->
    <logger name="net.pingfang.core.hardware.rtsp" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="RTSP_FILE" />
        <appender-ref ref="APP_FILE" />
    </logger>

    <!-- PLC相关类的日志 -->
    <logger name="net.pingfang.core.hardware.plc" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="PLC_FILE" />
        <appender-ref ref="APP_FILE" />
    </logger>

    <!-- 识别相关类的日志 -->
    <logger name="net.pingfang.core.recognition" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="RECOGNITION_FILE" />
        <appender-ref ref="APP_FILE" />
    </logger>

    <!-- 设置日志输出级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="APP_FILE" />
        <appender-ref ref="ERROR_FILE" />
    </root>
</configuration>
