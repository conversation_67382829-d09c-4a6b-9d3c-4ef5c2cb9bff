<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.pingfang.mapper.RecognitionMapper">
    <insert id="add">
        INSERT INTO "T_RECORD"
            ("id",
             "seq_no",
             "work_type",
             "container_type",
             "ctn_num",
             "ctn_no_a",
             "iso_no_a",
             "dangerous_lable_a",
             "seal_a",
             "plate_location_a",
             "trust_a",
             "ctn_no_b",
             "iso_no_b",
             "dangerous_lable_b",
             "seal_b",
             "plate_location_b",
             "trust_b",
             "ctn_no_c",
             "iso_no_c",
             "dangerous_lable_c",
             "seal_c",
             "plate_location_c",
             "trust_c",
             "ctn_no_d",
             "iso_no_d",
             "dangerous_lable_d",
             "seal_d",
             "plate_location_d",
             "trust_d",
             "plate_number_a",
             "plate_dir_a",
             "plate_number_b",
             "plate_dir_b",
             "lane_num_a",
             "lane_num_b",
             "door_dir_a",
             "door_dir_b",
             "door_dir_c",
             "door_dir_d",
             "weight",
             "recognize_time_consuming",
             "snap_time_consuming",
             "ctn_check_a",
             "ctn_check_b",
             "ctn_check_c",
             "ctn_check_d",
             "plate_check_a",
             "plate_check_b",
             "pass_time",
             "lock_time")
        VALUES (
                #{param.id},
                #{param.seqNo},
                #{param.workType},
                #{param.containerType},
                #{param.ctnNum},
                #{param.ctnNoA},
                #{param.isoNoA},
                #{param.dangerousLableA},
                #{param.sealA},
                #{param.plateLocationA},
                #{param.trustA},
                #{param.ctnNoB},
                #{param.isoNoB},
                #{param.dangerousLableB},
                #{param.sealB},
                #{param.plateLocationB},
                #{param.trustB},
                #{param.ctnNoC},
                #{param.isoNoC},
                #{param.dangerousLableC},
                #{param.sealC},
                #{param.plateLocationC},
                #{param.trustC},
                #{param.ctnNoD},
                #{param.isoNoD},
                #{param.dangerousLableD},
                #{param.sealD},
                #{param.plateLocationD},
                #{param.trustD},
                #{param.plateNumberA},
                #{param.plateDirA},
                #{param.plateNumberB},
                #{param.plateDirB},
                #{param.laneNumA},
                #{param.laneNumB},
                #{param.doorDirA},
                #{param.doorDirB},
                #{param.doorDirC},
                #{param.doorDirD},
                #{param.weight},
                #{param.recognizeTimeConsuming},
                #{param.snapTimeConsuming},
                #{param.ctnCheckA},
                #{param.ctnCheckB},
                #{param.ctnCheckC},
                #{param.ctnCheckD},
                #{param.plateCheckA},
                #{param.plateCheckB},
                #{param.passTime},
                #{param.lockTime}
               )
    </insert>
    <update id="update">
        UPDATE "T_RECORD"
        <set>
            <if test="param.ctnNum != null and param.ctnNum != ''">
                "ctn_num" = #{param.ctnNum},
            </if>
            <if test="param.ctnNoA != null and param.ctnNoA != ''">
                "ctn_no_a" = #{param.ctnNoA},
            </if>
            <if test="param.isoNoA != null and param.isoNoA != ''">
                "iso_no_a" = #{param.isoNoA},
            </if>
            <if test="param.dangerousLableA != null and param.dangerousLableA != ''">
                "dangerous_lable_a" = #{param.dangerousLableA},
            </if>
            <if test="param.sealA != null and param.sealA != ''">
                "seal_a" = #{param.sealA},
            </if>
            <if test="param.plateLocationA != null and param.plateLocationA != ''">
                "plate_location_a" = #{param.plateLocationA},
            </if>
            <if test="param.trustA != null and param.trustA != ''">
                "trust_a" = #{param.trustA},
            </if>
            <if test="param.ctnNoB != null and param.ctnNoB != ''">
                "ctn_no_b" = #{param.ctnNoB},
            </if>
            <if test="param.isoNoB != null and param.isoNoB != ''">
                "iso_no_b" = #{param.isoNoB},
            </if>
            <if test="param.dangerousLableB != null and param.dangerousLableB != ''">
                "dangerous_lable_b" = #{param.dangerousLableB},
            </if>
            <if test="param.sealB != null and param.sealB != ''">
                "seal_b" = #{param.sealB},
            </if>
            <if test="param.plateLocationB != null and param.plateLocationB != ''">
                "plate_location_b" = #{param.plateLocationB},
            </if>
            <if test="param.trustB != null and param.trustB != ''">
                "trust_b" = #{param.trustB},
            </if>
            <if test="param.ctnNoC != null and param.ctnNoC != ''">
                "ctn_no_c" = #{param.ctnNoC},
            </if>
            <if test="param.isoNoC != null and param.isoNoC != ''">
                "iso_no_c" = #{param.isoNoC},
            </if>
            <if test="param.dangerousLableC != null and param.dangerousLableC != ''">
                "dangerous_lable_c" = #{param.dangerousLableC},
            </if>
            <if test="param.sealC != null and param.sealC != ''">
                "seal_c" = #{param.sealC},
            </if>
            <if test="param.height != null and param.height != ''">
                "height" = #{param.height},
            </if>
            <if test="param.plateLocationC != null and param.plateLocationC != ''">
                "plate_location_c" = #{param.plateLocationC},
            </if>
            <if test="param.trustC != null and param.trustC != ''">
                "trust_c" = #{param.trustC},
            </if>
            <if test="param.ctnNoD != null and param.ctnNoD != ''">
                "ctn_no_d" = #{param.ctnNoD},
            </if>
            <if test="param.isoNoD != null and param.isoNoD != ''">
                "iso_no_d" = #{param.isoNoD},
            </if>
            <if test="param.dangerousLableD != null and param.dangerousLableD != ''">
                "dangerous_lable_d" = #{param.dangerousLableD},
            </if>
            <if test="param.sealD != null and param.sealD != ''">
                "seal_d" = #{param.sealD},
            </if>
            <if test="param.plateLocationD != null and param.plateLocationD != ''">
                "plate_location_d" = #{param.plateLocationD},
            </if>
            <if test="param.trustD != null and param.trustD != ''">
                "trust_d" = #{param.trustD},
            </if>
            <if test="param.plateNumberA != null and param.plateNumberA != ''">
                "plate_number_a" = #{param.plateNumberA},
            </if>
            <if test="param.plateDirA != null and param.plateDirA != ''">
                "plate_dir_a" = #{param.plateDirA},
            </if>
            <if test="param.plateNumberB != null and param.plateNumberB != ''">
                "plate_number_b" = #{param.plateNumberB},
            </if>
            <if test="param.plateDirB != null and param.plateDirB != ''">
                "plate_dir_b" = #{param.plateDirB},
            </if>
            <if test="param.laneNumA != null and param.laneNumA != ''">
                "lane_num_a" = #{param.laneNumA},
            </if>
            <if test="param.laneNumB != null and param.laneNumB != ''">
                "lane_num_b" = #{param.laneNumB},
            </if>
            <if test="param.doorDirA != null and param.doorDirA != ''">
                "door_dir_a" = #{param.doorDirA},
            </if>
            <if test="param.doorDirB != null and param.doorDirB != ''">
                "door_dir_b" = #{param.doorDirB},
            </if>
            <if test="param.doorDirC != null and param.doorDirC != ''">
                "door_dir_c" = #{param.doorDirC},
            </if>
            <if test="param.doorDirD != null and param.doorDirD != ''">
                "door_dir_d" = #{param.doorDirD},
            </if>
            <if test="param.weight != null and param.weight != ''">
                "weight" = #{param.weight},
            </if>
            <if test="param.recognizeTimeConsuming != null and param.recognizeTimeConsuming != ''">
                "recognize_time_consuming" = #{param.recognizeTimeConsuming},
            </if>
            <if test="param.snapTimeConsuming != null and param.snapTimeConsuming != ''">
                "snap_time_consuming" = #{param.snapTimeConsuming},
            </if>
            <if test="param.ctnCheckA != null and param.ctnCheckA != ''">
                "ctn_check_a" = #{param.ctnCheckA},
            </if>
            <if test="param.ctnCheckB != null and param.ctnCheckB != ''">
                "ctn_check_b" = #{param.ctnCheckB},
            </if>
            <if test="param.ctnCheckC != null and param.ctnCheckC != ''">
                "ctn_check_c" = #{param.ctnCheckC},
            </if>
            <if test="param.ctnCheckD != null and param.ctnCheckD != ''">
                "ctn_check_d" = #{param.ctnCheckD},
            </if>
            <if test="param.plateCheckA != null and param.plateCheckA != ''">
                "plate_check_a" = #{param.plateCheckA},
            </if>
            <if test="param.plateCheckB != null and param.plateCheckB != ''">
                "plate_check_b" = #{param.plateCheckB},
            </if>
            "rec_time" = #{param.recTime}
        </set>
        WHERE "id" = #{param.id}
    </update>

    <update id="updateBySeqNo">
        UPDATE "T_RECORD"
        SET "unlock_time" = #{param.unlockTime},
            "lane_num_b" = #{param.laneNumB},
            "work_type" = #{param.workType}
        WHERE "seq_no" = #{param.seqNo}
    </update>
    <update id="updateTopPlate">
        UPDATE "T_RECORD"
        SET "top_plate_a" = #{topPlate}
        WHERE "seq_no" = #{seqNo}
    </update>

    <delete id="deleteBySeqNo">
        DELETE FROM "T_RECORD" WHERE "seq_no" = #{seqNo}
    </delete>

    <select id="queryTotal" resultType="java.lang.Integer">
        SELECT count(ID) FROM "T_RECORD"
    </select>

    <select id="queryCtnTotalByType" resultType="java.lang.Integer">
        SELECT COALESCE(SUM(
        case when CONTAINER_TYPE is null THEN 0
        when CONTAINER_TYPE = 0 THEN 1
        when CONTAINER_TYPE = 1 THEN 1
        when CONTAINER_TYPE = 2 THEN 2
        when CONTAINER_TYPE = 3 THEN 2
        when CONTAINER_TYPE = 4 THEN 3
        when CONTAINER_TYPE = 5 THEN 3
        when CONTAINER_TYPE = 6 THEN 4
        when CONTAINER_TYPE = 7 THEN 0 end
        ),0) FROM "T_RECORD" WHERE CONTAINER_TYPE IN
        <foreach collection="containerType" item="type" separator="," close=")" open="(">
            #{type}
        </foreach>
    </select>

    <select id="queryRecoAvgTimeConsuming" resultType="java.lang.Integer">
        SELECT AVG(COALESCE(recognize_time_consuming, 0)) FROM "T_RECORD"
    </select>

    <select id="querySnapAvgTimeConsuming" resultType="java.lang.Integer">
        SELECT AVG(COALESCE(snap_time_consuming, 0)) FROM "T_RECORD"
    </select>
    <select id="queryCtnTotal" resultType="java.lang.Integer">
        SELECT SUM(
                       case when CONTAINER_TYPE is null THEN 0
                            when CONTAINER_TYPE = 0 THEN 1
                            when CONTAINER_TYPE = 1 THEN 1
                            when CONTAINER_TYPE = 2 THEN 2
                            when CONTAINER_TYPE = 3 THEN 2
                            when CONTAINER_TYPE = 4 THEN 3
                            when CONTAINER_TYPE = 5 THEN 3
                            when CONTAINER_TYPE = 6 THEN 4
                            when CONTAINER_TYPE = 7 THEN 0 else 0 end
                   ) FROM "T_RECORD"
    </select>
    <select id="queryCtnCheckSuccess" resultType="java.lang.Integer">
        SELECT SUM(
                       case when CONTAINER_TYPE is null THEN 0
                            when CONTAINER_TYPE = 0 THEN
                                (case when ctn_check_a = true then 1 else 0 end)
                            when CONTAINER_TYPE = 1 THEN
                                (case when ctn_check_a = true then 1 else 0 end)
                            when CONTAINER_TYPE = 2 THEN
                                (case when ctn_check_a = true and ctn_check_b = true then 2
                                      when ctn_check_a = true and ctn_check_b = false then 1
                                      when ctn_check_a = false and ctn_check_b = true then 1
                                         else 0 end)
                            when CONTAINER_TYPE = 3 THEN
                                (case when ctn_check_a = true and ctn_check_b = true then 2
                                      when ctn_check_a = true and ctn_check_b = false then 1
                                      when ctn_check_a = false and ctn_check_b = true then 1
                                         else 0 end)
                            when CONTAINER_TYPE = 4 THEN
                                (case when ctn_check_a = true and ctn_check_b = true and ctn_check_c = true then 3
                                      when ctn_check_a = false and ctn_check_b = true and ctn_check_c = true then 2
                                      when ctn_check_a = true and ctn_check_b = false and ctn_check_c = true then 2
                                      when ctn_check_a = true and ctn_check_b = true and ctn_check_c = false then 2
                                      when ctn_check_a = false and ctn_check_b = false and ctn_check_c = true then 1
                                      when ctn_check_a = false and ctn_check_b = true and ctn_check_c = false then 1
                                      when ctn_check_a = true and ctn_check_b = false and ctn_check_c = false then 1
                                         else 0 end)
                            when CONTAINER_TYPE = 5 THEN
                                (case when ctn_check_a = true and ctn_check_b = true and ctn_check_c = true then 3
                                      when ctn_check_a = false and ctn_check_b = true and ctn_check_c = true then 2
                                      when ctn_check_a = true and ctn_check_b = false and ctn_check_c = true then 2
                                      when ctn_check_a = true and ctn_check_b = true and ctn_check_c = false then 2
                                      when ctn_check_a = false and ctn_check_b = false and ctn_check_c = true then 1
                                      when ctn_check_a = false and ctn_check_b = true and ctn_check_c = false then 1
                                      when ctn_check_a = true and ctn_check_b = false and ctn_check_c = false then 1
                                         else 0 end)
                            when CONTAINER_TYPE = 6 THEN
                                (case when ctn_check_a = true and ctn_check_b = true and ctn_check_c = true and ctn_check_d = true then 4
                                      when ctn_check_a = false and ctn_check_b = true and ctn_check_c = true and ctn_check_d = true then 3
                                      when ctn_check_a = true and ctn_check_b = false and ctn_check_c = true and ctn_check_d = true then 3
                                      when ctn_check_a = true and ctn_check_b = true and ctn_check_c = false and ctn_check_d = true then 3
                                      when ctn_check_a = true and ctn_check_b = true and ctn_check_c = true and ctn_check_d = false then 3
                                      when ctn_check_a = false and ctn_check_b = false and ctn_check_c = true and ctn_check_d = true then 2
                                      when ctn_check_a = false and ctn_check_b = true and ctn_check_c = false and ctn_check_d = true then 2
                                      when ctn_check_a = false and ctn_check_b = true and ctn_check_c = true and ctn_check_d = false then 2
                                      when ctn_check_a = true and ctn_check_b = false and ctn_check_c = false and ctn_check_d = true then 2
                                      when ctn_check_a = true and ctn_check_b = false and ctn_check_c = true and ctn_check_d = false then 2
                                      when ctn_check_a = true and ctn_check_b = true and ctn_check_c = false and ctn_check_d = false then 2
                                      when ctn_check_a = false and ctn_check_b = false and ctn_check_c = false and ctn_check_d = true then 1
                                      when ctn_check_a = false and ctn_check_b = false and ctn_check_c = true and ctn_check_d = false then 1
                                      when ctn_check_a = false and ctn_check_b = true and ctn_check_c = false and ctn_check_d = false then 1
                                      when ctn_check_a = true and ctn_check_b = false and ctn_check_c = false and ctn_check_d = false then 1
                                         else 0 end)
                            when CONTAINER_TYPE = 7 THEN 0 else 0 end
                   ) FROM "T_RECORD"

    </select>
    <select id="queryPlateNumberCheckSuccess" resultType="java.lang.Integer">
            SELECT
                SUM(
                        case when CONTAINER_TYPE is null THEN 0
                             when CONTAINER_TYPE = 0 THEN
                                 (case when plate_check_a = true then 1 else 0 end)
                             when CONTAINER_TYPE = 1 THEN
                                 (case when plate_check_a = true then 1 else 0 end)
                            when CONTAINER_TYPE = 2 THEN
                                (case when plate_check_a = true then 1 else 0 end)
                            when CONTAINER_TYPE = 3 THEN
                               (case when plate_check_a = true then 1 else 0 end)
                            when CONTAINER_TYPE = 4 THEN
                                (case when plate_check_a = true and plate_check_b = true then 2
                                      when plate_check_a = true and plate_check_b = false then 1
                                      when plate_check_a = false and plate_check_b = true then 1
                                         else 0 end)
                            when CONTAINER_TYPE = 5 THEN
                                (case when plate_check_a = true and plate_check_b = true then 2
                                      when plate_check_a = true and plate_check_b = false then 1
                                      when plate_check_a = false and plate_check_b = true then 1
                                         else 0 end)
                            when CONTAINER_TYPE = 6 THEN
                                 (case when plate_check_a = true and plate_check_b = true then 2
                                      when plate_check_a = true and plate_check_b = false then 1
                                      when plate_check_a = false and plate_check_b = true then 1
                                         else 0 end)
                            when CONTAINER_TYPE = 7 THEN 0 else 0 end
                   )
            FROM "T_RECORD"
    </select>

    <select id="queryPageCount" resultType="java.lang.Integer">
        SELECT COUNT(TR."id") FROM "T_RECORD" TR
        <include refid="pageSql"/>
    </select>

    <select id="queryPage" resultType="net.pingfang.model.dto.QueryRecordDTO">
        SELECT
        TR.*,case when (TR."lane_num_a" = '拖车车道' or TR."lane_num_b" = '拖车车道') then 1 else 2 end as isVehicleLane
        FROM "T_RECORD" TR
        <include refid="pageSql"/>
        ORDER BY TR."id" DESC
        LIMIT #{queryParam.pageSize} OFFSET #{queryParam.startIndex}
    </select>

    <select id="queryExportRecordList" resultType="net.pingfang.model.dto.QueryRecordDTO">
        SELECT
        TR.*
        FROM "T_RECORD" TR
        <include refid="pageSql"/>
        ORDER BY TR."id" DESC
    </select>

    <sql id="pageSql">
        WHERE 1=1
        <if test="queryParam.isVehicleLane != null  and queryParam.isVehicleLane == 1">
            AND (TR."lane_num_a" = '拖车车道' or TR."lane_num_b" = '拖车车道')
        </if>
        <if test="queryParam.isVehicleLane != null  and queryParam.isVehicleLane == 2">
            AND ((TR."lane_num_a" != '拖车车道' or TR."lane_num_a" is NULL) and (TR."lane_num_b" != '拖车车道' or TR."lane_num_b" is NULL))
        </if>
        <if test="queryParam.ctnNo != null and queryParam.ctnNo != ''">
            AND (TR.ctn_no_a LIKE '%'||#{queryParam.ctnNo}||'%' OR TR.ctn_no_b LIKE '%'||#{queryParam.ctnNo}||'%' OR TR.ctn_no_c LIKE '%'||#{queryParam.ctnNo}||'%' OR TR.ctn_no_c LIKE '%'||#{queryParam.ctnNo}||'%')
        </if>
        <if test="queryParam.plateNumber != null and queryParam.plateNumber != ''">
            AND (TR.plate_number_a = #{queryParam.plateNumber} OR TR.plate_number_b = #{queryParam.plateNumber})
        </if>
        <if test="queryParam.workTypeList != null and queryParam.workTypeList.size() > 0">
            AND TR.work_type IN
            <foreach collection="queryParam.workTypeList" item="workType" separator="," close=")" open="(">
                #{workType}
            </foreach>
        </if>
        <if test="queryParam.spreaderTypeList != null and queryParam.spreaderTypeList.size() > 0">
            AND TR.container_type IN
            <foreach collection="queryParam.spreaderTypeList" item="containerType" separator="," close=")" open="(">
                #{containerType}
            </foreach>
        </if>
        <if test="queryParam.laneList != null and queryParam.laneList.size() > 0">
            AND TR.lane_num_a IN
            <foreach collection="queryParam.laneList" item="lane" separator="," close=")" open="(">
                #{lane}
            </foreach>
            OR TR.lane_num_b IN
            <foreach collection="queryParam.laneList" item="lane" separator="," open="(" close=")">
                #{lane}
            </foreach>
        </if>
        <if test="queryParam.isDamage != null">
            AND (TR.is_damage_a = #{queryParam.isDamage} OR TR.is_damage_b = #{queryParam.isDamage} OR TR.is_damage_c = #{queryParam.isDamage} OR TR.is_damage_d = #{queryParam.isDamage})
        </if>
        <if test="queryParam.dangerous != null">
            AND (TR.dangerous_lable_a = #{queryParam.dangerous} OR TR.dangerous_lable_b = #{queryParam.dangerous} OR TR.dangerous_lable_c = #{queryParam.dangerous} OR TR.dangerous_lable_d = #{queryParam.dangerous})
        </if>
        <if test="queryParam.seal != null">
            AND (TR.seal_a = #{queryParam.seal} OR TR.seal_b = #{queryParam.seal} OR TR.seal_c = #{queryParam.seal} OR TR.seal_d = #{queryParam.seal})
        </if>
        <if test="queryParam.fromDate != null and queryParam.fromDate != '' and queryParam.toDate != null and queryParam.toDate != ''">
            AND TR.pass_time between #{queryParam.fromDate} and #{queryParam.toDate}
        </if>
    </sql>

    <select id="queryByIds" resultType="net.pingfang.model.entity.Record">
        SELECT
            TR.*
        FROM "T_RECORD" TR
        <where>
            TR.seq_no IS NOT NULL
            <if test="null != seqNo and seqNo != ''">
                AND TR."seq_no" = #{seqNo}
            </if>
            <if test="null != ids and ids.size > 0">
                AND TR."id" IN
                <foreach collection="ids" item="id" separator="," close=")" open="(">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
    <select id="queryBySeqNo" resultType="net.pingfang.model.entity.Record">
        SELECT
            TR.*
        FROM "T_RECORD" TR
        where TR.seq_no = #{seqNo}
        order by id desc limit 1
    </select>

    <select id="querySyncBySeqNo" resultType="net.pingfang.model.dto.QueryRecordSyncReqDTO">
        SELECT id,seq_no,ctn_no_a,plate_number_a,top_plate_a,ctn_num FROM "T_RECORD"
        where seq_no = #{seqNo}
        order by id desc limit 1
    </select>

</mapper>
