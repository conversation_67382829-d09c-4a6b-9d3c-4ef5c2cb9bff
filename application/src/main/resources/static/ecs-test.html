<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECS接口测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
            text-align: center;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
            min-height: 100px;
        }
        .tab {
            overflow: hidden;
            border: 1px solid #ccc;
            background-color: #f1f1f1;
            margin-bottom: 20px;
        }
        .tab button {
            background-color: inherit;
            float: left;
            border: none;
            outline: none;
            cursor: pointer;
            padding: 14px 16px;
            transition: 0.3s;
            color: black;
        }
        .tab button:hover {
            background-color: #ddd;
        }
        .tab button.active {
            background-color: #ccc;
        }
        .tabcontent {
            display: none;
            padding: 6px 12px;
            border: 1px solid #ccc;
            border-top: none;
        }
        textarea {
            width: 100%;
            height: 200px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
    </style>
</head>
<body>
    <h1>ECS接口测试</h1>
    
    <div class="tab">
        <button class="tablinks active" onclick="openTab(event, 'truck')">拖车识别测试</button>
        <button class="tablinks" onclick="openTab(event, 'container')">集装箱识别测试</button>
        <button class="tablinks" onclick="openTab(event, 'carriage')">火车车厢识别测试</button>
        <button class="tablinks" onclick="openTab(event, 'full')">完整数据测试</button>
    </div>
    
    <div id="truck" class="tabcontent" style="display: block;">
        <div class="test-section">
            <h2>拖车识别测试 (Type 1)</h2>
            <div class="form-group">
                <label for="truckNo">拖车编号:</label>
                <input type="text" id="truckNo" value="粤B12345">
            </div>
            <button onclick="testTruckIdentify()">测试</button>
            <div class="result" id="truckResult">
                <p>结果将显示在这里...</p>
            </div>
        </div>
    </div>
    
    <div id="container" class="tabcontent">
        <div class="test-section">
            <h2>集装箱识别测试 (Type 2)</h2>
            <div class="form-group">
                <label for="ctnNo">集装箱号:</label>
                <input type="text" id="ctnNo" value="ABCD1234567">
            </div>
            <div class="form-group">
                <label for="iso">ISO代码:</label>
                <input type="text" id="iso" value="22G1">
            </div>
            <div class="form-group">
                <label for="containerType">集装箱类型:</label>
                <input type="text" id="containerType" value="40GP">
            </div>
            <button onclick="testContainerIdentify()">测试</button>
            <div class="result" id="containerResult">
                <p>结果将显示在这里...</p>
            </div>
        </div>
    </div>
    
    <div id="carriage" class="tabcontent">
        <div class="test-section">
            <h2>火车车厢识别测试 (Type 3)</h2>
            <div class="form-group">
                <label for="carriageNo">火车车厢编号:</label>
                <input type="text" id="carriageNo" value="X12345678">
            </div>
            <button onclick="testCarriageIdentify()">测试</button>
            <div class="result" id="carriageResult">
                <p>结果将显示在这里...</p>
            </div>
        </div>
    </div>
    
    <div id="full" class="tabcontent">
        <div class="test-section">
            <h2>完整数据测试</h2>
            <div class="form-group">
                <label for="fullData">完整JSON数据:</label>
                <textarea id="fullData">{
  "craneNo": "TRMG01",
  "identifyType": 2,
  "identifyCtnNo": "ABCD1234567",
  "identifyCtnNolso": "22G1",
  "identifyStuffingStatus": "1",
  "identifyContainerType": "40GP",
  "identifyContainerClass": "GP",
  "identifyContainerWeight": 25000
}</textarea>
            </div>
            <button onclick="testFullIdentify()">测试</button>
            <div class="result" id="fullResult">
                <p>结果将显示在这里...</p>
            </div>
        </div>
    </div>
    
    <script>
        function openTab(evt, tabName) {
            var i, tabcontent, tablinks;
            tabcontent = document.getElementsByClassName("tabcontent");
            for (i = 0; i < tabcontent.length; i++) {
                tabcontent[i].style.display = "none";
            }
            tablinks = document.getElementsByClassName("tablinks");
            for (i = 0; i < tablinks.length; i++) {
                tablinks[i].className = tablinks[i].className.replace(" active", "");
            }
            document.getElementById(tabName).style.display = "block";
            evt.currentTarget.className += " active";
        }
        
        function testTruckIdentify() {
            const truckNo = document.getElementById('truckNo').value;
            const resultElement = document.getElementById('truckResult');
            
            resultElement.innerHTML = '<p>正在发送请求...</p>';
            
            fetch(`/ecs/test/truck?truckNo=${encodeURIComponent(truckNo)}`)
                .then(response => response.json())
                .then(data => {
                    resultElement.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                })
                .catch(error => {
                    resultElement.innerHTML = `<p style="color: red;">请求失败: ${error.message}</p>`;
                });
        }
        
        function testContainerIdentify() {
            const ctnNo = document.getElementById('ctnNo').value;
            const iso = document.getElementById('iso').value;
            const containerType = document.getElementById('containerType').value;
            const resultElement = document.getElementById('containerResult');
            
            resultElement.innerHTML = '<p>正在发送请求...</p>';
            
            fetch(`/ecs/test/container?ctnNo=${encodeURIComponent(ctnNo)}&iso=${encodeURIComponent(iso)}&containerType=${encodeURIComponent(containerType)}`)
                .then(response => response.json())
                .then(data => {
                    resultElement.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                })
                .catch(error => {
                    resultElement.innerHTML = `<p style="color: red;">请求失败: ${error.message}</p>`;
                });
        }
        
        function testCarriageIdentify() {
            const carriageNo = document.getElementById('carriageNo').value;
            const resultElement = document.getElementById('carriageResult');
            
            resultElement.innerHTML = '<p>正在发送请求...</p>';
            
            fetch(`/ecs/test/carriage?carriageNo=${encodeURIComponent(carriageNo)}`)
                .then(response => response.json())
                .then(data => {
                    resultElement.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                })
                .catch(error => {
                    resultElement.innerHTML = `<p style="color: red;">请求失败: ${error.message}</p>`;
                });
        }
        
        function testFullIdentify() {
            const fullData = document.getElementById('fullData').value;
            const resultElement = document.getElementById('fullResult');
            
            resultElement.innerHTML = '<p>正在发送请求...</p>';
            
            fetch('/ecs/test/full', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: fullData
            })
                .then(response => response.json())
                .then(data => {
                    resultElement.innerHTML = `<pre>${JSON.stringify(data, null, 2)}</pre>`;
                })
                .catch(error => {
                    resultElement.innerHTML = `<p style="color: red;">请求失败: ${error.message}</p>`;
                });
        }
    </script>
</body>
</html>
