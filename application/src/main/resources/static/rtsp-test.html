<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RTSP预识别测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
        .btn:hover {
            background: #2980b9;
        }
        .btn-success {
            background: #27ae60;
        }
        .btn-success:hover {
            background: #229954;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .list-item {
            border: 1px solid #ddd;
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            background: #fafafa;
        }
        .list-item:hover {
            background: #f0f0f0;
            cursor: pointer;
        }
        .status-success {
            color: #27ae60;
            font-weight: bold;
        }
        .status-failed {
            color: #e74c3c;
            font-weight: bold;
        }
        .status-processing {
            color: #f39c12;
            font-weight: bold;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .image-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            background: white;
        }
        .image-item img {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 3px;
        }
        .image-info {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎥 RTSP预识别测试页面</h1>
            <p>测试RTSP预识别功能的数据存储和查询</p>
        </div>

        <!-- 列表查询测试 -->
        <div class="section">
            <h3>📋 1. 列表查询测试</h3>
            <button class="btn" onclick="loadList()">加载预识别列表</button>
            <button class="btn btn-success" onclick="loadList(1, 5)">分页查询(第1页,5条)</button>
            <div id="listResult" class="result" style="display:none;"></div>
            <div id="listDisplay"></div>
        </div>

        <!-- 详情查询测试 -->
        <div class="section">
            <h3>🔍 2. 详情查询测试</h3>
            <input type="number" id="detailId" placeholder="输入记录ID" value="1">
            <button class="btn" onclick="loadDetail()">查询详情</button>
            <div id="detailResult" class="result" style="display:none;"></div>
            <div id="detailDisplay"></div>
        </div>

        <!-- 图片展示测试 -->
        <div class="section">
            <h3>🖼️ 3. 图片展示测试</h3>
            <div id="imageDisplay"></div>
        </div>
    </div>

    <script>
        const API_BASE = '/api/rtsp-pre-recognition';

        // 加载列表
        async function loadList(page = 1, size = 10) {
            try {
                const response = await fetch(`${API_BASE}/list?page=${page}&size=${size}&sortBy=createTime&sortDir=desc`);
                const data = await response.json();
                
                document.getElementById('listResult').style.display = 'block';
                document.getElementById('listResult').textContent = JSON.stringify(data, null, 2);
                
                if (data.code === 200 && data.data.records) {
                    displayList(data.data.records);
                } else {
                    document.getElementById('listDisplay').innerHTML = '<p style="color: red;">查询失败: ' + data.message + '</p>';
                }
            } catch (error) {
                console.error('加载列表失败:', error);
                document.getElementById('listDisplay').innerHTML = '<p style="color: red;">请求失败: ' + error.message + '</p>';
            }
        }

        // 显示列表
        function displayList(records) {
            const container = document.getElementById('listDisplay');
            container.innerHTML = '<h4>📊 预识别记录列表</h4>';
            
            records.forEach(record => {
                const statusClass = record.status === 'SUCCESS' ? 'status-success' : 
                                  record.status === 'FAILED' ? 'status-failed' : 'status-processing';
                
                const item = document.createElement('div');
                item.className = 'list-item';
                item.onclick = () => loadDetailById(record.id);
                item.innerHTML = `
                    <div><strong>ID:</strong> ${record.id} | <strong>序列号:</strong> ${record.seqNo}</div>
                    <div><strong>吊机号:</strong> ${record.craneNo} | <strong>状态:</strong> <span class="${statusClass}">${record.status}</span></div>
                    <div><strong>箱号:</strong> ${record.containerNumber || 'N/A'} | <strong>置信度:</strong> ${record.confidence || 'N/A'}</div>
                    <div><strong>图片数量:</strong> ${record.imageCount} | <strong>创建时间:</strong> ${record.createTime}</div>
                    <div style="margin-top: 5px; font-size: 12px; color: #666;">点击查看详情</div>
                `;
                container.appendChild(item);
            });
        }

        // 加载详情
        async function loadDetail() {
            const id = document.getElementById('detailId').value;
            if (!id) {
                alert('请输入记录ID');
                return;
            }
            await loadDetailById(id);
        }

        // 根据ID加载详情
        async function loadDetailById(id) {
            try {
                const response = await fetch(`${API_BASE}/${id}/images`);
                const data = await response.json();
                
                document.getElementById('detailResult').style.display = 'block';
                document.getElementById('detailResult').textContent = JSON.stringify(data, null, 2);
                
                if (data.code === 200 && data.data) {
                    displayDetail(data.data);
                } else {
                    document.getElementById('detailDisplay').innerHTML = '<p style="color: red;">查询失败: ' + data.message + '</p>';
                }
            } catch (error) {
                console.error('加载详情失败:', error);
                document.getElementById('detailDisplay').innerHTML = '<p style="color: red;">请求失败: ' + error.message + '</p>';
            }
        }

        // 显示详情
        function displayDetail(detail) {
            const container = document.getElementById('detailDisplay');
            const statusClass = detail.status === 'SUCCESS' ? 'status-success' : 
                              detail.status === 'FAILED' ? 'status-failed' : 'status-processing';
            
            container.innerHTML = `
                <h4>📝 预识别详情 (ID: ${detail.id})</h4>
                <div><strong>序列号:</strong> ${detail.seqNo}</div>
                <div><strong>吊机号:</strong> ${detail.craneNo}</div>
                <div><strong>状态:</strong> <span class="${statusClass}">${detail.status}</span></div>
                <div><strong>识别箱号:</strong> ${detail.containerNumber || 'N/A'}</div>
                <div><strong>置信度:</strong> ${detail.confidence || 'N/A'}</div>
                <div><strong>图片数量:</strong> ${detail.imageCount}</div>
                <div><strong>开始时间:</strong> ${detail.startTime}</div>
                <div><strong>结束时间:</strong> ${detail.endTime}</div>
                <div><strong>耗时:</strong> ${detail.duration}ms</div>
                ${detail.errorMessage ? `<div><strong>错误信息:</strong> <span style="color: red;">${detail.errorMessage}</span></div>` : ''}
            `;
            
            // 显示图片信息
            if (detail.allCameraImages) {
                try {
                    const imageData = JSON.parse(detail.allCameraImages);
                    displayImages(imageData);
                } catch (error) {
                    console.error('解析图片信息失败:', error);
                    document.getElementById('imageDisplay').innerHTML = '<p style="color: red;">图片信息解析失败</p>';
                }
            } else {
                document.getElementById('imageDisplay').innerHTML = '<p>暂无图片信息</p>';
            }
        }

        // 显示图片
        function displayImages(imageData) {
            const container = document.getElementById('imageDisplay');
            container.innerHTML = '<h4>🖼️ 相机图片信息</h4>';
            
            if (imageData.cameras && imageData.cameras.length > 0) {
                const grid = document.createElement('div');
                grid.className = 'image-grid';
                
                imageData.cameras.forEach(camera => {
                    const item = document.createElement('div');
                    item.className = 'image-item';
                    
                    const confidenceColor = camera.confidence > 0.8 ? '#27ae60' : 
                                          camera.confidence > 0.5 ? '#f39c12' : '#e74c3c';
                    
                    item.innerHTML = `
                        <img src="${camera.imageUrl}" alt="${camera.imageName}" 
                             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWbvueJh+aXoOazleWKoOi9vTwvdGV4dD48L3N2Zz4='">
                        <div class="image-info">
                            <div><strong>${camera.cameraName}</strong> (ID: ${camera.cameraId})</div>
                            <div>文件: ${camera.imageName}</div>
                            <div>箱号: ${camera.containerNo || 'N/A'}</div>
                            <div>置信度: <span style="color: ${confidenceColor}; font-weight: bold;">${camera.confidence}</span></div>
                            <div>校验: ${camera.checkPassed ? '✅ 通过' : '❌ 失败'}</div>
                            <div>抓拍时间: ${camera.captureTime}</div>
                        </div>
                    `;
                    grid.appendChild(item);
                });
                
                container.appendChild(grid);
                
                // 显示汇总信息
                const summary = document.createElement('div');
                summary.style.marginTop = '20px';
                summary.style.padding = '15px';
                summary.style.background = '#f8f9fa';
                summary.style.borderRadius = '5px';
                summary.innerHTML = `
                    <h5>📊 汇总信息</h5>
                    <div>总图片数: ${imageData.totalCount}</div>
                    <div>平均置信度: ${imageData.averageConfidence?.toFixed(4) || 'N/A'}</div>
                    <div>最佳相机: ${imageData.bestCameraId || 'N/A'}</div>
                `;
                container.appendChild(summary);
            } else {
                container.innerHTML += '<p>暂无相机图片</p>';
            }
        }

        // 页面加载时自动加载列表
        window.onload = function() {
            loadList();
        };
    </script>
</body>
</html>
