<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.pingfang.mapper.LaneMapper">
    <insert id="add">
        INSERT INTO "T_LANE"
            ("id",
             "lane",
             "seq",
             "min_altitude",
             "rect_x_from",
             "rect_x_to",
             "create_by",
             "create_time",
             "update_by",
             "update_time")
        VALUES (
                #{param.id},
                #{param.lane},
                #{param.seq},
                #{param.minAltitude},
                #{param.rectXFrom},
                #{param.rectXTo},
                #{param.createBy},
                NOW(),
                #{param.createBy},
                NOW()
               )
    </insert>
    <update id="update">
        UPDATE "T_LANE"
        SET
            "lane" = #{param.lane},
            "seq" = #{param.seq},
            "min_altitude" = #{param.minAltitude},
            "rect_x_from" = #{param.rectXFrom},
            "rect_x_to" = #{param.rectXTo},
            "update_by" = #{param.updateBy},
            "update_time" = NOW()
        WHERE id = #{param.id}

    </update>
    <delete id="delete">
        DELETE FROM "T_LANE" WHERE ID = #{id}
    </delete>

    <select id="queryAll" resultType="net.pingfang.model.entity.Lane">
        SELECT * FROM "T_LANE" ORDER BY SEQ ASC
    </select>

    <select id="queryByPlcX" resultType="net.pingfang.model.entity.Lane">
        SELECT * FROM "T_LANE" WHERE "rect_x_from" <![CDATA[ <= ]]> #{plcX} AND "rect_x_to" >= #{plcX} LIMIT 1
    </select>

    <select id="queryById" resultType="net.pingfang.model.entity.Lane">
        SELECT * FROM "T_LANE" WHERE id = #{id}
    </select>


</mapper>
