<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.pingfang.mapper.SystemConfigMapper">
    <update id="updateList">
        <foreach collection="dataList" separator=";" item="param">
            UPDATE "T_SYSTEM_CONFIG"
            SET "name" = #{param.name},
                "value" = #{param.value},
                "update_by" = #{param.updateBy},
                "update_time" = NOW(),
                "code" =#{param.code}
            WHERE "id" = #{param.id}
        </foreach>
    </update>

    <select id="queryAll" resultType="net.pingfang.model.entity.SystemConfig">
        SELECT * FROM "T_SYSTEM_CONFIG"
    </select>
</mapper>