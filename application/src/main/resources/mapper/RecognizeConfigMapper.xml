<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.pingfang.mapper.RecognizeConfigMapper">

    <insert id="addBatch">
        INSERT INTO "T_RECOGNIZE_CONFIG"
         ("id",
          "lane_id",
          "camera_id",
          "preset_location",
          "type",
          "roi_rect",
          "video_recording")
        VALUES
        <foreach collection="dataList" item="param" separator=",">
            (
            #{param.id},
            #{param.laneId},
            #{param.cameraId},
            #{param.presetLocation},
            #{param.type},
            #{param.roiRect},
            #{param.videoRecording}
            )
        </foreach>
    </insert>

    <update id="updatePreset">
        UPDATE "T_RECOGNIZE_CONFIG"
        SET preset_location = (SELECT MAX(COALESCE(preset_location,0))+1 FROM "T_RECOGNIZE_CONFIG" WHERE  camera_id = #{cameraId} )
        WHERE lane_id = #{laneId}
        AND camera_id = #{cameraId}
        AND TYPE = #{type}
    </update>

    <delete id="deleteExId">
        DELETE FROM "T_RECOGNIZE_CONFIG" WHERE ID NOT IN
        <foreach collection="idList" item="id" separator="," close=")" open="(">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteById">
        DELETE FROM "T_RECOGNIZE_CONFIG" WHERE "id" = #{id}
    </delete>

    <select id="queryByLaneId" resultType="net.pingfang.model.dto.QueryRecognizeConfigResDTO">
        SELECT
            TRC."id",
            TRC."lane_id",
            TRC."camera_id",
            TRC."preset_location",
            TRC."type",
            TRC."roi_rect",
            TRC."video_recording",
            TC."name",
            TC."ip"
        FROM
            "T_RECOGNIZE_CONFIG" TRC LEFT JOIN "T_CAMERA" TC ON TC."id"=TRC."camera_id"
        WHERE
            TRC.LANE_ID = #{laneId}
    </select>

    <select id="queryPresetConfig" resultType="net.pingfang.model.entity.RecognizeConfig">
        SELECT * FROM "T_RECOGNIZE_CONFIG" WHERE LANE_ID = #{laneId} AND CAMERA_ID = #{cameraId}
    </select>

    <select id="queryWorkConfigByType" resultType="net.pingfang.model.vo.business.RecognizeConfigVO">
        SELECT
            TRC.*,
            TC.name,
            TC.code,
            TC.ip,
            TC.port,
            TC.brand,
            TC.channel,
            TC.username,
            TC.password,
            TC.control_port
        FROM "T_RECOGNIZE_CONFIG" TRC
                 INNER JOIN "T_CAMERA" TC ON TC.ID = TRC.CAMERA_ID
        WHERE 1=1
            <if test="laneId != null">
                AND TRC.LANE_ID = #{laneId}
            </if>
          and TRC.type = #{type}
    </select>
    <select id="queryRecordBySeqNo" resultType="net.pingfang.model.entity.Record">
        SELECT
               id,
               seq_no,
               work_type,
               container_type,
               ctn_num,
               ctn_no_a,
               ctn_no_b
        FROM
            "T_RECORD"
        WHERE
            seq_no = #{seqNo}
        ORDER BY
            ID DESC
    </select>
    <select id="queryConfig" resultType="net.pingfang.model.entity.RecognizeConfig">
        SELECT * FROM "T_RECOGNIZE_CONFIG" WHERE lane_id = #{laneId}  AND camera_id = #{cameraId} AND TYPE = #{type} LIMIT 1
    </select>
    <select id="queryAll" resultType="net.pingfang.model.vo.business.RecognizeConfigVO">
        SELECT
            TRC.*,
            TC.name,
            TC.code,
            TC.ip,
            TC.port,
            TC.brand,
            TC.channel,
            TC.username,
            TC.password,
            TC.control_port
        FROM "T_RECOGNIZE_CONFIG" TRC
                 INNER JOIN "T_CAMERA" TC ON TC.ID = TRC.CAMERA_ID
    </select>

    <insert id="add">
        INSERT INTO "T_RECOGNIZE_CONFIG"
        ("id",
        "lane_id",
        "camera_id",
        "preset_location",
        "type",
        "roi_rect",
        "video_recording")
        VALUES
        (
            #{param.id},
            #{param.laneId},
            #{param.cameraId},
            #{param.presetLocation},
            #{param.type},
            #{param.roiRect},
            #{param.videoRecording}
        )
    </insert>

    <update id="updateById">
        UPDATE "T_RECOGNIZE_CONFIG"
        <set>
            <if test="param.presetLocation != null">
                "preset_location" = #{param.presetLocation},
            </if>
            <if test="param.type != null">
                "type" = #{param.type},
            </if>
            <if test="param.roiRect != null and param.roiRect != ''">
                "roi_rect" = #{param.roiRect},
            </if>
            <if test="param.videoRecording != null">
                "video_recording" = #{param.videoRecording},
            </if>
            "lane_id" = #{param.laneId},
            "camera_id" = #{param.cameraId}
        </set>
        WHERE
            "id" = #{param.id};
    </update>

</mapper>
