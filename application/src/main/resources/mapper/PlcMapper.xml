<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.pingfang.mapper.PlcMapper">


    <select id="queryDetail" resultType="net.pingfang.model.dto.QueryPlcResDTO">
        SELECT
            "id",
            "brand",
            "model",
            "protocol",
            "ip",
            "port",
            "com",
            "inter_protocol"
        FROM
            "T_PLC"
        LIMIT 1
    </select>

    <update id="updateById">
        UPDATE "public"."T_PLC"
        <set>
            <if test="param.brand != null and param.brand != ''">
                "brand" = #{param.brand},
            </if>
            <if test="param.model != null and param.model != ''">
                "model" = #{param.model},
            </if>
            <if test="param.protocol != null and param.protocol != ''">
                "protocol" = #{param.protocol},
            </if>
            <if test="param.ip != null and param.ip != ''">
                "ip" = #{param.ip},
            </if>
            <if test="param.port != null">
                "port" = #{param.port},
            </if>
            <if test="param.com != null and param.com != ''">
                "com" = #{param.com},
            </if>
            <if test="param.interProtocol != null and param.interProtocol != ''">
                "inter_protocol" = #{param.interProtocol},
            </if>
            "update_by" = #{param.updateBy},
            "update_time" = CURRENT_TIMESTAMP
        </set>
        WHERE
            "id" = #{param.id}
    </update>

</mapper>
