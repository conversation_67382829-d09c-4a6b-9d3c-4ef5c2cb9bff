<?xml version="1.0" encoding="UTF-8" ?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.pingfang.mapper.ImgMapper">

    <insert id="insertBatch">
        INSERT INTO "public"."T_IMG"("id", "seq_no", "img_type", "img_url", "camera_name","camera_code", "create_time", "dect_rect") VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.id} ,#{item.seqNo}, #{item.imgType}, #{item.imgUrl}, #{item.cameraName}, #{item.cameraCode}, NOW(), #{item.dectRect})
        </foreach>
    </insert>
    <insert id="insert">
        INSERT INTO "public"."T_IMG"("id", "seq_no", "img_type", "img_url", "camera_name","camera_code", "create_time", "dect_rect") VALUES
            (#{item.id} ,#{item.seqNo}, #{item.imgType}, #{item.imgUrl}, #{item.cameraName}, #{item.cameraCode}, NOW(), #{item.dectRect})
    </insert>

    <select id="queryByRecordIds" resultType="net.pingfang.model.entity.Img">
        SELECT
            TI.*
        FROM"T_RECORD" TR
                LEFT JOIN "T_IMG" TI ON TR.seq_no=TI.seq_no
        WHERE TI.img_url IS NOT NULL AND TR."id" IN
        <foreach collection="list" item="id" separator="," close=")" open="(">
            #{id}
        </foreach>
    </select>
    <select id="queryBySeqNo" resultType="net.pingfang.model.entity.Img">
        SELECT * FROM "T_IMG" WHERE img_url is not null and SEQ_NO = #{seqNo}
    </select>
    <select id="queryBySeqNoAndType" resultType="net.pingfang.model.entity.Img">
        SELECT * FROM "T_IMG" WHERE img_url is not null and SEQ_NO = #{seqNo} and img_type = #{type}
    </select>
    <select id="queryUrlByCameraKeyWord" resultType="java.lang.String">
        SELECT img_url FROM "T_IMG" where camera_name like concat('%',#{cameraKeyWord}::text,'%')
    </select>

    <delete id="deleteById">
        DELETE FROM "T_IMG" WHERE id = #{id}
    </delete>
</mapper>
