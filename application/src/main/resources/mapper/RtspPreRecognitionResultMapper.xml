<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.pingfang.mapper.RtspPreRecognitionResultMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="net.pingfang.model.entity.RtspPreRecognitionResult">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="seq_no" property="seqNo" jdbcType="VARCHAR"/>
        <result column="crane_no" property="craneNo" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="container_number" property="containerNumber" jdbcType="VARCHAR"/>
        <result column="confidence" property="confidence" jdbcType="FLOAT"/>
        <result column="image_count" property="imageCount" jdbcType="INTEGER"/>
        <result column="task_status" property="taskStatus" jdbcType="INTEGER"/>
        <result column="work_type" property="workType" jdbcType="INTEGER"/>
        <result column="container_height" property="containerHeight" jdbcType="FLOAT"/>
        <result column="position_difference" property="positionDifference" jdbcType="FLOAT"/>
        <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
        <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
        <result column="duration" property="duration" jdbcType="BIGINT"/>
        <result column="error_message" property="errorMessage" jdbcType="VARCHAR"/>
        <result column="all_camera_images" property="allCameraImages" jdbcType="VARCHAR"/>
        <result column="best_camera_id" property="bestCameraId" jdbcType="INTEGER"/>
        <result column="best_image_path" property="bestImagePath" jdbcType="VARCHAR"/>
        <result column="best_image_url" property="bestImageUrl" jdbcType="VARCHAR"/>
        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, seq_no, crane_no, status, container_number, confidence, image_count,
        task_status, work_type, container_height, position_difference,
        start_time, end_time, duration, error_message,
        all_camera_images, best_camera_id, best_image_path, best_image_url,
        create_by, create_time, update_by, update_time
    </sql>

    <!-- 插入 -->
    <insert id="insert" parameterType="net.pingfang.model.entity.RtspPreRecognitionResult">
        INSERT INTO t_rtsp_pre_recognition_result
        (
            seq_no,
            crane_no,
            status,
            container_number,
            confidence,
            image_count,
            task_status,
            work_type,
            container_height,
            position_difference,
            start_time,
            end_time,
            duration,
            error_message,
            all_camera_images,
            best_camera_id,
            best_image_path,
            best_image_url,
            create_by,
            create_time,
            update_by,
            update_time
        )
        VALUES
        (
            #{result.seqNo},
            #{result.craneNo},
            #{result.status},
            #{result.containerNumber},
            #{result.confidence},
            #{result.imageCount},
            #{result.taskStatus},
            #{result.workType},
            #{result.containerHeight},
            #{result.positionDifference},
            #{result.startTime},
            #{result.endTime},
            #{result.duration},
            #{result.errorMessage},
            #{result.allCameraImages},
            #{result.bestCameraId},
            #{result.bestImagePath},
            #{result.bestImageUrl},
            #{result.createBy},
            #{result.createTime},
            #{result.updateBy},
            #{result.updateTime}
        )
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_rtsp_pre_recognition_result
        WHERE id = #{id}
    </select>

    <!-- 查询所有 -->
    <select id="selectAll" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_rtsp_pre_recognition_result
        ORDER BY create_time DESC
    </select>

    <!-- 根据序列号查询 -->
    <select id="selectBySeqNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_rtsp_pre_recognition_result
        WHERE seq_no = #{seqNo}
        ORDER BY create_time DESC
    </select>

    <!-- 根据吊机号查询 -->
    <select id="selectByCraneNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_rtsp_pre_recognition_result
        WHERE crane_no = #{craneNo}
        ORDER BY create_time DESC
    </select>

    <!-- 根据状态查询 -->
    <select id="selectByStatus" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_rtsp_pre_recognition_result
        WHERE status = #{status}
        ORDER BY create_time DESC
    </select>

</mapper>
