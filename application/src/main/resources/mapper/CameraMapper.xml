<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="net.pingfang.mapper.CameraMapper">

    <insert id="add">
        INSERT INTO "T_CAMERA"
            ("id",
             "name",
             "code",
             "ip",
             "port",
             "username",
             "password",
             "create_by",
             "create_time",
             "update_by",
             "update_time",
             "brand",
             "model",
             "sequence",
             "state",
             "control_port")
        VALUES (
                #{param.id},
                #{param.name},
                #{param.code},
                #{param.ip},
                #{param.port},
                #{param.username},
                #{param.password},
                #{param.createBy},
                NOW(),
                #{param.createBy},
                NOW(),
                #{param.brand},
                #{param.model},
                #{param.sequence},
                #{param.state},
                #{param.controlPort}
               )

    </insert>

    <update id="update">
        UPDATE "T_CAMERA"
        SET name = #{param.name},
            code = #{param.code},
            ip = #{param.ip},
            port = #{param.port},
            username = #{param.username},
            password = #{param.password},
            update_by = #{param.updateBy},
            update_time = NOW(),
            brand = #{param.brand},
            model = #{param.model},
            sequence = #{param.sequence},
            state = #{param.state},
            control_port = #{param.controlPort}
        WHERE ID = #{param.id}
    </update>
    <delete id="delete">
        DELETE FROM "T_CAMERA" WHERE ID = #{id}
    </delete>

    <select id="queryAll" resultType="net.pingfang.model.entity.Camera">
        SELECT * FROM "T_CAMERA"
    </select>

    <select id="queryById" resultType="net.pingfang.model.entity.Camera">
        SELECT * FROM "T_CAMERA" WHERE ID = #{id}
    </select>

    <select id="queryByIds" resultType="net.pingfang.model.entity.Camera">
        SELECT * FROM "T_CAMERA" WHERE name is not null and id IN
        <foreach collection="ids" item="id" separator="," close=")" open="(">
            #{id}
        </foreach>
    </select>
    <select id="queryByType" resultType="net.pingfang.model.entity.Camera">
        SELECT * FROM "T_CAMERA" WHERE type = #{type}
    </select>

</mapper>
