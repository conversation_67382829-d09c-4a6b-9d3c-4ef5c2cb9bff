#server port
server.port=9703

#service name
spring.application.name=PF_SMART_VOICE

project.version=@project.version@

#log setting
logging.config=classpath:logback.xml
#logging.level.root=info
#logging.pattern.file=%d{yyyy/MM/dd HH:mm:ss.SSS} %clr(%p)[TraceId:%3X{traceId}] [%logger][%M][%L] : %m%n
#logging.pattern.console=%d{yyyy/MM/dd HH:mm:ss.SSS} %clr(%p)[TraceId:%3X{traceId}] [%logger][%M][%L] : %m%n
logging.file.path=D:/PFKJ/project/application/Log
#logging.file.max-history=7
#logging.file.max-size=20MB
#logging.file.name=D:/log/rmg/sys.log
#logging.pattern.rolling-file-name=D:/log/rmg/hello.%d{yyyy-MM-dd_HH-mm-ss}.%i.log.gz

#spring.datasource.master.driver-class-name=org.postgresql.Driver
#spring.datasource.master.jdbc-url=***********************************************************************
#spring.datasource.master.username=test
#spring.datasource.master.password=123456

#fcg_lmd
spring.datasource.master.driver-class-name=org.postgresql.Driver
spring.datasource.master.jdbc-url=jdbc:postgresql://*************:10001/rmg_mzl?stringtype=unspecified
spring.datasource.master.username=postgres
spring.datasource.master.password=pfkj2016
#mybatis setting
mybatis.mapper-locations=classpath:/mapper/*Mapper.xml
mybatis.configuration.map-underscore-to-camel-case=true

# redis server
spring.redis.host=127.0.0.1
spring.redis.port=8329
spring.redis.password=pfkj2016
spring.redis.database=0


#upload file max size
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# çè´§å¹³å°æå¡
pingfang.url.tallyPath=http://************:9643

# æ¬å°å¾çå°å
pingfang.picture.localPath=D:\\PFKJ\\Img\\
# åå·ç¼å·
pingfang.picture.rmgName=TRMG01
# IISå°åï¼ç¨äºåé¨æä»¶æå¡ï¼
pingfang.picture.iisUrl=http://*************:18882/
# ç¼©ç¥å¾å¤§å°
pingfang.picture.size=100

# å¾çè®¿é®æå¡å¨éç½®ï¼ç¨äºçæåç«¯è®¿é®URLï¼
# å¾çæå¡å¨åºç¡URLï¼å¯éç½®ä¸ºä¸åçæå¡å¨å°åï¼
pingfang.picture.frontend.baseUrl=http://*************:18882
# å¾çURLè·¯å¾åç¼
pingfang.picture.frontend.urlPrefix=img

# è½¦çè¯å«ç¸å³éç½®
# æ¯å¦å¯ç¨è½¦çè¯å«åè½
pingfang.license-plate.enabled=true
# è½¦çè¯å«æåå¤´IPå°åæ°ç»
pingfang.license-plate.camera-ips=*************
# èªå¨éè¿é´éæ¶é´ï¼æ¯«ç§ï¼
pingfang.license-plate.reconnect-interval=5000
# è¿æ¥è¶æ¶æ¶é´ï¼ç§ï¼
pingfang.license-plate.connection-timeout=5
# è¿æ¥ç«¯å£
pingfang.license-plate.port=30000
# å¾çä¿å­è·¯å¾
pingfang.license-plate.image-save-path=D:/license_plate_images/
# æ¯å¦å¯ç¨è½¦çå å¯
pingfang.license-plate.encryption-enabled=false
# è½¦çè¯å«å å¯å¯ç ï¼å½å¯ç¨å å¯æ¶ä½¿ç¨ï¼
pingfang.license-plate.encryption-password=admin

# ECSç³»ç»éç½®
ecs.api.url=http://*************:8882
ecs.api.identify.path=/ocrCrane/receiveIdentifyInfo
# é¨åå·éç½®
pingfang.license-plate.crane-no=TRMG01

# è½¦é¡¶å·è¯å«éç½®
truck-top-recognition.timeout=30
truck-top-recognition.interval=2

# ç®æ³å¨æåºæä»¶å°å
#dll.hk=D:\hikvision-doc\CH-HCNetSDKV6.1.9.48_build20230410_win64\???\HCNetSDK.dll
#dll.ocrContainer=D:\\SF_ENVIRONMENT\\AILib_container.dll

# æµ·åº·å¨æåºæä»¶å°å
dll.hk=D:\\PFKJ\\application\\lib\\HCNetSDK.dll

# ç®æ³å¨æåºæä»¶å°å
# ç®±å·è¯å«
dll.ocrContainer=D:\\PFKJ\\ai\\algorithm\\AILib_container.dll
# è½¦é¡¶å·è¯å«
dll.topPlate=D:\\PFKJ\\ai\\algorithm\\AILib_vehicle.dll
# è½¦çè¯å«
dll.plateThree=D:\\PFKJ\\ai\\algorithm\\PFLPRDevice_x64.dll


# é»è®¤æä½è¯å«é«åº¦
identity.height=21000
# ä½ä¸åççº¿ï¼ä¸è¬æ¯æµ·éä¾§ï¼æèä½ä¸è½¦éåå åºï¼xè½´åæ 
identity.line=53000
# ç®±å·è¯å«åºåä½ç½®ååéï¼ç¨äºå¤æ­è¯å«åºåä½ç½®æ¯å¦åå
backBox.locationChange=10

# éç½®éå¡è½¦éå·
truck.work.lane.no=è½¦é


plc.initPlcFlag=true
plc.initPlcXMultiple=0
plc.initPlcYMultiple=0

# åæºæ¯å¦å­å¨Yå¼ï¼å°è½¦ä¸åé«åº¦æ°æ®ï¼
plc.ifExistY=false
# ifExistYä¸ºfalseæ¶çæï¼ä¸éæµç¨ålockAndStartDetectTimeç§å¼å¯è¯å«æµç¨
plc.lockAndStartDetectTime=3

# æ¯å¦è¿è¡ç®æ³å è½½
algorithm.flag=false
# æ¯å¦ä½¿ç¨å¨æ¯å¾çè¿è¡è½¦é¡¶å·è¯å«
algorithm.ifCheckTopPlateByPanorama=false
# æ¯å¦ä»è¯å«å¼å³éå¤äºæè½¦è½¦éæ¶çè½¦é¡¶å·
algorithm.ifCheckTopPlateOnlyVehicleLane=true

# GPSæ°æ®æ¯å¦æ¥æ¶
gps.receive.open=false
# GPSä¸­è½¬æå¡å¨IP
gps.transfer.ip=*************
# GPSä¸­è½¬æå¡å¨ç«¯å£
gps.transfer.port=12345
# GPSè®¾å¤ç¼å·ï¼12ä½
gps.imei=293059288966
# GPSæå¡æ¥å£å°å http://*************:13000/gps/queryGps   http://************:13000/gps/queryGps
gps.url=http://*************:12345/gps/queryGps

# --------------------------------- ç¬¬ä¸æ¹æ¥å£ç¸å³éç½® -------------------------------------

# ECSæ¥å£å°å
# ECSæ¥å£æ¯å¦å¼å¯ï¼true-å¼å¯ false-å³é­ï¼
ecs.api.sync=true
# ECSæ¥å£è®¤è¯ä¿¡æ¯
ecs.api.auth=Basic b2NyQ3JhbmU6MTIzNDU2
# -------------------------------- é²åæ¸¯é¡¹ç®å¼å§ -------------------------------------
# ç¬¬ä¸æ¹æ¥å£ï¼å°å
external.api.url=http://*************:18060/m?xwl=RINflow/computer_lon/com_loction
# ç¬¬ä¸æ¹æ¥å£ï¼å¤ä½éç¥æ¯å¦å¼å¯ï¼true-å¼å¯ false-å³é­ï¼
external.api.restorationSync=true
# å¤ä½æ°æ®æ¥å£ï¼å°å
external.api.restorationUrl=http://*************:18060/m?xwl=RINflow/computer_lon/restoration
external.api.restorationStart=0
external.api.restorationEnd=500
external.api.waitDealTime=30000
# ç¬¬ä¸æ¹æ¥å£ï¼ç¨æ·å
external.api.user=1302
# ç¬¬ä¸æ¹æ¥å£ï¼å¯ç 
external.api.password=1302@FCG2024
# -------------------------------- é²åæ¸¯é¡¹ç®ç»æ -------------------------------------
### ä¸­éèéé¡¹ç®
# ç¬¬ä¸æ¹æ¥å£ï¼å°åï¼éè·¯å±ï¼ å¬ç½æµè¯å°åï¼http://tx-ch3.ezsoft.com.cn:9112
external.api.rmgName=TRMG01
external.api.gkptUrl=http://tx-ch3.ezsoft.com.cn:9112
spring.profiles.active=test

# å¿è·³ä¸æ¥éç½®
# æ¯å¦å¯ç¨å¿è·³åè½
pingfang.heartbeat.enabled=true
# å¿è·³ä¸æ¥é´éæ¶é´ï¼ç§ï¼
pingfang.heartbeat.interval=2

# å®¹å¨è¯å«ç®¡çå¨éç½®
# æ¯å¦å¯ç¨å®¹å¨è¯å«ç®¡çå¨
container-recognition.enabled=true
# æ¯å¦å¯ç¨ä¸è½¦çè¯å«çéæ
container-recognition.plate-integration-enabled=true

# åé½æ¸¯é¡¹ç®éæçPLCéç½®
# æ¯å¦è¦ä¿®æ­£plcå¼ï¼è§£å³è·³åé®é¢
plc.ifFixPlcValue=true
# PLCååå¹åº¦ä¸´çå¼
plc.changeValue=20000

# RTSPè§é¢æµé¢è¯å«éç½®
# æ¯å¦å¼å¯è§é¢æµé¢è¯å«
identity.ifRtspDetect=true

# RTSPè§é¢æµå°ååè¡¨ï¼4ä¸ªç¸æºï¼è´¦å·å¯ç ï¼admin/pfkj2016ï¼
rtsp.urlList=rtsp://admin:pfkj2016@*************:554/h264/ch1/main/av_stream,rtsp://admin:pfkj2016@*************:554/h264/ch1/main/av_stream,rtsp://admin:pfkj2016@*************:554/h264/ch1/main/av_stream,rtsp://admin:pfkj2016@*************:554/h264/ch1/main/av_stream

# å¯¹åºçç¸æºIDåè¡¨ï¼ä¸urlListä¸ä¸å¯¹åºï¼
# IPæ å°è§åï¼143->15, 144->13, 145->14, 146->12
rtsp.cameraIDList=15,13,14,12

# ææå¸§çï¼æ¯ç§ææå¸§æ°ï¼
rtsp.fps=5

# å»¶è¿ç»ææ¶é´ï¼ç§ï¼- é¿åé¢ç¹å¼å³
rtsp.endDelayTime=1

# RTSPè¯å«é«åº¦èå´éç½®
# æä½è¯å«é«åº¦ï¼ç±³ï¼- ä½äºæ­¤é«åº¦åæ­¢ææ
rtsp.recognition.minHeight=5.5
# æé«è¯å«é«åº¦ï¼ç±³ï¼- é«äºæ­¤é«åº¦ä¸å¯å¨ææ
rtsp.recognition.maxHeight=7.5



# RTSPè§é¢å½å¶éç½® - 720På½å¶
# æ¯å¦å¯ç¨è§é¢å½å¶åè½
rtsp.video.recording.enabled=true
# æ¯å¦å¯ç¨JVMå³é­é©å­ï¼é²æ­¢å¼å¸¸éåºæ¶è§é¢æä»¶æåï¼
rtsp.video.recording.shutdownHook=true
# è§é¢ç¼ç æ ¼å¼
rtsp.video.codec=H264
# è§é¢æ¯ç¹çï¼bpsï¼- 720Pæ¨è2Mbps
rtsp.video.bitrate=2000000
# è§é¢æ ¼å¼
rtsp.video.format=mp4
# è§é¢å¸§ç
rtsp.video.frameRate=25
# è§é¢åè¾¨ç - 720P
rtsp.video.width=1280
rtsp.video.height=720

# æ¥å¿æ¸çéç½®
# æ¥å¿æä»¶ä¿çå¤©æ°ï¼è¶è¿æ­¤å¤©æ°çæ¥å¿æä»¶å°è¢«å é¤ï¼
logging.cleanup.retentionDays=30
# æ¥å¿æ ¹ç®å½è·¯å¾
logging.cleanup.path=D:/log/rmg
# æ¯å¦å¯ç¨æ¥å¿èªå¨æ¸çåè½
logging.cleanup.enabled=true

# RTSPæºè½ç»ææ¡ä»¶éç½®
# æ¡ä»¶1ï¼é«å¯ä¿¡åº¦ç»æ
rtsp.end.highConfidence.enabled=true
rtsp.end.highConfidence.threshold=0.95
rtsp.end.highConfidence.consecutiveCount=3

# æ¡ä»¶2ï¼è·ç¦»è¿è¿ç»æ
rtsp.end.tooClose.enabled=true
rtsp.end.tooClose.heightThreshold=3.0

# æ¡ä»¶4ï¼è¶æ¶ç»æ
rtsp.end.timeout.enabled=true
rtsp.end.timeout.maxSeconds=60


