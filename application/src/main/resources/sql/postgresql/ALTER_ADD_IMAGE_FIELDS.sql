-- 为现有表添加图片信息字段
-- <AUTHOR>
-- @Date 2025/06/27
-- @Description: 为T_RTSP_PRE_RECOGNITION_RESULT表添加图片信息相关字段

-- 添加图片信息字段
ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "all_camera_images" TEXT;

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "best_camera_id" INTEGER;

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "best_image_path" VARCHAR(500);

ALTER TABLE "T_RTSP_PRE_RECOGNITION_RESULT" 
ADD COLUMN IF NOT EXISTS "best_image_url" VARCHAR(500);

-- 添加索引
CREATE INDEX IF NOT EXISTS "idx_rtsp_result_container_number" ON "T_RTSP_PRE_RECOGNITION_RESULT"("container_number");
CREATE INDEX IF NOT EXISTS "idx_rtsp_result_best_camera_id" ON "T_RTSP_PRE_RECOGNITION_RESULT"("best_camera_id");

-- 添加字段注释
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."all_camera_images" IS '所有相机图片信息(JSON格式)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."best_camera_id" IS '最佳结果相机ID';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."best_image_path" IS '最佳结果图片路径';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."best_image_url" IS '最佳结果图片URL';

-- 更新示例数据
UPDATE "T_RTSP_PRE_RECOGNITION_RESULT" 
SET "all_camera_images" = '{
    "cameras": [
        {
            "cameraId": 1,
            "cameraName": "相机1",
            "imageName": "IMG_20250627_143001_CAM1.jpg",
            "imagePath": "/rtsp/images/2025/06/27/IMG_20250627_143001_CAM1.jpg",
            "imageUrl": "http://localhost:8080/api/images/rtsp/2025/06/27/IMG_20250627_143001_CAM1.jpg",
            "fileSize": 1024000,
            "imageWidth": 1920,
            "imageHeight": 1080,
            "containerNo": "ABCD1234567",
            "containerIso": "22G1",
            "confidence": 0.95,
            "checkPassed": true,
            "captureTime": "2025-06-27 14:30:01"
        }
    ],
    "totalCount": 1,
    "bestCameraId": 1,
    "averageConfidence": 0.95
}',
"best_camera_id" = 1,
"best_image_path" = '/rtsp/images/2025/06/27/IMG_20250627_143001_CAM1.jpg',
"best_image_url" = 'http://localhost:8080/api/images/rtsp/2025/06/27/IMG_20250627_143001_CAM1.jpg'
WHERE "all_camera_images" IS NULL;

SELECT '图片信息字段添加完成！' AS message;
