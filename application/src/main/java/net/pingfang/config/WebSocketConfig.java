package net.pingfang.config;

import net.pingfang.util.DateUtil;
import javax.websocket.HandshakeResponse;
import javax.websocket.server.HandshakeRequest;
import javax.websocket.server.ServerEndpointConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.server.standard.ServerEndpointExporter;

@Configuration
@Slf4j
@Component
public class WebSocketConfig extends ServerEndpointConfig.Configurator {


    public static final String PID = "PID";
    public static final String UNDER_LINE = "_";

    @Override
    public void modifyHandshake(ServerEndpointConfig sec, HandshakeRequest request, HandshakeResponse response) {
        // 将IP放到配置里
        sec.getUserProperties().put(PID, UNDER_LINE + DateUtil.getCurrentTime());
        super.modifyHandshake(sec, request, response);
    }

    @Bean
    public ServerEndpointExporter serverEndpointExporter() {
        return new ServerEndpointExporter();
    }
}
