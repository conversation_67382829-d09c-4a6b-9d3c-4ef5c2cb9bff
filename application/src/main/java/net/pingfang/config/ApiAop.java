package net.pingfang.config;

import java.lang.reflect.Method;
import net.pingfang.constant.MdcConstant;
import net.pingfang.model.annotation.Log;
import net.pingfang.model.common.Result;
import net.pingfang.util.ExceptionUtil;
import net.pingfang.util.IpUtils;
import net.pingfang.util.JsonUtil;
import java.util.UUID;
import javax.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * @Author: CM
 * @Date: 2022/3/19 19:16
 * @Description: API接口Aop-捕获全局异常及出入参日志打印
 */
@Component
@Aspect
@Slf4j
public class ApiAop {


    @Pointcut("execution(* net.pingfang.api..*.*(..))")
    public void apiPointCut() {
    }

    @Around("apiPointCut()")
    public Object doAround(ProceedingJoinPoint joinPoint) {
        long startTime = System.currentTimeMillis();
        // 将traceId添加到日志中用于全链路追踪
        String traceId = UUID.randomUUID().toString();
        MDC.put(MdcConstant.traceId, traceId);

        // 获取自定义的Log注解,判断是否要打印日志
        int logType = 1;
        try {
            String targetName = joinPoint.getTarget().getClass().getName();
            String methodName = joinPoint.getSignature().getName();
            Object[] arguments = joinPoint.getArgs();
            Class<?> targetClass = Class.forName(targetName);
            Method[] methods = targetClass.getMethods();
            for (Method method : methods) {
                if (method.getName().equals(methodName)) {
                    Class[] clazzs = method.getParameterTypes();
                    if (clazzs.length == arguments.length) {
                        Log annotation = method.getAnnotation(Log.class);
                        if (annotation != null) {
                            logType = annotation.value();
                        }
                    }
                }
            }
        } catch (Exception exception) {
            log.error("Get @Log type error : {}", ExceptionUtil.getStackTrace(exception));
        }
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (logType == 2) {
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                // 打印请求URL
                log.info("request url : {}", request.getRequestURL());
                // 打印请求ip
                String ip = IpUtils.getIpAddr(request);
                log.info("request ip : {}", ip);
            }
            log.info("3234 ip : {}", joinPoint.getArgs());
            String requestParam = JsonUtil.toJson(joinPoint.getArgs());
            log.info("request param : {}", requestParam);
        }

        // 请求正常执行
        Object proceed = null;
        try {
            proceed = joinPoint.proceed();
        } catch (Throwable throwable) {
            String stackTrace = ExceptionUtil.getStackTrace(throwable);
            log.error("Catch global exception : {}", stackTrace);
            proceed = Result.error(MDC.get(MdcConstant.traceId));
        }
        // 判断是否打印出参日志

        if (logType == 2) {
            log.info("response:{}", JsonUtil.toJson(proceed));
            // 记录接口耗时
            long endTime = System.currentTimeMillis();
            log.info("Time-Consuming : {}ms", endTime - startTime);
        }

        return proceed;
    }

}
