package net.pingfang.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/27
 */
@Data
@Configuration
public class ProjectConfig {
    @Value("${project.version}")
    public String version;

    @Value("${pingfang.picture.localPath:D:/PFKJ/image/}")
    public String fileDirPath;

    /**
     * 是否开启视频流预识别
     */
    @Value("${identity.ifRtspDetect:false}")
    public Boolean ifRtspDetect;

    /**
     * 是否启动程序时即开启持续抓拍识别车顶号，默认关闭
     */
    @Value("${identity.ifCaptureRecognitionTopPlate:false}")
    public Boolean ifCaptureRecognitionTopPlate;

    /**
     * 是否启动程序时即开启三代车牌识别/平方车牌识别流程,默认关闭
     */
    @Value("${identity.ifRecognitionPlateNumber:false}")
    public Boolean ifRecognitionPlateNumber;

    /**
     * 是否启动程序时即开启持续抓拍识别箱号
     */
    @Value("${identity.ifCaptureRecognitionContainer:false}")
    public Boolean ifCaptureRecognitionContainer;

}
