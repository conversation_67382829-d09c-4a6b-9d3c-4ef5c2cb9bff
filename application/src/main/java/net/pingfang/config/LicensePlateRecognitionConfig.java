package net.pingfang.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 车牌识别相关配置
 * <AUTHOR>
 */
@Data
@Component
@ConfigurationProperties(prefix = "pingfang.license-plate")
public class LicensePlateRecognitionConfig {

    /**
     * 车牌识别摄像头IP地址数组
     */
    private List<String> cameraIps;

    /**
     * 是否启用车牌识别功能
     */
    private Boolean enabled = true;

    /**
     * 自动重连间隔时间（毫秒）
     */
    private Long reconnectInterval = 5000L;

    /**
     * 连接超时时间（秒）
     */
    private Integer connectionTimeout = 5;

    /**
     * 连接端口
     */
    private Integer port = 30000;

    /**
     * 图片保存路径
     */
    private String imageSavePath = "D:/license_plate_images/";

    /**
     * 门吊号
     */
    private String craneNo = "TRMG01";

    /**
     * 车牌识别加密密码
     */
    private String encryptionPassword;

    /**
     * 是否启用车牌加密
     */
    private Boolean encryptionEnabled = false;
}
