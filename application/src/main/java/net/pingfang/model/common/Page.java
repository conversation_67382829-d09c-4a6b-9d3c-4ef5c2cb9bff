package net.pingfang.model.common;

import java.util.List;
import lombok.Data;

/**
 * @Author: CM
 * @Date: 2022/3/23 18:22
 * @Description: 分页返回对象
 */
@Data
public class Page<T> {
    /**
     * 总数据量
     */
    private int total;

    /**
     * 查询数据
     */
    private List<T> records;


    public void initPage(int total, List<T> dataList) {
        this.total = total;
        records = dataList;
    }
}
