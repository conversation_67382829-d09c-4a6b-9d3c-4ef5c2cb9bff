package net.pingfang.model.dto;

import lombok.Data;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
@Data
public class ManualRecognizeDTO {

    /**
     * 吊具类型(0-长箱,1-短箱)
     */
    private Integer spreaderType;

    /**
     * 作业类型（1-装船；2-卸船；10-未知；11-其他）
     */
    private Integer workType;

    /**
     * 车道
     */
    private Long laneId;

    /**
     * 开始/结束(1-开始,2-停止)
     */
    private Integer flag;

    /**
     * 预置位类型
     */
    private Integer type;

}
