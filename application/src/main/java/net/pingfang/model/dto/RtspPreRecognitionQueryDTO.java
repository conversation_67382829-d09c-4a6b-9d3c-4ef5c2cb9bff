package net.pingfang.model.dto;

import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * RTSP预识别查询DTO
 * 
 * <AUTHOR>
 * @since 2025-07-07
 */
@Data
public class RtspPreRecognitionQueryDTO {
    
    /**
     * 吊机号
     */
    private String craneNo;
    
    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;
    
    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;
    
    /**
     * 结束原因
     */
    private String endReason;
    
    /**
     * ECS推送状态
     */
    private String ecsPushStatus;
    
    /**
     * 页码（从1开始）
     */
    private Integer page = 1;
    
    /**
     * 每页大小
     */
    private Integer size = 20;
    
    /**
     * 排序字段
     */
    private String sortBy = "endTime";
    
    /**
     * 排序方向（asc/desc）
     */
    private String sortDir = "desc";
}
