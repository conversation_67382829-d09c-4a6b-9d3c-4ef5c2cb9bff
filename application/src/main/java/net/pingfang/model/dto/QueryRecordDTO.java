package net.pingfang.model.dto;

import net.pingfang.enums.ContainerTypeEnum;
import net.pingfang.enums.IsSealNoEnum;
import net.pingfang.enums.TrueFalseEnum;
import net.pingfang.enums.WorkTypeEnum;
import lombok.Data;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
@Data
public class QueryRecordDTO {

    /**
     * 	主键id
     */
    private Long id;
    /**
     * 	作业唯一编号
     */
    private String seqNo;
    /**
     * 	作业类型
     */
    private Integer workType;
    /**
     * 作业类型（展示）
     */
    private String workTypeStr;
    public void setWorkType(Integer workType) {
        this.workType = workType;
        this.workTypeStr = WorkTypeEnum.getDescByValue(workType);
    }
    /**
     * 	箱类型
     */
    private Integer containerType;
    /**
     * 箱类型（展示）
     */
    private String containerTypeStr;
    public void setContainerType(Integer containerType) {
        this.containerType = containerType;
        this.containerTypeStr = ContainerTypeEnum.getTypeByValue(containerType);
    }
    /**
     * 	箱数量
     */
    private Integer ctnNum;
    /**
     * 	第一个箱号
     */
    private String ctnNoA;
    /**
     * 	第一个iso
     */
    private String isoNoA;
    /**
     * 	危标有无
     */
    private String dangerousLableA;

    /**
     * 是否有拖车车道(1-是,2-否)
     */
    private Integer isVehicleLane;
    /**
     * 	铅封有无
     */
    private Integer sealA;
    private String sealAStr;
    public void setSealA(Integer sealA){
        this.sealA = sealA;
        this.sealAStr = IsSealNoEnum.getCodeDescByCode(sealA);
    }
    /**
     * 	车上位置
     */
    private String plateLocationA;
    /**
     * 	是否残损
     */
    private Integer isDamageA;
    private String isDamageAStr;
    public void setIsDamageA(Integer isDamageA){
        this.isDamageA = isDamageA;
        this.isDamageAStr = TrueFalseEnum.getDescByValue(isDamageA);
    }
    /**
     * 	可信度
     */
    private String trustA;
    /**
     * 	第二个箱号
     */
    private String ctnNoB;
    /**
     * 	第二个iso
     */
    private String isoNoB;
    /**
     * 	危标有无
     */
    private String dangerousLableB;
    /**
     * 	铅封有无
     */
    private Integer sealB;
    private String sealBStr;
    public void setSealB(Integer sealB){
        this.sealB = sealB;
        this.sealBStr = IsSealNoEnum.getCodeDescByCode(sealB);
    }
    /**
     * 	车上位置
     */
    private String plateLocationB;
    /**
     * 	是否残损
     */
    private Integer isDamageB;
    private String isDamageBStr;
    public void setIsDamageB(Integer isDamageB){
        this.isDamageB = isDamageB;
        this.isDamageBStr = TrueFalseEnum.getDescByValue(isDamageB);
    }
    /**
     * 	可信度
     */
    private String trustB;
    /**
     * 	第三个箱号
     */
    private String ctnNoC;
    /**
     * 	第三个iso
     */
    private String isoNoC;
    /**
     * 	危标有无
     */
    private String dangerousLableC;
    /**
     * 	铅封有无
     */
    private Integer sealC;
    private String sealCStr;
    public void setSealC(Integer sealC){
        this.sealC = sealC;
        this.sealCStr = IsSealNoEnum.getCodeDescByCode(sealC);
    }
    /**
     * 	车上位置
     */
    private String plateLocationC;
    /**
     * 	是否残损
     */
    private Integer isDamageC;
    private String isDamageCStr;
    public void setIsDamageC(Integer isDamageC){
        this.isDamageC = isDamageC;
        this.isDamageCStr = TrueFalseEnum.getDescByValue(isDamageC);
    }
    /**
     * 	可信度
     */
    private String trustC;
    /**
     * 	第四个箱号
     */
    private String ctnNoD;
    /**
     * 	第四个iso
     */
    private String isoNoD;
    /**
     * 	危标有无
     */
    private String dangerousLableD;
    /**
     * 	铅封有无
     */
    private Integer sealD;
    private String sealDStr;
    public void setSealD(Integer sealD){
        this.sealD = sealD;
        this.sealDStr = IsSealNoEnum.getCodeDescByCode(sealD);
    }
    /**
     * 	车上位置
     */
    private String plateLocationD;
    /**
     * 	是否残损
     */
    private Integer isDamageD;
    private String isDamageDStr;
    public void setIsDamageD(Integer isDamageD){
        this.isDamageD = isDamageD;
        this.isDamageDStr = TrueFalseEnum.getDescByValue(isDamageD);
    }
    /**
     * 	可信度
     */
    private String trustD;
    /**
     * 作业高度
     */
    private Float height;
    /**
     * 	车号1
     */
    private String plateNumberA;
    /**
     * 	车方向1
     */
    private String plateDirA;
    /**
     * 	车号2
     */
    private String plateNumberB;
    /**
     * 	车方向2
     */
    private String plateDirB;
    /**
     * 	车道1
     */
    private String laneNumA;
    /**
     * 	车道2
     */
    private String laneNumB;
    /**
     * 	车门方向a
     */
    private String doorDirA;
    /**
     * 	车门方向b
     */
    private String doorDirB;
    /**
     * 	车门方向c
     */
    private String doorDirC;
    /**
     * 	车门方向d
     */
    private String doorDirD;
    /**
     * 	重量
     */
    private String weight;

    /**
     * 识别耗时
     */
    private Long recognizeTimeConsuming;

    /**
     * 抓拍耗时
     */
    private Long snapTimeConsuming;

    /**
     * 箱号校验a
     */
    private Boolean ctnCheckA;

    /**
     * 箱号校验b
     */
    private Boolean ctnCheckB;

    /**
     * 箱号校验c
     */
    private Boolean ctnCheckC;

    /**
     * 箱号校验d
     */
    private Boolean ctnCheckD;

    /**
     * 车号校验a
     */
    private Boolean plateCheckA;

    /**
     * 车号校验b
     */
    private Boolean plateCheckB;

    /**
     * 作业时间
     */
    private String passTime;

    /**
     * 上锁时间
     */
    private String lockTime;

    /**
     * 解锁时间
     */
    private String unlockTime;

    /**
     * 识别时间
     */
    private String recTime;
    /**
     * 车顶号a
     */
    private String topPlateA;
    /**
     * 车顶号b
     */
    private String topPlateB;
}
