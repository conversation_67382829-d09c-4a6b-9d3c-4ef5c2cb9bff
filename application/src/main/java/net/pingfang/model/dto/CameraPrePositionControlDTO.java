package net.pingfang.model.dto;

import lombok.Data;

/**
 * 相机
 *
 * @author: CM
 * @date: 2023/6/5
 */
@Data
public class CameraPrePositionControlDTO {
    /**
     * 主键id(相机id)
     */
    private Long id;

    /**
     * 预置位类型
     */
    private Integer type;

    /**
     * 指令(1-设置预置位, 2-调用相机预置位, 3-调用车道预置位)
     */
    private Integer command;

    /**
     * 吊具类型(0-长箱,1-短箱)
     */
    private Integer spreaderType;

    /**
     * 作业类型（1-装船；2-卸船；10-未知；11-其他）
     */
    private Integer workType;

    /**
     * 车道id
     */
    private Long laneId;

}
