package net.pingfang.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @since 2023-07-19 9:50
 */
@Data
@EqualsAndHashCode
public class ExportRecordExcelDTO {

    /**
     * 	序号
     */
    @ExcelProperty(value = "序号",index = 0)
    private Integer num;
    /**
     * 	作业唯一编号
     */
    @ExcelProperty(value = "作业编号",index = 1)
    private String seqNo;
    /**
     * 	作业类型
     */
    @ExcelProperty(value = "作业类型",index = 2)
    private String workTypeStr;
    /**
     * 	箱类型
     */
    @ExcelProperty(value = "箱类型",index = 3)
    private String containerTypeStr;
    /**
     * 	箱数量
     */
    @ExcelProperty(value = "箱数量",index = 4)
    private Integer ctnNum;
    /**
     * 	重量
     */
    @ExcelProperty(value = "重量",index = 5)
    private String weight;
    /**
     * 识别耗时
     */
    @ExcelProperty(value = "识别耗时",index = 6)
    private Long recognizeTimeConsuming;
    /**
     * 抓拍耗时
     */
    @ExcelProperty(value = "抓拍耗时",index = 7)
    private Long snapTimeConsuming;
    /**
     * 上锁时间
     */
    @ExcelProperty(value = "上锁时间",index = 8)
    private String lockTime;
    /**
     * 解锁时间
     */
    @ExcelProperty(value = "解锁时间",index = 9)
    private String unlockTime;
    /**
     * 识别时间
     */
    @ExcelProperty(value = "识别时间",index = 10)
    private String recTime;



    /**
     * 	第一个箱号
     */
    @ExcelProperty(value = "第一个箱号",index = 11)
    private String ctnNoA;
    /**
     * 	第一个iso
     */
    @ExcelProperty(value = "第一个iso",index = 12)
    private String isoNoA;
    /**
     * 	危标有无
     */
    @ExcelProperty(value = "危标有无",index = 13)
    private String dangerousLableA;
    /**
     * 	铅封有无
     */
    @ExcelProperty(value = "铅封有无",index = 14)
    private String sealAStr;
    /**
     * 	车上位置
     */
    @ExcelProperty(value = "车上位置",index = 15)
    private String plateLocationA;
    /**
     * 	残损
     */
    @ExcelProperty(value = "残损",index = 16)
    private String isDamageAStr;
    /**
     * 	箱门朝向
     */
    @ExcelProperty(value = "箱门朝向",index = 17)
    private String doorDirA;
    /**
     * 	箱号校验
     */
    @ExcelProperty(value = "箱号校验",index = 18)
    private Boolean ctnCheckA;
    /**
     * 	可信度
     */
    @ExcelProperty(value = "可信度",index = 19)
    private String trustA;



    /**
     * 	第二个箱号
     */
    @ExcelProperty(value = "第二个箱号",index = 20)
    private String ctnNoB;
    /**
     * 	第二个iso
     */
    @ExcelProperty(value = "第二个iso",index = 21)
    private String isoNoB;
    /**
     * 	危标有无
     */
    @ExcelProperty(value = "危标有无",index = 22)
    private String dangerousLableB;
    /**
     * 	铅封有无
     */
    @ExcelProperty(value = "铅封有无",index = 23)
    private String sealBStr;
    /**
     * 	车上位置
     */
    @ExcelProperty(value = "车上位置",index = 24)
    private String plateLocationB;
    /**
     * 	残损
     */
    @ExcelProperty(value = "残损",index = 25)
    private String isDamageBStr;
    /**
     * 	箱门朝向
     */
    @ExcelProperty(value = "箱门朝向",index = 26)
    private String doorDirB;
    /**
     * 	箱号校验
     */
    @ExcelProperty(value = "箱号校验",index = 27)
    private Boolean ctnCheckB;
    /**
     * 	可信度
     */
    @ExcelProperty(value = "可信度",index = 28)
    private String trustB;



    /**
     * 	第三个箱号
     */
    @ExcelProperty(value = "第三个箱号",index = 29)
    private String ctnNoC;
    /**
     * 	第三个iso
     */
    @ExcelProperty(value = "第三个iso",index = 30)
    private String isoNoC;
    /**
     * 	危标有无
     */
    @ExcelProperty(value = "危标有无",index = 31)
    private String dangerousLableC;
    /**
     * 	铅封有无
     */
    @ExcelProperty(value = "铅封有无",index = 32)
    private String sealCStr;
    /**
     * 	车上位置
     */
    @ExcelProperty(value = "车上位置",index = 33)
    private String plateLocationC;
    /**
     * 	残损
     */
    @ExcelProperty(value = "残损",index = 34)
    private String isDamageCStr;
    /**
     * 	箱门朝向
     */
    @ExcelProperty(value = "箱门朝向",index = 35)
    private String doorDirC;
    /**
     * 	箱号校验
     */
    @ExcelProperty(value = "箱号校验",index = 36)
    private Boolean ctnCheckC;
    /**
     * 	可信度
     */
    @ExcelProperty(value = "可信度",index = 37)
    private String trustC;


    /**
     * 	第四个箱号
     */
    @ExcelProperty(value = "第四个箱号",index = 38)
    private String ctnNoD;
    /**
     * 	第四个iso
     */
    @ExcelProperty(value = "第四个iso",index = 39)
    private String isoNoD;
    /**
     * 	危标有无
     */
    @ExcelProperty(value = "危标有无",index = 40)
    private String dangerousLableD;
    /**
     * 	铅封有无
     */
    @ExcelProperty(value = "铅封有无",index = 41)
    private String sealDStr;
    /**
     * 	车上位置
     */
    @ExcelProperty(value = "车上位置",index = 42)
    private String plateLocationD;
    /**
     * 	残损
     */
    @ExcelProperty(value = "残损",index = 43)
    private String isDamageDStr;
    /**
     * 	箱门朝向
     */
    @ExcelProperty(value = "箱门朝向",index = 44)
    private String doorDirD;
    /**
     * 	箱号校验
     */
    @ExcelProperty(value = "箱号校验",index = 45)
    private Boolean ctnCheckD;
    /**
     * 	可信度
     */
    @ExcelProperty(value = "可信度",index = 46)
    private String trustD;


    /**
     * 	车号1
     */
    @ExcelProperty(value = "车号1",index = 47)
    private String plateNumberA;
    /**
     * 	车方向1
     */
    @ExcelProperty(value = "车方向1",index = 48)
    private String plateDirA;
    /**
     * 	车道1
     */
    @ExcelProperty(value = "车道1",index = 49)
    private String laneNumA;
    /**
     * 车号校验a
     */
    @ExcelProperty(value = "车号校验a",index = 50)
    private Boolean plateCheckA;



    /**
     * 	车号2
     */
    @ExcelProperty(value = "车号2",index = 51)
    private String plateNumberB;
    /**
     * 	车方向2
     */
    @ExcelProperty(value = "车方向2",index = 52)
    private String plateDirB;
    /**
     * 	车道2
     */
    @ExcelProperty(value = "车道2",index = 53)
    private String laneNumB;
    /**
     * 车号校验b
     */
    @ExcelProperty(value = "车号校验b",index = 54)
    private Boolean plateCheckB;






}
