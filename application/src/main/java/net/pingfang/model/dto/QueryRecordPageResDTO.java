package net.pingfang.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @since 2023-07-17 17:19
 */
public class QueryRecordPageResDTO {

    /**
     * 	主键id
     */
    private Long id;
    /**
     * 	作业唯一编号
     */
    private String seqNo;
    /**
     * 	作业类型
     */
    private Integer workType;
    /**
     * 	箱类型
     */
    private Integer containerType;
    /**
     * 	箱数量
     */
    private Integer ctnNum;
    /**
     * 	第一个箱号
     */
    private String ctnNoA;
    /**
     * 	第一个iso
     */
    private String isoNoA;
    /**
     * 	危标有无
     */
    private String dangerousLableA;
    /**
     * 	铅封有无
     */
    private String sealA;
    /**
     * 	车上位置
     */
    private String plateLocationA;
    /**
     * 	可信度
     */
    private String trustA;
    /**
     * 	第二个箱号
     */
    private String ctnNoB;
    /**
     * 	第二个iso
     */
    private String isoNoB;
    /**
     * 	危标有无
     */
    private String dangerousLableB;
    /**
     * 	铅封有无
     */
    private String sealB;
    /**
     * 	车上位置
     */
    private String plateLocationB;
    /**
     * 	可信度
     */
    private String trustB;
    /**
     * 	第三个箱号
     */
    private String ctnNoC;
    /**
     * 	第三个iso
     */
    private String isoNoC;
    /**
     * 	危标有无
     */
    private String dangerousLableC;
    /**
     * 	铅封有无
     */
    private String sealC;
    /**
     * 	车上位置
     */
    private String plateLocationC;
    /**
     * 	可信度
     */
    private String trustC;
    /**
     * 	第四个箱号
     */
    private String ctnNoD;
    /**
     * 	第四个iso
     */
    private String isoNoD;
    /**
     * 	危标有无
     */
    private String dangerousLableD;
    /**
     * 	铅封有无
     */
    private String sealD;
    /**
     * 	车上位置
     */
    private String plateLocationD;
    /**
     * 	可信度
     */
    private String trustD;
    /**
     * 	车号1
     */
    private String plateNumberA;
    /**
     * 	车方向1
     */
    private String plateDirA;
    /**
     * 	车号2
     */
    private String plateNumberB;
    /**
     * 	车方向2
     */
    private String plateDirB;
    /**
     * 	重量
     */
    private String weight;

    /**
     * 识别耗时
     */
    private Long recognizeTimeConsuming;

    /**
     * 抓拍耗时
     */
    private Long snapTimeConsuming;

    /**
     * 箱号校验a
     */
    private boolean ctnCheckA;

    /**
     * 箱号校验b
     */
    private boolean ctnCheckB;

    /**
     * 箱号校验c
     */
    private boolean ctnCheckC;

    /**
     * 箱号校验d
     */
    private boolean ctnCheckD;

    /**
     * 车号校验a
     */
    private boolean plateCheckA;

    /**
     * 车号校验b
     */
    private boolean plateCheckB;

    /**
     * 作业时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime passTime;

}
