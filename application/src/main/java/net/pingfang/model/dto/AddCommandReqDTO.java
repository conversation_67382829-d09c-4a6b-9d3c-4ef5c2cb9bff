package net.pingfang.model.dto;

import lombok.Data;

/**
 * <AUTHOR>
 * @since 2024-10-23 17:17
 */
@Data
public class AddCommandReqDTO {

    /**
     * 作业类型
     */
    private String workType;

    /**
     * 运单号
     */
    private String waybillNumber;

    /**
     * 需求单号
     */
    private String requirementNumber;

    /**
     * 箱号
     */
    private String ctnNo;

    /**
     * iso
     */
    private String iso;

    /**
     * 空重：E：空，F：重
     */
    private String stuffingStaus;

    /**
     * 毛重
     */
    private String weight;

    /**
     * 箱重
     */
    private String ctnWeight;

    /**
     * 货重
     */
    private String cargoWeight;

    /**
     * 原始位置
     */
    private String originalPosition;

    /**
     * 目标位置
     */
    private String targetPosition;

    /**
     * 车号
     */
    private String trainNum;

    /**
     * 车型
     */
    private String vehicleModel;

    /**
     * 顺位号
     */
    private String sequenceNumber;

    /**
     * 作业设备编码
     */
    private String craneNum;

    /**
     * 作业日期
     */
    private String workDate;

    /**
     * 股道名称
     */
    private String stockTrackName;

    /**
     * 股道代码
     */
    private String stockTrackCode;


}
