package net.pingfang.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 心跳数据传输对象
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class HeartBeatDTO {

    /**
     * 系统名称
     * 格式：龙门吊OCR(TRMG01)、龙门吊OCR(TRMG02)
     */
    private String systemName;

    /**
     * 心跳值
     * 从1-255循环累加
     */
    private Integer heartBeatValue;
}
