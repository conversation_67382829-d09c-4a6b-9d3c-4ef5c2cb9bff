package net.pingfang.model.dto;

import java.util.List;
import lombok.Data;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
@Data
public class ExportRecordReqDTO {

    /**
     * 箱号(支持模糊)
     */
    private String ctnNo;

    /**
     * 车号
     */
    private String plateNumber;

    /**
     * 作业类型集合
     */
    private List<Integer> workTypeList;

    /**
     * 箱类型(吊具类型)集合
     */
    private List<Integer> spreaderTypeList;

    /**
     * 车道编号集合 11
     */
    private List<String> laneList;

    /**
     * 是否残损(1-是,2-否) 11
     */
    private Integer isDamage;

    /**
     * 危标有无(1-有,2-无)
     */
    private Integer dangerous;

    /**
     * 铅封有无(1-有,2-无)
     */
    private Integer seal;

    /**
     * 开始时间
     */
    private String fromDate;

    /**
     * 结束时间
     */
    private String toDate;

}
