package net.pingfang.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * <AUTHOR>
 * @CreateTime 2023/9/28 9:30
 * @Description
 */
@Data
public class QueryLogListResDTO {
    /**
     * 日志绝对路径
     */
    private String absolutePath;

    /**
     * 日志创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime creationTime;
}
