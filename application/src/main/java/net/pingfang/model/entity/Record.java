package net.pingfang.model.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.time.LocalDateTime;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
@Data
public class Record {

    /**
     * 	主键id
     */
    private Long id;
    /**
     * 	作业唯一编号
     */
    private String seqNo;
    /**
     * 	作业类型
     */
    private Integer workType;
    /**
     * 作业高度
     */
    private Float height;
    /**
     * 	箱类型
     */
    private Integer containerType;
    /**
     * 	箱数量
     */
    private Integer ctnNum;
    /**
     * 	第一个箱号
     */
    private String ctnNoA;
    /**
     * 	第一个iso
     */
    private String isoNoA;
    /**
     * 	危标有无
     */
    private Integer dangerousLableA;
    /**
     * 	铅封有无
     */
    private Integer sealA;
    /**
     * 	车上位置
     */
    private String plateLocationA;
    /**
     * 	可信度
     */
    private String trustA;
    /**
     * 	是否残损
     */
    private Integer isDamageA;

    /**
     * 	第二个箱号
     */
    private String ctnNoB;
    /**
     * 	第二个iso
     */
    private String isoNoB;
    /**
     * 	危标有无
     */
    private Integer dangerousLableB;
    /**
     * 	铅封有无
     */
    private Integer sealB;
    /**
     * 	车上位置
     */
    private String plateLocationB;
    /**
     * 	可信度
     */
    private String trustB;
    /**
     * 	是否残损
     */
    private Integer isDamageB;

    /**
     * 	第三个箱号
     */
    private String ctnNoC;
    /**
     * 	第三个iso
     */
    private String isoNoC;
    /**
     * 	危标有无
     */
    private Integer dangerousLableC;
    /**
     * 	铅封有无
     */
    private Integer sealC;
    /**
     * 	车上位置
     */
    private String plateLocationC;
    /**
     * 	可信度
     */
    private String trustC;
    /**
     * 	是否残损
     */
    private Integer isDamageC;

    /**
     * 	第四个箱号
     */
    private String ctnNoD;
    /**
     * 	第四个iso
     */
    private String isoNoD;
    /**
     * 	危标有无
     */
    private Integer dangerousLableD;
    /**
     * 	铅封有无
     */
    private Integer sealD;
    /**
     * 	车上位置
     */
    private String plateLocationD;
    /**
     * 	可信度
     */
    private String trustD;
    /**
     * 	是否残损
     */
    private Integer isDamageD;

    /**
     * 	车号1
     */
    private String plateNumberA;
    /**
     * 	车方向1
     */
    private String plateDirA;
    /**
     * 	车号2
     */
    private String plateNumberB;
    /**
     * 	车方向2
     */
    private String plateDirB;
    /**
     * 	车道1
     */
    private String laneNumA;
    /**
     * 	车道2
     */
    private String laneNumB;
    /**
     * 	车门方向a
     */
    private String doorDirA;
    /**
     * 	车门方向b
     */
    private String doorDirB;
    /**
     * 	车门方向c
     */
    private String doorDirC;
    /**
     * 	车门方向d
     */
    private String doorDirD;
    /**
     * 	重量
     */
    private String weight;

    /**
     * 识别耗时
     */
    private Long recognizeTimeConsuming;

    /**
     * 抓拍耗时
     */
    private Long snapTimeConsuming;

    /**
     * 箱号校验a
     */
    private Boolean ctnCheckA;

    /**
     * 箱号校验b
     */
    private Boolean ctnCheckB;

    /**
     * 箱号校验c
     */
    private Boolean ctnCheckC;

    /**
     * 箱号校验d
     */
    private Boolean ctnCheckD;

    /**
     * 车号校验a
     */
    private Boolean plateCheckA;

    /**
     * 车号校验b
     */
    private Boolean plateCheckB;

    /**
     * 作业时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private LocalDateTime passTime;

    /**
     * 上锁时间
     */
    private String lockTime;

    /**
     * 解锁时间
     */
    private String unlockTime;

    /**
     * 识别时间
     */
    private String recTime;
    /**
     * 车顶号a
     */
    private String topPlateA;
    /**
     * 车顶号b
     */
    private String topPlateB;
}
