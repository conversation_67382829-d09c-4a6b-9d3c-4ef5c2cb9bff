package net.pingfang.model.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class OverResult {

	/**
	 *是否成功
	 */
	private String status;
	/**
	 * 处理结果，200为成功，其他失败，详细参考响应状态码
	 */
	private int infoCode;
	/**
	 * 请求消息
	 */
	private String msg;

	/**
	 *返回推送记录
	 */
	private String data;

	/**
	 *是否成功
	 */
	private Boolean success;
}
