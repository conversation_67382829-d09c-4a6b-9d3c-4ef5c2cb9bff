package net.pingfang.model.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@Data
@ToString
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TokenResult {

	/**
	 * 授权token信息
	 */
	private String access_token;
	/**
	 * Token类型
	 */
	private String token_type;

	/**
	 *刷新token信息
	 */
	private String refresh_token;
	/**
	 * 过期时间
	 */
	private int expires_in;
	/**
	 * 过期时间
	 */
	private String userCode;
}
