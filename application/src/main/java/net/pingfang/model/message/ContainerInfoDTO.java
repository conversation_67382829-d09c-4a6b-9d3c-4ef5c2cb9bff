package net.pingfang.model.message;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-09-12 17:50
 */
@Data
@AllArgsConstructor
@Builder
public class ContainerInfoDTO {
	/**
	 * 对应的正面吊或龙门吊设备Id
	 */
	String sbId;
	/**
	 * 集装箱箱号
	 */
	String xh;
	/**
	 * 类型：0：抓取，1：放下（必传，尽量不要重复）
	 */
	String type;
	/**
	 * 5324562
	 */
	String ch;
	/**
	 * 重量信息（吨）  (空重：0：空，1：重)
	 */
	String empty;
	/**
	 * 告警信息,拼接信息
	 */
	String alarm;

}
