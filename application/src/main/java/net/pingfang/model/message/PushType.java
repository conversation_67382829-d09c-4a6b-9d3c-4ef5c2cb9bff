package net.pingfang.model.message;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 推送类型
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-26
 */
@Getter
@AllArgsConstructor
public enum PushType {
	/**
	 * 抓取
	 */
	GRAB("0", "抓取"),
	/**
	 * 放下
	 */
	PUT_DOWN("1", "放下");

	private final String label;

	private final String name;

	/**
	 *
	 * @param label
	 * @return
	 */
	public static PushType cmdtype(String label) {
		PushType[] values = PushType.values();
		for (PushType value : values) {
			if (value.getLabel().equals(label)) {
				return value;
			}
		}
		return null;
	}
}
