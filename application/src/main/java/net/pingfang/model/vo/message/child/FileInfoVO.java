package net.pingfang.model.vo.message.child;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.util.List;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: CM
 * @Date: 2021/5/29 11:10
 * @Description:
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class FileInfoVO {
    /**
     * 备注
     */
    private String note;

    /**
     * 图片区域集合(没有用)
     */
    private List<String> img_dect_rect;

    /**
     * 图片名称集合
     */
    private List<String> img_path_name;

    /**
     * 相机名称集合
     */
    private List<String> location;

    /**
     * 图片数量
     */
    private Integer img_num;

    /**
     * 图片类型 0-开始  1-箱  3-车  4-残损  5-解锁
     */
    private Integer snap_img_type;
}
