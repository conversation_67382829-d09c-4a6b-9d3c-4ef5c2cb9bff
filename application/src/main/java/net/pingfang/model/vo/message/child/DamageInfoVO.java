package net.pingfang.model.vo.message.child;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: CM
 * @Date: 2021/9/10 13:35
 * @Description:
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class DamageInfoVO {
    /**
     * 残损箱序号 --对应箱节点的索引, 0,1,2,3
     */
    private Integer conta_index;

    /**
     * 残损类型(没有用)
     */
    private Integer damaged_type;

    /**
     * 可信度(没有用)
     */
    private Double trust;

    /**
     * 残损级别,长宽深,面积  MINOR,22,43,0,946,多组数据用|分割
     */
    private String note;

    /**
     * 残损类型,多组数据用|分割,0：腐蚀，1：凹，2: 凸，3：箱面变形，4：拉杆变形，5: 棱变形，6：柱变形，7：洞
     */
    private String type;

    /**
     * 残损程度,算法的残损度,多组数据用|分割
     */
    private String degree;

    /**
     * 图片对应的箱面  小面,大面
     */
    private String position;


}
