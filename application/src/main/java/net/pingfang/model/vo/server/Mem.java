package net.pingfang.model.vo.server;

import net.pingfang.util.ArithUtil;

/**
 * 內存相关信息
 *
 * <AUTHOR>
 */
public class Mem
{
    /**
     * 内存总量
     */
    private double total;

    /**
     * 已用内存
     */
    private double used;

    /**
     * 剩余内存
     */
    private double free;

    /**
     * 内存使用率
     */
    private double usedRatio;

    public double getTotal()
    {
        return ArithUtil.div(total, (1024 * 1024 * 1024), 2);
    }

    public void setTotal(long total)
    {
        this.total = total;
    }

    public double getUsed()
    {
        return ArithUtil.div(used, (1024 * 1024 * 1024), 2);
    }

    public void setUsed(long used)
    {
        this.used = used;
    }

    public double getFree()
    {
        return ArithUtil.div(free, (1024 * 1024 * 1024), 2);
    }

    public void setFree(long free)
    {
        this.free = free;
    }

    public double getUsage()
    {
        return ArithUtil.mul(ArithUtil.div(used, total, 4), 100);
    }

    public double getUsedRatio() {
        return usedRatio;
    }

    public void setUsedRatio(double usedRatio) {
        this.usedRatio = usedRatio;
    }
}
