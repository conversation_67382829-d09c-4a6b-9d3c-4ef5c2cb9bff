package net.pingfang.model.vo.message;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * @Author: CM
 * @Date: 2021/5/31 9:59
 * @Description:
 */
@Getter
@Setter
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class MessageSixteenVO {
    /**
     * 预识别时间
     */
    @JSONField(name = "UPDATE_TIME")
    private String updateTime;

    /**
     * 车道号
     */
    @JSONField(name = "LaneID")
    private String laneNum;

    /**
     * 拖车号
     */
    @JSONField(name = "VEHICLE_ID")
    private String truckNumber;

    /**
     * 岸桥号
     */
    @JSONField(name = "POINT_OF_WORK")
    private String craneNum;


}
