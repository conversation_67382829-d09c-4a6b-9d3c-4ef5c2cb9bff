package net.pingfang.core;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/5/30
 */
public class Test {

    public static void main(String[] args) throws Exception{

//        AlgorithmService algorithmService = new AlgorithmService();
//        algorithmService.init();

//        CoreFlow coreFlow = new CoreFlow();
//        coreFlow.start();
        //色调测试
//        HikvisionService controlService = new HikvisionService();
//        controlService.test();
//        controlService.tone( 0, 1, +1);
        //查询预置位
        /*HikvisionService controlService = new HikvisionService();
        controlService.test();*/

        // 相机测试
/*        HikvisionServiceikvisionService controlService = new HikvisionService();
        controlService.register("192.168.1.211", 8000, "admin", "pfkj2016");
//        controlService.setPosition(1, 8, 2);
        controlService.control(1,21,2,5000);*/
//
//        AlgorithmService algorithmService = new AlgorithmService();
//        algorithmService.init();
//        long time = System.currentTimeMillis();
//        for (int index = 0; index < 20; index++) {
////            controlService.snapMemory(1, "D:\\temp\\20230530\\"+index+".jpg");
//            controlService.snapFile(1, "D:\\temp\\20230530\\"+index+".jpg");
//            long newTime = System.currentTimeMillis();
//            System.out.println("抓拍耗时:"+(newTime-time));
////            DetectResults detectResults = algorithmService.detPicture("D:\\temp\\20230530\\"+index+".jpg");
////            algorithmService.recPicture("D:\\temp\\20230530\\"+index+".jpg", detectResults);
//            Thread.sleep(100);
//            long newTime1 = System.currentTimeMillis();
//            System.out.println("识别耗时:"+(newTime1-time));
//            time= newTime1;
//        }



//        controlService.snapMemory(1, "D:\\temp\\20230530\\"+111+".jpg");

//        controlService.control(1, 23, 1,5000);


        // 算法测试

 /*       AlgorithmService algorithmService = new AlgorithmService();
        algorithmService.init();
        DetectResults detectResults = algorithmService.detPicture("D:\\temp\\20230530\\"+index+".jpg");
        algorithmService.recPicture("D:\\temp\\20230530\\"+index+".jpg", detectResults);*/

    }
}
