package net.pingfang.core;

import com.sun.jna.Structure;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

/**
 * 智能结构
 *
 * @author: CM
 * @date: 2023/5/30
 */
public class SmartStructure extends Structure {
    @Override
    protected List<String> getFieldOrder() {
        Class clazz = this.getClass();

        // 返回一个 Field 对象，该对象反映此 Class 对象所表示的类或接口的指定已声明字段（包括私有成员
        Field[] fields = clazz.getDeclaredFields();
        List<String> fi = new ArrayList<>();
        for (Field f : fields) {
            /**
             * 取消默认 Java 语言访问控制检查的能力
             */
            f.setAccessible(true);
            fi.add(f.getName());
        }
        return fi;
    }
//    @Override
//    protected List<String> getFieldOrder() {
//        List<String> fieldOrderList = new ArrayList<String>();
//        for (Class<?> cls = getClass();
//             !cls.equals(SmartStructure.class);
//             cls = cls.getSuperclass()) {
//            Field[] fields = cls.getDeclaredFields();
//            int modifiers;
//            for (Field field : fields) {
//                modifiers = field.getModifiers();
//                if (Modifier.isStatic(modifiers) || !Modifier.isPublic(modifiers)) {
//                    continue;
//                }
//                fieldOrderList.add(field.getName());
//            }
//        }
//        return fieldOrderList;
//    }
}
