package net.pingfang.core.hardware.camera.hikvision;

import com.sun.jna.ptr.IntByReference;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import net.pingfang.core.DllFactory;
import net.pingfang.util.ExceptionUtil;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;

/**
 * 摄像头所有控制方法
 *
 * @author: CM
 * @date: 2023/5/30
 */
@Slf4j
public class HikvisionService {
    int userID = -1;
    HCNetSDK instance = null;

    short derNum = 0;

    private DllFactory dllFactory;

    public HikvisionService(DllFactory dllFactory) {
        this.dllFactory = dllFactory;
    }

    /**
     * 注册
     */
    public boolean register(String ip, int port, String username, String password) {
        if (instance != null && userID != -1) {
            log.debug("相机{}已注册", ip);
            return true;
        }

        if (instance == null) {
            instance = dllFactory.getHkSdk();
            log.info("海康动态库初始化成功");
            boolean b = instance.NET_DVR_Init();
            if (!b) {
                log.info("初始化SDK失败");
                return false;
            }
            log.info("初始化SDK成功");
        }

        if (userID == -1) {
            HCNetSDK.NET_DVR_DEVICEINFO_V30 m_strDeviceInfo = new HCNetSDK.NET_DVR_DEVICEINFO_V30();
            userID = instance.NET_DVR_Login_V30(ip, (short) port, username, password, m_strDeviceInfo);
            if (userID == -1) {
                int iErr = instance.NET_DVR_GetLastError();
                log.error("注册失败,错误号：{}", iErr);
                IntByReference m_err = new IntByReference();
                log.error("注册失败,错误信息：{}", instance.NET_DVR_GetErrorMsg(m_err));
                return false;
            }
            derNum = m_strDeviceInfo.wDevType;
            log.info("相机{}注册成功", ip);
        }

        return true;
    }

    /**
     * 登出
     */
    public boolean Logout() {
        if (userID >= 0) {
            if (!instance.NET_DVR_Logout(userID)) {
                log.error("注销失败，错误码为{}", instance.NET_DVR_GetLastError());
                return false;
            }
            log.info("注销成功");
            instance.NET_DVR_Cleanup();
        } else {
            log.info("设备未登录");
            instance.NET_DVR_Cleanup();
            return false;
        }
        return true;
    }


    /**
     * 云台控制
     *
     * @param speed 云台控制的速度，用户按不同解码器的速度控制值设置。取值范围[1,7]
     */
    public void control(int channel, int iPTZCommand, int speed, int time) {
        try {
            HCNetSDK instance = dllFactory.getHkSdk();
            boolean ret;
            if (speed >= 1)//有速度的ptz
            {
                // dwStop 云台停止动作或开始动作：0－开始；1－停止
                ret = instance.NET_DVR_PTZControlWithSpeed_Other(userID, channel, iPTZCommand, 0, speed);
                if (!ret) {
                    log.error("云台控制(开始)失败");
                    return;
                }
                //控制时间
                Thread.sleep(time);
                ret = instance.NET_DVR_PTZControlWithSpeed_Other(userID, channel, iPTZCommand, 1, speed);
                if (!ret) {
                    log.error("云台控制(结束)失败");
                    return;
                }
                log.error("云台控制(完成)：时间：{}", LocalDateTime.now());
            } else//速度为默认时
            {
                ret = instance.NET_DVR_PTZControl_Other(userID, channel, iPTZCommand, 0);
                if (!ret) {
                    log.error("云台控制(开始)失败");
                    int iErr = instance.NET_DVR_GetLastError();
                    log.error("云台控制(开始)失败,错误号：{}", iErr);
                    IntByReference m_err = new IntByReference();
                    log.error("云台控制(开始)失败,错误信息：{}", instance.NET_DVR_GetErrorMsg(m_err));
                    return;
                }
                //控制时间
                Thread.sleep(speed);
                ret = instance.NET_DVR_PTZControl_Other(userID, channel, iPTZCommand, 1);
                if (!ret) {
                    log.error("云台控制(结束)失败");
                    return;
                }
                log.error("云台控制(完成)：时间：{}", LocalDateTime.now());
            }
        } catch (Exception exception) {
            log.error("云台控制异常:{}", ExceptionUtil.getStackTrace(exception));
        }
    }


    /**
     * 抓拍保存文件
     */
    public boolean snapFile(int channel, String path) {
        long startTime = System.currentTimeMillis();
        HCNetSDK.NET_DVR_WORKSTATE_V30 devwork = new HCNetSDK.NET_DVR_WORKSTATE_V30();
        if (!instance.NET_DVR_GetDVRWorkState_V30(userID, devwork)) {
            // 返回Boolean值，判断是否获取设备能力
            log.info("hksdk(抓图)-返回设备状态失败");
            return false;
        }
        //图片质量
        HCNetSDK.NET_DVR_JPEGPARA jpeg = new HCNetSDK.NET_DVR_JPEGPARA();
        //设置图片分辨率
        jpeg.wPicSize = 4;
        //设置图片质量
        jpeg.wPicQuality = 0;
        IntByReference a = new IntByReference();
        //需要加入通道
        boolean ok = instance.NET_DVR_CaptureJPEGPicture(userID, channel, jpeg, path.getBytes(StandardCharsets.UTF_8));
        if (ok) {
            log.info("hksdk(抓图)-结果状态值(0表示成功):" + instance.NET_DVR_GetLastError());
        } else {
            IntByReference m_err = new IntByReference();
            String err = instance.NET_DVR_GetErrorMsg(m_err);
            log.info("hksdk(抓图)-抓取失败,错误码:{}", m_err.getValue() + "," + err);
            return false;
        }
        long endTime = System.currentTimeMillis();
        log.info("图片:{},抓拍耗时:{}ms", path, (endTime - startTime));
        return true;
    }

    /**
     * 抓拍到内存
     */
    public boolean snapMemory(int channel, File file) {
        HCNetSDK.NET_DVR_WORKSTATE_V30 devwork = new HCNetSDK.NET_DVR_WORKSTATE_V30();
        if (!instance.NET_DVR_GetDVRWorkState_V30(userID, devwork)) {
            // 返回Boolean值，判断是否获取设备能力
            log.info("hksdk(抓图)-返回设备状态失败:{}", instance.NET_DVR_GetLastError());
            return false;
        }
        //图片质量
        HCNetSDK.NET_DVR_JPEGPARA jpeg = new HCNetSDK.NET_DVR_JPEGPARA();
        //设置图片分辨率
        jpeg.wPicSize = 4;
        //设置图片质量
        jpeg.wPicQuality = 0;
        IntByReference a = new IntByReference();
        //设置图片大小
        ByteBuffer jpegBuffer = ByteBuffer.allocate(1920 * 1080);
        //需要加入通道
        boolean ok = instance.NET_DVR_CaptureJPEGPicture_NEW(userID, channel, jpeg, jpegBuffer, 1920 * 1080, a);
        if (ok) {
            log.debug("hksdk(抓图)-结果状态值(0表示成功):{}", instance.NET_DVR_GetLastError());
            //存储到本地
            BufferedOutputStream outputStream = null;
            try {
                outputStream = new BufferedOutputStream(new FileOutputStream(file));
                outputStream.write(jpegBuffer.array(), 0, a.getValue());
                outputStream.flush();
            } catch (Exception e) {
                log.error("抓图存储文件失败:{}", ExceptionUtil.getStackTrace(e));
                return false;
            }  finally {
                if (outputStream != null) {
                    try {
                        outputStream.close();
                    } catch (IOException e) {
                        log.error("抓图存储文件失败:{}", ExceptionUtil.getStackTrace(e));
                        return false;
                    }
                }
            }
        } else {
            IntByReference m_err = new IntByReference();
            String err = instance.NET_DVR_GetErrorMsg(m_err);
            log.info("hksdk(抓图)-抓取失败,错误码:{}", m_err.getValue() + "," + err);
            return false;
        }
        return true;
    }

    /**
     * 设置预置点
     *
     * @param lChannel      通道编号
     * @param iPTZCommand   命令 8-设置预置点 9-清除预置点 39-转到预置点
     * @param dwPresetIndex 预置点的序号（从1开始），最多支持300个预置点
     */
    public boolean setPosition(int lChannel, int iPTZCommand, int dwPresetIndex) {

        boolean b = instance.NET_DVR_PTZPreset_Other(userID, lChannel, iPTZCommand, dwPresetIndex);
        if (b) {
            log.info("预置位{},设置成功:", dwPresetIndex);
        } else {
            IntByReference m_err = new IntByReference();
            String err = instance.NET_DVR_GetErrorMsg(m_err);
            log.info("预置位{},设置失败:{}", dwPresetIndex, m_err.getValue() + "," + err);
        }
        return b;
    }

    /**
     * 重启摄像头
     *
     * @return
     */
    public boolean restart() {
        boolean result = instance.NET_DVR_RebootDVR(userID);
        return result;
    }

    /**
     * 色调
     *
     * @param dwDeviceNum 分布式多屏控制器中，高的2个字节表示串口号，低2字节表示屏幕序号（0xffff表示所有屏幕）；集中式控制器中高2字节无效，低2字节屏幕序号从0开始
     * @param byColorType 类型：1- 亮度，2- 对比度，3- 饱和度，4- 清晰度
     * @param byScale     控制取值：对应类型 -1或者+1
     * @return
     */
    public boolean tone(int dwDeviceNum, int byColorType, int byScale) {
        // 注册摄像头
        boolean registerResult = this.register("192.168.1.211", 8000, "admin", "pfkj2016");
        if (!registerResult) {
            return false;
        }

        HCNetSDK instance = dllFactory.getHkSdk();
        //屏幕控制结构体
        HCNetSDK.NET_DVR_SCREEN_CONTROL lpStruScreenCtrl = new HCNetSDK.NET_DVR_SCREEN_CONTROL();
        lpStruScreenCtrl.byRes1[0] = (byte) 0;
        lpStruScreenCtrl.byRes2[0] = (byte) 0;
        lpStruScreenCtrl.byProtocol = (byte) 1;
        lpStruScreenCtrl.dwCommand = 4;
        //结构体大小
        lpStruScreenCtrl.dwSize = 0;
        //屏幕控制参数-屏幕控制结构体
        HCNetSDK.NET_DVR_SCREEN_CONTROL_PARAM struControlParam = new HCNetSDK.NET_DVR_SCREEN_CONTROL_PARAM();
        //dwCommand 为4时有效
        if (4 == lpStruScreenCtrl.dwCommand) {
            //显示单元颜色控制结构体
            HCNetSDK.NET_DVR_DISPLAY_COLOR_CTRL struDisplayCtrl = new HCNetSDK.NET_DVR_DISPLAY_COLOR_CTRL();
            //类型：1- 亮度，2- 对比度，3- 饱和度，4- 清晰度
            struDisplayCtrl.byColorType = (byte) byColorType;
            //控制取值：-1或者+1
            struDisplayCtrl.byScale += (byte) 1;
            struControlParam.struDisplayCtrl = struDisplayCtrl;
        }
        lpStruScreenCtrl.struControlParam = struControlParam;

        boolean b = instance.NET_DVR_ScreenCtrl(userID, dwDeviceNum, lpStruScreenCtrl);
        if (b) {
            log.info("色调设置成功:");
        } else {
            IntByReference m_err = new IntByReference();
            String err = instance.NET_DVR_GetErrorMsg(m_err);
            log.info("色调设置失败:{}", m_err.getValue() + "," + err);
        }
        return b;
    }

    /**
     * 重启摄像头
     *
     * @return
     */
    public void test() {
        // 注册摄像头
        boolean registerResult = this.register("192.168.1.211", 8000, "admin", "pfkj2016");
        if (!registerResult) {
            return;
        }

        HCNetSDK instance = dllFactory.getHkSdk();
        //屏幕控制结构体
        HCNetSDK.NET_DVR_SCREEN_CONTROL lpStruScreenCtrl = new HCNetSDK.NET_DVR_SCREEN_CONTROL();
        lpStruScreenCtrl.byRes1[0] = (byte) 0;
        lpStruScreenCtrl.byRes2[0] = (byte) 0;
        lpStruScreenCtrl.byProtocol = (byte) 1;
        lpStruScreenCtrl.dwCommand = 4;
        //结构体大小
        lpStruScreenCtrl.dwSize = 0;
        //屏幕控制参数-屏幕控制结构体
        HCNetSDK.NET_DVR_SCREEN_CONTROL_PARAM struControlParam = new HCNetSDK.NET_DVR_SCREEN_CONTROL_PARAM();
        struControlParam.byRes[0] = 0;
        //dwCommand 为4时有效
        if (4 == lpStruScreenCtrl.dwCommand) {
            //显示单元颜色控制结构体
            HCNetSDK.NET_DVR_DISPLAY_COLOR_CTRL struDisplayCtrl = new HCNetSDK.NET_DVR_DISPLAY_COLOR_CTRL();
            //类型：1- 亮度，2- 对比度，3- 饱和度，4- 清晰度
            struDisplayCtrl.byColorType = (byte) 1;
            //控制取值：-1或者+1
            struDisplayCtrl.byScale = (byte) -1;
            struControlParam.struDisplayCtrl = struDisplayCtrl;
        }
        lpStruScreenCtrl.struControlParam = struControlParam;


        boolean b = instance.NET_DVR_ScreenCtrl(userID, 0, lpStruScreenCtrl);
        if (b) {
            log.info("色调设置成功:");
        } else {
            IntByReference m_err = new IntByReference();
            String err = instance.NET_DVR_GetErrorMsg(m_err);
            log.info("色调设置失败:{}", m_err.getValue() + "," + err);
        }
    }

}
