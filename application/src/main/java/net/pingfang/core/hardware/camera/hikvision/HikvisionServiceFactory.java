package net.pingfang.core.hardware.camera.hikvision;

import net.pingfang.core.DllFactory;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 摄像头所有控制方法
 *
 * @author: CM
 * @date: 2023/5/30
 */
@Slf4j
@Component
public class HikvisionServiceFactory {
    @Autowired
    private DllFactory dllFactory;

    private Map<String, HikvisionService> map = new ConcurrentHashMap<>();

    public HikvisionService getService(String ip) {
        if (map.get(ip) != null) {
            return map.get(ip);
        }
        HikvisionService hikvisionService = new HikvisionService(dllFactory);
        map.put(ip, hikvisionService);
        return hikvisionService;
    }

}
