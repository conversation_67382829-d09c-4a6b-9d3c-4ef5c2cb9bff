package net.pingfang.core;

import com.sun.jna.Native;
import net.pingfang.core.algorithm.ailib.Container;
import net.pingfang.core.algorithm.ailib.ResponseEnum;
import net.pingfang.core.algorithm.ailib.TopPlate;
import net.pingfang.core.hardware.camera.hikvision.HCNetSDK;
import net.pingfang.util.ExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 动态库工厂
 *
 * @author: CM
 * @date: 2023/8/10
 */
@Component
@Slf4j
public class DllFactory {
    @Autowired
    private DllPath dllPath;

    private volatile static HCNetSDK hcNetSDK;

    private volatile static Container ocrContainerSDK;

    private volatile static TopPlate topPlateSDK;


    public HCNetSDK getHkSdk() {
        if (hcNetSDK == null) {
            synchronized (DllFactory.class) {
                try {
                    if (hcNetSDK == null) {
                        // 目前本地启动会报错,本地启动可以先把下面这行注释掉 TODO
                        hcNetSDK = Native.load(dllPath.getHk(), HCNetSDK.class);
                        log.info("海康SDK加载成功");
                    }
                }catch (Exception exception){
                    log.error("海康SDK加载异常:{}", ExceptionUtil.getStackTrace(exception));
                }

            }
        }
        return hcNetSDK;
    }

    public Container getOcrContainerSdk() {
        if (ocrContainerSDK == null) {
            synchronized (DllFactory.class) {
                if (ocrContainerSDK == null) {
                    // 目前本地启动会报错,本地启动可以先把下面这行注释掉 TODO
                    try {
                        ocrContainerSDK = Native.load(dllPath.getOcrContainer(), Container.class);
                        log.info("箱号算法SDK加载成功");
                        int result = ocrContainerSDK.InitDetector();
                        log.info("箱号算法初始化结果:{}", ResponseEnum.getDescByValue(result));
                    }catch (Exception exception){
                        log.error("箱号算法SDK加载异常:{}", ExceptionUtil.getStackTrace(exception));
                    }

                }
            }
        }
        return ocrContainerSDK;
    }

    public TopPlate getTopPlateSdk() {
        if (topPlateSDK == null) {
            synchronized (DllFactory.class) {
                if (topPlateSDK == null) {
                    try {
                        topPlateSDK = Native.load(dllPath.getTopPlate(), TopPlate.class);
                        log.info("车顶号算法SDK加载成功");
                        int result = topPlateSDK.init_detecter();
                        log.info("车顶号算法初始化结果:{}", ResponseEnum.getDescByValue(result));
                    }catch (Exception exception){
                        log.error("车顶号算法SDK加载异常:{}", ExceptionUtil.getStackTrace(exception));
                    }

                }
            }
        }
        return topPlateSDK;
    }


}
