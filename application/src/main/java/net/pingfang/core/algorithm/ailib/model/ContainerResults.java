package net.pingfang.core.algorithm.ailib.model;

import net.pingfang.core.SmartStructure;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 多箱号结果
 *
 * @author: CM
 * @date: 2023/5/29
 */
@Data
@NoArgsConstructor
public class ContainerResults extends SmartStructure {
    /**
     * 检测序号（备用）
     */
    public int index;
    /**
     * 检测结果数量
     */
    public int det_num;
    /**
     * 检测结果（最大返回32个检测结果）
     */
    public ContainerResult[] dr = new ContainerResult[32];
}
