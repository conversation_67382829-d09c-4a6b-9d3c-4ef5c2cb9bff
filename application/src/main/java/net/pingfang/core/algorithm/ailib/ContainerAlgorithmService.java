package net.pingfang.core.algorithm.ailib;

import net.pingfang.core.DllFactory;
import net.pingfang.core.algorithm.ailib.model.ContainerResult;
import net.pingfang.core.algorithm.ailib.model.ContainerResults;
import net.pingfang.core.algorithm.ailib.model.DetectResults;
import net.pingfang.core.algorithm.ailib.model.ImgData;
import net.pingfang.core.algorithm.ailib.model.RecContainer;
import net.pingfang.util.JsonUtil;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import javax.imageio.ImageIO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/5/30
 */
@Component
@Slf4j
public class ContainerAlgorithmService {
    @Autowired
    private DllFactory dllFactory;

    private Container container = null;

    /**
     * 初始化
     */
    public void init(){
        container = dllFactory.getOcrContainerSdk();
        log.debug("箱号算法服务初始化完成");
    }

    /**
     * 原箱号检测方法（保留兼容性）
     */
    public DetectResults detPicture(String filePath){
        long startTime = System.currentTimeMillis();

        DetectResults detectResults = new DetectResults();
        int i = container.det_img_file(filePath, detectResults);
        long endTime = System.currentTimeMillis();
        log.debug("图片:{}调用完毕：{},检测耗时:{}ms,图片检测结果{}", filePath,i, (endTime-startTime), JsonUtil.toJson(detectResults));
        return detectResults;
    }

    /**
     * 吊具摄像头下的箱号检测方法
     */
    public DetectResults detPictureSpreader(String filePath, int cameraID){
        long startTime = System.currentTimeMillis();

        DetectResults detectResults = new DetectResults();
        int i = container.det_img_spreader_file(filePath, cameraID, detectResults);
        long endTime = System.currentTimeMillis();
        log.info("吊具摄像头箱号检测 - 图片:{}, 相机ID:{}, 调用结果:{}, 检测耗时:{}ms",
                filePath, cameraID, i, (endTime-startTime));
        return detectResults;
    }

    /**
     * 原箱号识别方法（保留兼容性）
     */
    public List<RecContainer> recPicture(String filePath, DetectResults detectResults){
        if (detectResults==null || detectResults.getDet_num() == 0){
            log.info("未检测到数据,不进行识别");
            return null;
        }

        long startTime = System.currentTimeMillis();

        ContainerResults containerResults = new ContainerResults();
        int i = container.RecContainerFile(filePath, detectResults, containerResults);
        List<RecContainer> containerList = new ArrayList<>();
        if (containerResults.det_num>0){
            RecContainer recContainer;
            for (int index = 0; index < containerResults.det_num; index++) {
                ContainerResult containerResult = containerResults.getDr()[index];
                if (StringUtils.isEmpty(containerResult.getId()) && StringUtils.isEmpty(containerResult.getIso())){
                    continue;
                }
                recContainer = new RecContainer();
                recContainer.setCtnNo(new String(containerResult.getId(), StandardCharsets.UTF_8).trim());
                recContainer.setIso(new String(containerResult.getIso(), StandardCharsets.UTF_8).trim());
                recContainer.setDetRect(containerResult.getDet_rect());
                recContainer.setTrust(containerResult.getTrust());
                recContainer.setIdConf(containerResult.getIdConf());
                recContainer.setIsoConf(containerResult.getIsoConf());
                recContainer.setBcheck(containerResult.isBcheck());
                recContainer.setSzNote(new String(containerResult.getSzNote(), StandardCharsets.UTF_8).trim());
                containerList.add(recContainer);
            }
        }
        long endTime = System.currentTimeMillis();
        log.debug("图片:{}调用完毕：{},检测耗时:{}ms,图片识别结果{}", filePath,i, (endTime-startTime),JsonUtil.toJson(containerList));
        return containerList;
    }

    /**
     * 吊具摄像头下的箱号识别方法
     */
    public List<RecContainer> recPictureSpreader(String filePath, int cameraID, DetectResults detectResults){
        if (detectResults==null || detectResults.getDet_num() == 0){
            log.info("吊具摄像头箱号识别 - 未检测到数据,不进行识别");
            return null;
        }

        long startTime = System.currentTimeMillis();

        ContainerResults containerResults = new ContainerResults();
        int i = container.RecContainerSpreaderFile(filePath, cameraID, detectResults, containerResults);

        List<RecContainer> containerList = new ArrayList<>();
        if (containerResults.det_num>0){
            RecContainer recContainer;
            for (int index = 0; index < containerResults.det_num; index++) {
                ContainerResult containerResult = containerResults.getDr()[index];
                if (StringUtils.isEmpty(containerResult.getId()) && StringUtils.isEmpty(containerResult.getIso())){
                    continue;
                }
                recContainer = new RecContainer();
                recContainer.setCtnNo(new String(containerResult.getId(), StandardCharsets.UTF_8).trim());
                recContainer.setIso(new String(containerResult.getIso(), StandardCharsets.UTF_8).trim());
                recContainer.setDetRect(containerResult.getDet_rect());
                recContainer.setTrust(containerResult.getTrust());
                recContainer.setIdConf(containerResult.getIdConf());
                recContainer.setIsoConf(containerResult.getIsoConf());
                recContainer.setBcheck(containerResult.isBcheck());
                recContainer.setSzNote(new String(containerResult.getSzNote(), StandardCharsets.UTF_8).trim());
                containerList.add(recContainer);
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("吊具摄像头箱号识别 - 图片:{}, 相机ID:{}, 调用结果:{}, 识别耗时:{}ms, 识别结果:{}",
                filePath, cameraID, i, (endTime-startTime), JsonUtil.toJson(containerList));
        return containerList;
    }

    /**
     * 将BufferedImage转换为ImgData
     * @param bufferedImage 图片对象
     * @param cameraID 相机ID
     * @return ImgData对象
     */
    public ImgData convertBufferedImageToImgData(BufferedImage bufferedImage, int cameraID) {
        try {
            ImgData imgData = new ImgData();

            // 设置图片基本信息
            imgData.width = bufferedImage.getWidth();
            imgData.heigth = bufferedImage.getHeight();
            imgData.imgType = 0; // 0:jpg
            imgData.imgInfo = "camera_" + cameraID; // 相机信息

            // 将BufferedImage转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, "jpg", baos);
            byte[] imageBytes = baos.toByteArray();

            // 设置图片数据
            imgData.dataLen = imageBytes.length;
            System.arraycopy(imageBytes, 0, imgData.imgData, 0, Math.min(imageBytes.length, imgData.imgData.length));

            log.debug("BufferedImage转换为ImgData完成 - 宽度:{}, 高度:{}, 数据长度:{}",
                    imgData.width, imgData.heigth, imgData.dataLen);

            return imgData;
        } catch (IOException e) {
            log.error("BufferedImage转换为ImgData失败", e);
            return null;
        }
    }

    /**
     * 吊具摄像头下的箱号检测方法（使用内存数据）
     */
    public DetectResults detPictureSpreaderFromMemory(BufferedImage bufferedImage, int cameraID) {
        long startTime = System.currentTimeMillis();

        ImgData imgData = convertBufferedImageToImgData(bufferedImage, cameraID);
        if (imgData == null) {
            log.error("图片数据转换失败，无法进行检测 - 相机ID:{}", cameraID);
            return null;
        }

        DetectResults detectResults = new DetectResults();
        log.debug("调用底层算法: container.det_img_spreader(imgData, cameraID={}, detectResults)", cameraID);
        int i = container.det_img_spreader(imgData, cameraID, detectResults);
        long endTime = System.currentTimeMillis();

        log.info("吊具摄像头箱号检测(内存) - 相机ID:{}, 调用结果:{}, 检测耗时:{}ms, 检测结果数:{}",
                cameraID, i, (endTime-startTime), detectResults.det_num);

        return detectResults;
    }

    /**
     * 吊具摄像头下的箱号识别方法（使用内存数据）
     */
    public List<RecContainer> recPictureSpreaderFromMemory(BufferedImage bufferedImage, int cameraID, DetectResults detectResults) {
        if (detectResults == null || detectResults.getDet_num() == 0) {
            log.info("吊具摄像头箱号识别(内存) - 未检测到数据,不进行识别");
            return null;
        }

        long startTime = System.currentTimeMillis();

        ImgData imgData = convertBufferedImageToImgData(bufferedImage, cameraID);
        if (imgData == null) {
            log.error("图片数据转换失败，无法进行识别 - 相机ID:{}", cameraID);
            return null;
        }

        ContainerResults containerResults = new ContainerResults();
        log.debug("调用底层算法: container.RecContainerSpreaderImg(imgData, cameraID={}, detectResults, containerResults)", cameraID);
        int i = container.RecContainerSpreaderImg(imgData, cameraID, detectResults, containerResults);

        List<RecContainer> containerList = new ArrayList<>();
        if (containerResults.det_num > 0) {
            RecContainer recContainer;
            for (int index = 0; index < containerResults.det_num; index++) {
                ContainerResult containerResult = containerResults.getDr()[index];
                if (StringUtils.isEmpty(containerResult.getId()) && StringUtils.isEmpty(containerResult.getIso())) {
                    continue;
                }
                recContainer = new RecContainer();
                recContainer.setCtnNo(new String(containerResult.getId(), StandardCharsets.UTF_8).trim());
                recContainer.setIso(new String(containerResult.getIso(), StandardCharsets.UTF_8).trim());
                recContainer.setDetRect(containerResult.getDet_rect());
                recContainer.setTrust(containerResult.getTrust());
                recContainer.setIdConf(containerResult.getIdConf());
                recContainer.setIsoConf(containerResult.getIsoConf());
                recContainer.setBcheck(containerResult.isBcheck());
                recContainer.setSzNote(new String(containerResult.getSzNote(), StandardCharsets.UTF_8).trim());
                containerList.add(recContainer);
            }
        }
        long endTime = System.currentTimeMillis();
        log.info("吊具摄像头箱号识别(内存) - 相机ID:{}, 调用结果:{}, 识别耗时:{}ms, 识别结果:{}",
                cameraID, i, (endTime-startTime), JsonUtil.toJson(containerList));
        return containerList;
    }

}
