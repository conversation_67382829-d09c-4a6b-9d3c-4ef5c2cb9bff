package net.pingfang.core.algorithm.ailib.model;

import net.pingfang.core.SmartStructure;
import lombok.Data;

/**
 * XXX
 *
 * @author: CM
 * @date: 2024/4/19
 */
@Data
public class PlateResult extends SmartStructure {

    //typedef struct tagPlateResult
    //{
    //	char plate[16];//车架号
    //	char color[8];//车牌颜色
    //	float trust;//可信度
    //	char szNote[16];//备注
    //}PlateResult;

    public byte[] plate = new byte[16]; 			//车架号
    public byte[] color = new byte[8]; 			//车牌颜色
    public float trust;			//整个结果可信度
    public byte[] szNote = new byte[64];			//备注信息

}
