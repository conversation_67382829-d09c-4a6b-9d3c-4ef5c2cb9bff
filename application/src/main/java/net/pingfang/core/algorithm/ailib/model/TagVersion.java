package net.pingfang.core.algorithm.ailib.model;

import net.pingfang.core.SmartStructure;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * XXX
 *
 * @author: CM
 * @date: 2023/8/15
 */
@Data
@NoArgsConstructor
public class TagVersion extends SmartStructure {
    /**
     * 版本号
     */
    public byte[] versionID = new byte[32];
    /**
     * 备注信息
     */
    public byte[] note = new byte[256];

}
