package net.pingfang.core.algorithm.ailib.model;

import net.pingfang.core.SmartStructure;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * XXX
 *
 * @author: CM
 * @date: 2023/5/29
 */
@Data
@NoArgsConstructor
public class DetectResult extends SmartStructure {
    /**
     * 检测类型
     * 0：横向(侧面水平)，
     * 1：纵向(侧面垂直)，
     * 2：横向(顶部正向)，
     * 3：横向(顶部反向)，
     * 4：有破损(占位，无法使用)，
     * 5：检测箱门（利用箱门锁杆特征），
     * 6：检测门把手（无铅封），
     * 7：检测到锁（有铅封），
     * 8：罐箱，
     * 9：冷藏箱，
     * 10：电子关锁（占位，未启用），
     * 11：吊具，
     * 12：集装箱体(占位，无法使用)，
     * 13：角柱(占位，无法使用)，
     * 14：危险品标识，
     * 15：箱面，
     * 16：箱门（全箱门区域），
     * 17：箱头（全箱头区域）
     */

    /** 车顶号
     * 0：箱门，
     * 1：箱头，
     * 2：车顶号（正向），
     * 3：车头，
     * 4：车顶号（反向），
     * 5：二维码，
     * 6：车架，
     * 7：车尾架号
     */
    public int det_type;
    /**
     * 箱号区域
     */
    public RectDet det_rect;
    /**
     * 检测可信度
     */
    public float det_fr;
}
