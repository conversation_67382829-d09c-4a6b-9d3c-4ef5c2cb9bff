package net.pingfang.core.algorithm.ailib;

import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * 返回值枚举类
 *
 * @author: CM
 * @date: 2023/5/29
 */
public enum ResponseEnum {
    DR_OK(0, "成功"),
    DR_ERR(1, "失败"),
    DR_INIT_NUM_ERR(2, "初始化检测器数量错误，范围1-4"),
    DR_KEY_ERR(3, "加密锁错误"),
    DR_DET_PARAM_ERR(4, "参数错误"),
    DR_NOT_INIT(5, "未初始化"),
    DR_FILE_PATH_ERR(6, "文件路径错误"),
    DR_OPEN_IMG_ERR(7, "打开图片文件错误"),
    DR_INIT_REG_ERR(8, "初始化识别器数量错误"),
    REC_CUDA_ERR_SZ(9, "加载CUDA错误"),
    REC_GPU_ERR_SZ(10, "加载GPU错误"),
    REC_CPU_ERR_SZ(11, "加载CPU错误"),
    REC_JSON_ERR_SZ(12, "JSON报文格式错误"),
    REC_JSON_PARMA_ERR_SZ(13, "JSON报文解析失败"),
    REC_LPBYTE_ERR_SZ(14, "LPBYTE图片解析失败"),
    REC_Outputjson_ERR_SZ(15, " Outputjson生成失败"),
    REC_OutLPBYTE_ERR_SZ(16, "生成LPBYTE失败"),
    REC_ERROR_SZ(17, "识别错误"),
    REC_DETAREA_ERR_SZ(18, "识别区域划分有问题，导致箱号ISO无法正常拼接"),
    DR_CATCH_ERR(100, "捕获异常");

    @Getter
    private Integer value;

    @Getter
    private String desc;

    ResponseEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDescByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (ResponseEnum enu : ResponseEnum.values()) {
            if (enu.getValue().equals(value)) {
                return enu.getDesc();
            }
        }
        return null;
    }

    public static Integer getValueByDesc(String desc) {
        if (StringUtils.isEmpty(desc)) {
            return null;
        }
        for (ResponseEnum enu : ResponseEnum.values()) {
            if (enu.getDesc().equalsIgnoreCase(desc)) {
                return enu.getValue();
            }
        }
        return null;
    }
}
