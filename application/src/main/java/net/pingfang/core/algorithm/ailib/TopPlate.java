package net.pingfang.core.algorithm.ailib;

import com.sun.jna.Library;
import net.pingfang.core.algorithm.ailib.model.DetectResult;
import net.pingfang.core.algorithm.ailib.model.DetectResults;
import net.pingfang.core.algorithm.ailib.model.ImgData;
import net.pingfang.core.algorithm.ailib.model.PlateResult;
import net.pingfang.core.algorithm.ailib.model.TagVersion;

/**
 * XXX
 *
 * @author: CM
 * @date: 2024/4/19
 */
public interface TopPlate extends Library {

    /**
     * 检测识别初始化
     *
     * @return 返回初始化结果
     */
    int init_detecter();

    /**
     * 检测图片文件/检测图片数据
     *
     * @param img_path 图片文件路径
     * @param pdrs     箱号检测结果
     * @return
     */
    int det_img_file(String img_path, DetectResults pdrs);

    /**
     * 检测图片文件/检测图片数据
     *
     * @param pImgData 图片数据结构
     * @param pdrs     箱号检测结果
     * @return
     */
    int det_img(ImgData pImgData, DetectResults pdrs);

    /**
     * 识别图片文件/识别图片数据
     *
     * @param filePath 图片文件路径
     * @param pdr      第二步的检测结果
     * @param ppr      箱号识别结果，具体见新箱号识别结果定义
     * @return
     */
    int recg_plate_img_file(String filePath, DetectResult pdr, PlateResult ppr);

    /**
     * 识别图片文件/识别图片数据
     *
     * @param pImgData 图片数据结构
     * @param pdrs     第二步的检测结果
     * @param pcrs     识别结果
     * @return
     */
    int recg_plate_img(ImgData pImgData, DetectResults pdrs, PlateResult pcrs);


    int GetAlgVersion(TagVersion version);
}
