package net.pingfang.core.algorithm.plate;

import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * 返回值枚举类
 *
 * @author: CM
 * @date: 2023/5/29
 */
public enum PlateResponseEnum {
    // 	LPR_OK=0,					//成功
    //	LPR_ERR=1,					//失败
    //	LPR_OVER_MAX_CAMERA_NUM=2,		//超出相机最大连接数量
    //	LPR_NOT_ADD_CAMERA=3,			//未添加该相机
    //	LPR_CAMERA_LOGIN_ERR=4,			//相机登录失败
    //	LPR_CAMERA_ALREAD_ADD=5,			//已经添加该相机，本次添加失败
    //	LPR_PARAM_ERR=6,				//参数错误
    //	LPR_NOT_REG_LPR_CALLBACK=7,			//未注册识别结果回调
    //	LPR_CAMERA_PLAY_ERR=8,			//启动视频失败
    //	LPR_CAMERA_DISCONNECT=9,			//相机已断开
    //	LPR_TIME_OUT=10,				//接收图片超时时间
    //	LPR_KEY_ERR=11,				//软件锁错误
    //	LPR_LOST_FILE=12,				//缺少文件
    //	LPR_NOT_SUPPORT=20,				//不支持该功能
    LPR_OK(0, "成功"),
    LPR_ERR(1, "失败"),
    LPR_OVER_MAX_CAMERA_NUM(2, "超出相机最大连接数量"),
    LPR_NOT_ADD_CAMERA(3, "未添加该相机"),
    LPR_CAMERA_LOGIN_ERR(4, "相机登录失败"),
    LPR_CAMERA_ALREAD_ADD(5, "已经添加该相机，本次添加失败"),
    LPR_PARAM_ERR(6, "参数错误"),
    LPR_NOT_REG_LPR_CALLBACK(7, "未注册识别结果回调"),
    LPR_CAMERA_PLAY_ERR(8, "启动视频失败"),
    LPR_CAMERA_DISCONNECT(9, "相机已断开"),
    LPR_TIME_OUT(10, "接收图片超时时间"),
    LPR_KEY_ERR(11, "软件锁错误"),
    LPR_LOST_FILE(12, "缺少文件"),
    LPR_NOT_SUPPORT(20, "不支持该功能"),
    ;

    @Getter
    private Integer value;

    @Getter
    private String desc;

    PlateResponseEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDescByValue(Integer value) {
        if (value == null) {
            return null;
        }
        for (PlateResponseEnum enu : PlateResponseEnum.values()) {
            if (enu.getValue().equals(value)) {
                return enu.getDesc();
            }
        }
        return null;
    }

    public static Integer getValueByDesc(String desc) {
        if (StringUtils.isEmpty(desc)) {
            return null;
        }
        for (PlateResponseEnum enu : PlateResponseEnum.values()) {
            if (enu.getDesc().equalsIgnoreCase(desc)) {
                return enu.getValue();
            }
        }
        return null;
    }
}
