package net.pingfang.core.algorithm.ailib.model;

import net.pingfang.model.entity.Img;
import java.util.List;
import lombok.Data;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/5/31
 */
@Data
public class RecContainerResult {
    /**
     * 唯一任务编号
     */
    private String seqNo;

    /**
     * 识别箱号列表
     */
    private List<RecContainer> recContainerList;

    /**
     * 图片列表
     */
    private List<Img> imgList;

    /**
     * 识别耗时
     */
    private Long recognizeTimeConsuming;

    /**
     * 抓拍耗时
     */
    private Long snapTimeConsuming;

}
