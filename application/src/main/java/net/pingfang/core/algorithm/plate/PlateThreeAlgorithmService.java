package net.pingfang.core.algorithm.plate;

import com.sun.jna.Native;
import com.sun.jna.platform.win32.WinDef;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import net.pingfang.core.DllFactory;
import net.pingfang.core.algorithm.plate.model.PlateThreeResult;
import net.pingfang.core.flow.CoreFlow;
import net.pingfang.enums.WebMessageTypeEnum;
import net.pingfang.model.dto.MessageDTO;
import net.pingfang.model.vo.business.PlateNumberMessageVO;
import net.pingfang.util.DateUtil;
import net.pingfang.util.ExceptionUtil;
import net.pingfang.util.RedisUtil;
import net.pingfang.util.WebSocketUtil;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.TimeUnit;
import javax.imageio.ImageIO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * XXX
 *
 * @author: CM
 * @date: 2024/4/18
 */
@Slf4j
public class PlateThreeAlgorithmService {

    private PlateThree plateThree;
    private String dllPath;
    private static LPRResult_CALLBACK callback;

    public PlateThreeAlgorithmService(String dp) {
        this.dllPath = dp;
    }

    // 算法不支持并发,因此需要实例化多个
    public void initThreePlateSdk() {
        synchronized (DllFactory.class) {
            // 目前本地启动会报错,本地启动可以先把下面这行注释掉 TODO
            plateThree = Native.load(dllPath, PlateThree.class);
            log.info("三代车牌算法SDK加载成功");
        }
    }

    public boolean addCamera(String ip) {
        if (plateThree == null) {
            log.error("车牌算法SDK未加载");
            return false;
        }

        int res = plateThree.add_camera(ip);
        log.info("车牌算法添加相机:{}完成,结果为:{}", ip, PlateResponseEnum.getDescByValue(res));
        return res == PlateResponseEnum.LPR_OK.getValue();
    }

    public boolean addCallback(String ip, String fileDirPath, RedisUtil redisUtil, WebSocketUtil webSocketUtil) {
        if (plateThree == null) {
            log.error("车牌算法SDK未加载");
            return false;
        }
        callback = createCallBack(fileDirPath, redisUtil, webSocketUtil);
        int res = plateThree.reg_lpr_callback(ip, callback, new WinDef.LPVOID());
        log.info("车牌算法添加回调函数:{}完成,结果为:{}", ip, PlateResponseEnum.getDescByValue(res));
        return res == PlateResponseEnum.LPR_OK.getValue();
    }

    public boolean softTrigger(String ip) {
        if (plateThree == null) {
            log.error("车牌算法SDK未加载");
            return false;
        }

        int res = plateThree.soft_trigger(ip);
        log.debug("车牌软触发:{}完成,结果为:{}", ip, PlateResponseEnum.getDescByValue(res));
        return res == PlateResponseEnum.LPR_OK.getValue();
    }

    public static void main(String[] args) {
        //京A17061_20240514233350626
        //京A17061_20240514233354398
    }

    private LPRResult_CALLBACK createCallBack(String fileDirPath, RedisUtil redisUtil, WebSocketUtil webSocketUtil) {
        return new LPRResult_CALLBACK() {
            @Override
            public void invoke(PlateThree.LprResult pLPRR, PlateThree.ImgInfo pVehImg, PlateThree.PlateImgInfo pPlateImg, WinDef.LPVOID pParam) {
                try {
                    String ip = new String(pLPRR.ip, StandardCharsets.UTF_8).trim();
                    log.debug("相机:{},收到回调结果", ip);
                    String plateNumber = new String(pLPRR.plate, "GBK").trim();
                    if (!StringUtils.isEmpty(plateNumber)) {
                        if (redisUtil.get("PLATE_" + plateNumber) != null) {
                            log.debug("车牌:{}重复识别,过滤掉", plateNumber);
                            return;
                        }
                        String date = DateUtil.getDate(DateUtil.TYPE_FIVE);
                        log.info("相机:{},接收到识别的车牌:{}", ip, plateNumber);
                        log.info("车牌大图大小：{},车牌局部图大小：{}", pVehImg.nImgDataLen, pPlateImg.nImgDataLen);
                        redisUtil.setEx("PLATE_" + plateNumber, date, 10, TimeUnit.MINUTES);
                        // websocket发送前台
                        PlateNumberMessageVO plateNumberMessageVO = new PlateNumberMessageVO();
                        plateNumberMessageVO.setPlateNumberA(plateNumber);
                        MessageDTO messageDTO = new MessageDTO();
                        messageDTO.setTime(DateUtil.getDate(DateUtil.TYPE_THREE));
                        messageDTO.setObject(plateNumberMessageVO);
                        messageDTO.setType(WebMessageTypeEnum.PLATE_NUMBER.getValue());
                        webSocketUtil.sendMessageToWeb(messageDTO);

                        PlateThreeResult plateThreeResult = new PlateThreeResult();
                        plateThreeResult.setPlateNumber(plateNumber);
                        // 图片存储
                        String packagePath = (fileDirPath.endsWith("/") ? fileDirPath : (fileDirPath + "/")) + "PLATE" + "/" + ip + "/" + DateUtil.getDate(DateUtil.TYPE_EIGHT);
                        File pathFile = new File(packagePath);
                        if (!pathFile.exists()) {
                            pathFile.mkdirs();
                        }

                        // 存储车头图片
                        if (pVehImg.nImgDataLen > 0) {
                            String path = packagePath + "/" + plateNumber + "_" + date + ".jpg";
                            saveImage(compressImage(pVehImg.pImgData), path);
                            plateThreeResult.setPath(path.replace((fileDirPath.endsWith("/") ? fileDirPath : (fileDirPath + "/")), ""));
                        }
                        // 存储车牌局部图片
                        if (pPlateImg.nImgDataLen > 0) {
                            String path = packagePath + "/" + plateNumber + "_" + date + "_head.jpg";
                            saveImage(compressImage(pPlateImg.pPlateImgData), path);
                            plateThreeResult.setPlatePath(path.replace((fileDirPath.endsWith("/") ? fileDirPath : (fileDirPath + "/")), ""));
                        }
                        plateThreeResult.setTime(System.currentTimeMillis());
                        CoreFlow.plateThreeResult = plateThreeResult;
                    }

                } catch (Exception exception) {
                    log.error("处理回调函数异常:{}", ExceptionUtil.getStackTrace(exception));
                }
            }
        };
    }

    private static void saveImage(byte[] imageData, String path) {
        if (imageData == null) {
            return;
        }
        File file = new File(path); // 要写入的文件
        try (FileOutputStream fos = new FileOutputStream(path)) {
            fos.write(imageData); // 将byte数组写入文件
        } catch (IOException e) {
            log.error("车牌图片保存异常:{}", ExceptionUtil.getStackTrace(e));
        }
    }

    public static byte[] compressImage(byte[] imageData) {
        if (imageData == null || imageData.length == 0) {
            return null;
        }
        try {
            // 从 byte[] 创建 BufferedInputStream
            ByteArrayInputStream inputStream = new ByteArrayInputStream(imageData);
            BufferedImage originalImage = ImageIO.read(inputStream);
            if (originalImage == null) {
                log.info("图片数据为空");
                return null;
            }

            // 创建空白的 BufferedImage 对象，宽度和高度与原始图片相同
            BufferedImage compressedImage = new BufferedImage(
                    originalImage.getWidth(), originalImage.getHeight(), BufferedImage.TYPE_INT_RGB);

            // 获取 Graphics2D 对象，用于绘制压缩后的图片
            Graphics2D g2d = compressedImage.createGraphics();

            // 设置渲染质量和抗锯齿
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
                    RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING,
                    RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING,
                    RenderingHints.VALUE_ANTIALIAS_ON);

            // 绘制压缩后的图片
            g2d.drawImage(originalImage, 0, 0, null);
            g2d.dispose();

            // 将压缩后的图片保存到 ByteArrayOutputStream
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(compressedImage, "jpg", baos);
            baos.flush();
            byte[] compressedBytes = baos.toByteArray();
            baos.close();
            return compressedBytes;
        } catch (IOException e) {
            e.printStackTrace();
        }

        return null;
    }
}
