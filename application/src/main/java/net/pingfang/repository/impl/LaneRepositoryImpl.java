package net.pingfang.repository.impl;

import net.pingfang.mapper.LaneMapper;
import net.pingfang.model.entity.Lane;
import net.pingfang.repository.LaneRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/26
 */
@Repository
public class LaneRepositoryImpl implements LaneRepository {
    @Autowired
    private LaneMapper laneMapper;

    @Override
    public List<Lane> queryAll() {
        return laneMapper.queryAll();
    }

    @Override
    public void add(Lane lane) {
        laneMapper.add(lane);
    }

    @Override
    public void update(Lane lane) {
        laneMapper.update(lane);
    }

    @Override
    public void delete(Long id) {
        laneMapper.delete(id);
    }

    @Override
    public Lane queryByPlcX(int plcX) {
        return laneMapper.queryByPlcX(plcX);
    }

    @Override
    public Lane queryById(Long id) {
        return laneMapper.queryById(id);
    }
}
