package net.pingfang.repository.impl;

import net.pingfang.mapper.SystemConfigMapper;
import net.pingfang.model.entity.SystemConfig;
import net.pingfang.repository.SystemConfigRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/26
 */
@Repository
public class SystemConfigRepositoryImpl implements SystemConfigRepository {
    @Autowired
    private SystemConfigMapper systemConfigMapper;

    @Override
    public List<SystemConfig> queryAll() {
        return systemConfigMapper.queryAll();
    }

    @Override
    public void updateList(List<SystemConfig> param) {
        systemConfigMapper.updateList(param);
    }
}
