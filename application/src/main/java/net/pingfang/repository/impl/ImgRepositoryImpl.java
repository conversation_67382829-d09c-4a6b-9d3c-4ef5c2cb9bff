package net.pingfang.repository.impl;

import net.pingfang.mapper.ImgMapper;
import net.pingfang.model.entity.Img;
import net.pingfang.repository.ImgRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2023-07-19 13:54
 */
@Repository
public class ImgRepositoryImpl implements ImgRepository {

    @Autowired
    private ImgMapper imgMapper;

    @Override
    public int insertBatch(List<Img> list) {
        if (CollectionUtils.isEmpty(list)){
            return 0;
        }
        return imgMapper.insertBatch(list);
    }

    @Override
    public List<Img> queryByRecordIds(List<Long> ids) {
        return imgMapper.queryByRecordIds(ids);
    }

    @Override
    public List<Img> queryBySeqNo(String seqNo) {
        return imgMapper.queryBySeqNo(seqNo);
    }

    @Override
    public void insert(Img img) {
        imgMapper.insert(img);
    }

    @Override
    public List<Img> queryBySeqNoAndType(String seqNo, Integer type) {
        return imgMapper.queryBySeqNoAndType(seqNo, type);
    }

    @Override
    public List<String> queryUrlByCameraKeyWord(String cameraKeyWord) {
        return imgMapper.queryUrlByCameraKeyWord(cameraKeyWord);
    }
}
