package net.pingfang.repository.impl;

import net.pingfang.mapper.GpsMapper;
import net.pingfang.model.entity.Gps;
import net.pingfang.repository.GpsRepository;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;

/**
 * gps操作GpsRepository
 *
 * @title: GpsRepositoryImpl
 * @author: cb
 * @date: 2024-06-12 16:19
 * @version: 1.0
 */
@Repository
public class GpsRepositoryImpl implements GpsRepository {
    @Resource
    private GpsMapper gpsMapper;

    @Override
    public void add(Gps gps) {
        gpsMapper.add(gps);
    }
}
