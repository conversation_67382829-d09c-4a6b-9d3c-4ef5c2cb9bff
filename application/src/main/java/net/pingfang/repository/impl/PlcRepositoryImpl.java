package net.pingfang.repository.impl;

import net.pingfang.mapper.PlcMapper;
import net.pingfang.model.dto.QueryPlcResDTO;
import net.pingfang.model.dto.UpdatePlcReqDTO;
import net.pingfang.repository.PlcRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @since 2023-07-20 17:27
 */
@Repository
public class PlcRepositoryImpl implements PlcRepository {

    @Autowired
    private PlcMapper plcMapper;

    @Override
    public int updateById(UpdatePlcReqDTO param) {
        return plcMapper.updateById(param);
    }

    @Override
    public QueryPlcResDTO queryDetail() {
        return plcMapper.queryDetail();
    }
}
