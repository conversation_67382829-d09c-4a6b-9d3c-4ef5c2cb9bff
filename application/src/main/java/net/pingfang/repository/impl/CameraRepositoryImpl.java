package net.pingfang.repository.impl;

import net.pingfang.mapper.CameraMapper;
import net.pingfang.model.entity.Camera;
import net.pingfang.repository.CameraRepository;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/26
 */
@Repository
public class CameraRepositoryImpl implements CameraRepository {
    @Autowired
    private CameraMapper cameraMapper;

    @Override
    public void add(Camera camera) {
        cameraMapper.add(camera);
    }

    @Override
    public List<Camera> queryAll() {
        return cameraMapper.queryAll();
    }

    @Override
    public void update(Camera camera) {
        cameraMapper.update(camera);
    }

    @Override
    public void delete(Long id) {
        cameraMapper.delete(id);
    }

    @Override
    public Camera queryById(Long id) {
        return cameraMapper.queryById(id);
    }

    @Override
    public List<Camera> queryByIds(List<Long> ids) {
        if(CollectionUtils.isEmpty(ids)){
            return null;
        }
        return cameraMapper.queryByIds(ids);
    }

    @Override
    public List<Camera> queryByType(Integer type) {
        return cameraMapper.queryByType(type);
    }

}
