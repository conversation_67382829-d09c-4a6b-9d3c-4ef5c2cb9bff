package net.pingfang.repository.impl;

import net.pingfang.mapper.RecognizeConfigMapper;
import net.pingfang.model.dto.QueryRecognizeConfigResDTO;
import net.pingfang.model.entity.RecognizeConfig;
import net.pingfang.model.entity.Record;
import net.pingfang.model.vo.business.RecognizeConfigVO;
import net.pingfang.repository.RecognizeConfigRepository;
import net.pingfang.util.SnowFlakeUtil;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/8/17
 */
@Repository
public class RecognizeConfigRepositoryImpl implements RecognizeConfigRepository {
    @Autowired
    private RecognizeConfigMapper recognizeConfigMapper;

    @Override
    public List<QueryRecognizeConfigResDTO> queryByLaneId(Long laneId) {
        return recognizeConfigMapper.queryByLaneId(laneId);
    }

    @Override
    public void deleteExId(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)){
            return;
        }

        recognizeConfigMapper.deleteExId(idList);
    }

    @Override
    public void deleteById(Long id) {
        if (null == id){
            return;
        }
        recognizeConfigMapper.deleteById(id);
    }

    @Override
    public void addBatch(List<RecognizeConfig> dataList) {
        if (CollectionUtils.isEmpty(dataList)){
            return;
        }

        recognizeConfigMapper.addBatch(dataList);
    }

    @Override
    public void updatePreset(Long laneId, Long cameraId, Integer type) {
        recognizeConfigMapper.updatePreset(laneId, cameraId, type);
    }

    @Override
    public List<RecognizeConfig> queryPresetConfig(Long laneId, Long cameraId) {
        return recognizeConfigMapper.queryPresetConfig(laneId, cameraId);
    }

    @Override
    public List<RecognizeConfigVO> queryWorkConfigByType(Long laneId, Integer type) {
        return recognizeConfigMapper.queryWorkConfigByType(laneId, type);
    }

    @Override
    public void add(RecognizeConfig param) {
        param.setId(SnowFlakeUtil.getDefaultSnowFlakeId());
        recognizeConfigMapper.add(param);
    }

    @Override
    public void updateById(RecognizeConfig param) {
        recognizeConfigMapper.updateById(param);
    }

    @Override
    public Record queryRecordBySeqNo(String seqNo) {
        return recognizeConfigMapper.queryRecordBySeqNo(seqNo);
    }

    @Override
    public RecognizeConfig queryConfig(Long laneId, Long cameraId, Integer type) {
        return recognizeConfigMapper.queryConfig(laneId, cameraId, type);
    }

    @Override
    public List<RecognizeConfigVO> queryAll() {
        return recognizeConfigMapper.queryAll();
    }
}
