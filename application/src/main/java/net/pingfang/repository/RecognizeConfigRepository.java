package net.pingfang.repository;

import net.pingfang.model.dto.QueryRecognizeConfigResDTO;
import net.pingfang.model.entity.RecognizeConfig;
import net.pingfang.model.entity.Record;
import net.pingfang.model.vo.business.RecognizeConfigVO;
import java.util.List;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/8/17
 */
public interface RecognizeConfigRepository {

    List<QueryRecognizeConfigResDTO> queryByLaneId(Long laneId);

    void deleteExId(List<Long> idList);

    void deleteById(Long id);

    void addBatch(List<RecognizeConfig> dataList);

    void updatePreset(Long laneId, Long cameraId, Integer type);

    List<RecognizeConfig> queryPresetConfig(Long laneId, Long cameraId);

    List<RecognizeConfigVO> queryWorkConfigByType(Long laneId, Integer type);

    void add(RecognizeConfig param);

    void updateById(RecognizeConfig param);

    Record queryRecordBySeqNo(String seqNo);

    RecognizeConfig queryConfig(Long laneId, Long cameraId, Integer type);

    List<RecognizeConfigVO> queryAll();

}
