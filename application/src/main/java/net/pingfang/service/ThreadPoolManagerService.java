package net.pingfang.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.util.concurrent.*;
import java.util.function.Supplier;

/**
 * 统一线程池管理服务
 * 负责管理应用中所有线程池的创建、状态检查、任务提交和关闭
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@Service
@Slf4j
public class ThreadPoolManagerService {

    /**
     * RTSP相关线程池 - 固定4个线程用于RTSP抓拍任务
     */
    private final ExecutorService rtspExecutor = Executors.newFixedThreadPool(4, 
            r -> new Thread(r, "RTSP-Pool-" + System.currentTimeMillis() % 1000));

    /**
     * 车顶号识别线程池 - 缓存线程池，根据需要创建线程
     */
    private final ExecutorService truckTopExecutor = Executors.newCachedThreadPool(
            r -> new Thread(r, "TruckTop-Pool-" + System.currentTimeMillis() % 1000));

    /**
     * PLC处理线程池 - 缓存线程池，用于PLC相关异步任务
     */
    private final ExecutorService plcExecutor = Executors.newCachedThreadPool(
            r -> new Thread(r, "PLC-Pool-" + System.currentTimeMillis() % 1000));

    /**
     * 通用异步任务线程池 - 用于其他异步任务
     */
    private final ExecutorService commonExecutor = Executors.newCachedThreadPool(
            r -> new Thread(r, "Common-Pool-" + System.currentTimeMillis() % 1000));

    /**
     * 提交RTSP相关任务
     * 
     * @param task 要执行的任务
     * @param taskDescription 任务描述，用于日志
     * @return Future对象，如果提交失败返回null
     */
    public Future<?> submitRtspTask(Runnable task, String taskDescription) {
        return submitTask(rtspExecutor, task, taskDescription, "RTSP");
    }

    /**
     * 提交车顶号识别任务
     * 
     * @param task 要执行的任务
     * @param taskDescription 任务描述，用于日志
     * @return Future对象，如果提交失败返回null
     */
    public Future<?> submitTruckTopTask(Runnable task, String taskDescription) {
        return submitTask(truckTopExecutor, task, taskDescription, "车顶号识别");
    }

    /**
     * 提交PLC处理任务
     * 
     * @param task 要执行的任务
     * @param taskDescription 任务描述，用于日志
     * @return Future对象，如果提交失败返回null
     */
    public Future<?> submitPlcTask(Runnable task, String taskDescription) {
        return submitTask(plcExecutor, task, taskDescription, "PLC处理");
    }

    /**
     * 提交通用异步任务
     * 
     * @param task 要执行的任务
     * @param taskDescription 任务描述，用于日志
     * @return Future对象，如果提交失败返回null
     */
    public Future<?> submitCommonTask(Runnable task, String taskDescription) {
        return submitTask(commonExecutor, task, taskDescription, "通用异步");
    }

    /**
     * 提交带返回值的RTSP任务
     * 
     * @param task 要执行的任务
     * @param taskDescription 任务描述，用于日志
     * @param <T> 返回值类型
     * @return Future对象，如果提交失败返回null
     */
    public <T> Future<T> submitRtspTask(Callable<T> task, String taskDescription) {
        return submitTask(rtspExecutor, task, taskDescription, "RTSP");
    }

    /**
     * 提交带返回值的车顶号识别任务
     * 
     * @param task 要执行的任务
     * @param taskDescription 任务描述，用于日志
     * @param <T> 返回值类型
     * @return Future对象，如果提交失败返回null
     */
    public <T> Future<T> submitTruckTopTask(Callable<T> task, String taskDescription) {
        return submitTask(truckTopExecutor, task, taskDescription, "车顶号识别");
    }

    /**
     * 统一的任务提交方法（Runnable版本）
     * 
     * @param executor 线程池
     * @param task 要执行的任务
     * @param taskDescription 任务描述
     * @param poolType 线程池类型
     * @return Future对象，如果提交失败返回null
     */
    private Future<?> submitTask(ExecutorService executor, Runnable task, String taskDescription, String poolType) {
        if (executor == null) {
            log.error("❌ {}线程池为null，无法提交任务: {}", poolType, taskDescription);
            return null;
        }

        if (executor.isShutdown() || executor.isTerminated()) {
            log.error("❌ {}线程池已关闭，无法提交任务: {}", poolType, taskDescription);
            return null;
        }

        try {
            log.debug("📋 提交{}任务: {}", poolType, taskDescription);
            Future<?> future = executor.submit(() -> {
                try {
                    log.debug("🚀 {}任务开始执行: {} - 线程: {}", poolType, taskDescription, Thread.currentThread().getName());
                    task.run();
                    log.debug("✅ {}任务执行完成: {}", poolType, taskDescription);
                } catch (Exception e) {
                    log.error("❌ {}任务执行异常: {} - 错误: {}", poolType, taskDescription, e.getMessage(), e);
                }
            });
            log.debug("✅ {}任务提交成功: {}", poolType, taskDescription);
            return future;
        } catch (RejectedExecutionException e) {
            log.error("❌ {}任务提交被拒绝: {} - 错误: {}", poolType, taskDescription, e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("❌ {}任务提交失败: {} - 错误: {}", poolType, taskDescription, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 统一的任务提交方法（Callable版本）
     * 
     * @param executor 线程池
     * @param task 要执行的任务
     * @param taskDescription 任务描述
     * @param poolType 线程池类型
     * @param <T> 返回值类型
     * @return Future对象，如果提交失败返回null
     */
    private <T> Future<T> submitTask(ExecutorService executor, Callable<T> task, String taskDescription, String poolType) {
        if (executor == null) {
            log.error("❌ {}线程池为null，无法提交任务: {}", poolType, taskDescription);
            return null;
        }

        if (executor.isShutdown() || executor.isTerminated()) {
            log.error("❌ {}线程池已关闭，无法提交任务: {}", poolType, taskDescription);
            return null;
        }

        try {
            log.debug("📋 提交{}任务: {}", poolType, taskDescription);
            Future<T> future = executor.submit(() -> {
                try {
                    log.debug("🚀 {}任务开始执行: {} - 线程: {}", poolType, taskDescription, Thread.currentThread().getName());
                    T result = task.call();
                    log.debug("✅ {}任务执行完成: {}", poolType, taskDescription);
                    return result;
                } catch (Exception e) {
                    log.error("❌ {}任务执行异常: {} - 错误: {}", poolType, taskDescription, e.getMessage(), e);
                    throw new RuntimeException(e);
                }
            });
            log.debug("✅ {}任务提交成功: {}", poolType, taskDescription);
            return future;
        } catch (RejectedExecutionException e) {
            log.error("❌ {}任务提交被拒绝: {} - 错误: {}", poolType, taskDescription, e.getMessage());
            return null;
        } catch (Exception e) {
            log.error("❌ {}任务提交失败: {} - 错误: {}", poolType, taskDescription, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查RTSP线程池状态
     * 
     * @return 线程池状态信息
     */
    public String getRtspPoolStatus() {
        return getPoolStatus(rtspExecutor, "RTSP");
    }

    /**
     * 检查车顶号识别线程池状态
     * 
     * @return 线程池状态信息
     */
    public String getTruckTopPoolStatus() {
        return getPoolStatus(truckTopExecutor, "车顶号识别");
    }

    /**
     * 检查PLC处理线程池状态
     * 
     * @return 线程池状态信息
     */
    public String getPlcPoolStatus() {
        return getPoolStatus(plcExecutor, "PLC处理");
    }

    /**
     * 获取线程池状态信息
     * 
     * @param executor 线程池
     * @param poolType 线程池类型
     * @return 状态信息字符串
     */
    private String getPoolStatus(ExecutorService executor, String poolType) {
        if (executor == null) {
            return String.format("%s线程池: null", poolType);
        }

        boolean isShutdown = executor.isShutdown();
        boolean isTerminated = executor.isTerminated();

        String status;
        if (isTerminated) {
            status = "已终止";
        } else if (isShutdown) {
            status = "正在关闭";
        } else {
            status = "正常运行";
        }

        return String.format("%s线程池: %s (isShutdown=%s, isTerminated=%s)", 
                poolType, status, isShutdown, isTerminated);
    }

    /**
     * 获取所有线程池状态
     * 
     * @return 所有线程池状态信息
     */
    public String getAllPoolStatus() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 线程池状态汇总 ===\n");
        sb.append(getRtspPoolStatus()).append("\n");
        sb.append(getTruckTopPoolStatus()).append("\n");
        sb.append(getPlcPoolStatus()).append("\n");
        sb.append(getPoolStatus(commonExecutor, "通用异步")).append("\n");
        sb.append("=====================");
        return sb.toString();
    }

    /**
     * Spring Bean销毁时调用，确保所有线程池正确关闭
     */
    @PreDestroy
    public void destroy() {
        log.info("ThreadPoolManagerService正在销毁，关闭所有线程池...");

        // 关闭所有线程池
        shutdownExecutor(rtspExecutor, "RTSP");
        shutdownExecutor(truckTopExecutor, "车顶号识别");
        shutdownExecutor(plcExecutor, "PLC处理");
        shutdownExecutor(commonExecutor, "通用异步");

        log.info("ThreadPoolManagerService销毁完成");
    }

    /**
     * 优雅关闭线程池
     * 
     * @param executor 要关闭的线程池
     * @param poolType 线程池类型
     */
    private void shutdownExecutor(ExecutorService executor, String poolType) {
        if (executor == null || executor.isShutdown()) {
            log.debug("{}线程池已关闭或为null，跳过关闭操作", poolType);
            return;
        }

        log.info("关闭{}线程池...", poolType);
        executor.shutdown();

        try {
            // 等待5秒让正在执行的任务完成
            if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                log.warn("{}线程池未在5秒内完成，强制关闭...", poolType);
                executor.shutdownNow();
                // 再等待2秒确保强制关闭完成
                if (!executor.awaitTermination(2, TimeUnit.SECONDS)) {
                    log.error("{}线程池强制关闭失败", poolType);
                } else {
                    log.info("{}线程池已强制关闭", poolType);
                }
            } else {
                log.info("{}线程池已成功关闭", poolType);
            }
        } catch (InterruptedException e) {
            log.warn("等待{}线程池关闭时被中断，强制关闭...", poolType);
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
