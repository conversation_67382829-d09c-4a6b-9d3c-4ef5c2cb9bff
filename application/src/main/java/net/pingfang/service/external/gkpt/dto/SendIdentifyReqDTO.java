package net.pingfang.service.external.gkpt.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

/**
 * 识别数据同步接口
 * 
 * <AUTHOR>
 * @since 2024-10-23 16:53
 */
@Data
public class SendIdentifyReqDTO {

    /**
     * 唯一编号
     */
    private String seqNo;
    /**
     * 车号
     */
    private String truckNo;
    /**
     * 车号标记
     * 
     * 0表示应无集卡车号，1表示应有集卡车号
     */
    private String truckNoFlag;

    /**
     * 上锁车道
     */
    private String lane;

    /**
     * 门吊编号
     */
    private String craneNum;

    /**
     * 火车车号
     */
    private String trainNum;

    /**
     * 箱号
     */
    private String ctnNo;

    /**
     * 箱号ISO
     */
    private String iso;

    /**
     * 作业时间 (yyyy-MM-dd HH:mm:ss)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime passTime;

    /**
     * 上锁时间 (yyyy-MM-dd HH:mm:ss)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lockTime;
    
    /**
     * 解锁时间 (yyyy-MM-dd HH:mm:ss)
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime unLockTime;

    
}
