package net.pingfang.service.external.gkpt.dto;

import lombok.Data;

/**
 * 识别信息返回结果
 *
 * @title: ResultIdentifyInfo
 * @author: cb
 * @date: 2025-06-03 11:34
 * @version: 1.0
 */
@Data
public class ResultIdentifyInfo {
    /**
     * 返回码
     */
    private String code;
    
    /**
     * 返回消息
     */
    private String message;
    
    /**
     * 返回数据
     */
    private Object data;
    
    @Override
    public String toString() {
        return "ResultIdentifyInfo{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
