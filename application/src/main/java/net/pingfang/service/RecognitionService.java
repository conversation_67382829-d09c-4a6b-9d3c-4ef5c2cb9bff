package net.pingfang.service;

import net.pingfang.model.common.Result;
import net.pingfang.model.dto.*;
import net.pingfang.model.entity.Img;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
public interface RecognitionService {

    Result<String> manualRecognize(ManualRecognizeDTO param);

    Result<QueryRecordResDTO> queryRecord(QueryRecordReqDTO param);

    Result<String> manualUpload(ManualUploadReqDTO param);

    void download(DownloadReqDTO param,HttpServletResponse response);

    void export(QueryRecordReqDTO param, HttpServletResponse response);

    Result<List<Img>> queryImg(String seqNo);

    Result<String> manualRecognizeCtn(Long cameraId);

    Result<String> manualRecognizeTop(Long cameraId);

    Result<Integer> copyImagesByCameraKeyWordToAbstractPath(String cameraKeyWord, String abstractPath);
}
