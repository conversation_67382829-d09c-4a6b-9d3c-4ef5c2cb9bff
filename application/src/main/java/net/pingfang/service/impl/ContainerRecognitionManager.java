package net.pingfang.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.core.flow.CoreFlow;
import net.pingfang.util.RedisUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

/**
 * 容器识别管理器 - 专注于车牌识别与容器识别的协同工作
 * 注意：高度差触发识别功能已在CoreFlow和PlcCoreService中实现，此类不重复实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class ContainerRecognitionManager {

    @Autowired
    private CoreFlow coreFlow;

    @Autowired
    private LicensePlateRecognitionManager licensePlateManager;

    @Autowired
    private RedisUtil redisUtil;

    @Value("${container-recognition.enabled:true}")
    private Boolean enabled;

    @Value("${container-recognition.plate-integration-enabled:true}")
    private Boolean plateIntegrationEnabled;

    /**
     * 初始化容器识别管理器
     */
    @PostConstruct
    public void init() {
        if (!enabled) {
            log.info("容器识别管理器已禁用");
            return;
        }

        log.info("容器识别管理器初始化完成 - 车牌集成: {}", plateIntegrationEnabled);
    }

    /**
     * 处理上锁流程 - 启动车牌识别与容器识别的协同
     *
     * @param seqNo 序列号
     */
    public void handleLockFlow(String seqNo) {
        if (!enabled || !plateIntegrationEnabled) {
            return;
        }

        try {
            log.info("容器识别管理器 - 处理上锁流程 - seqNo: {}", seqNo);

            // 启动CoreFlow流程状态管理
            if (licensePlateManager != null) {
                licensePlateManager.startCoreFlowProcess(seqNo);
            }

        } catch (Exception e) {
            log.error("处理上锁流程异常 - seqNo: {}, 错误: {}", seqNo, e.getMessage(), e);
        }
    }

    /**
     * 处理解锁流程 - 完成车牌识别与容器识别的协同
     *
     * @param seqNo 序列号
     */
    public void handleUnlockFlow(String seqNo) {
        if (!enabled || !plateIntegrationEnabled) {
            return;
        }

        try {
            log.info("容器识别管理器 - 处理解锁流程 - seqNo: {}", seqNo);

            // 完成CoreFlow流程
            if (licensePlateManager != null) {
                licensePlateManager.endCoreFlowProcess(seqNo);
            }

            // 清理识别状态
            clearRecognitionStatus(seqNo);

        } catch (Exception e) {
            log.error("处理解锁流程异常 - seqNo: {}, 错误: {}", seqNo, e.getMessage(), e);
        }
    }

    /**
     * 清理识别状态
     */
    private void clearRecognitionStatus(String seqNo) {
        try {
            redisUtil.delete("PROCESS_STATUS:" + seqNo);
            log.debug("清理识别状态 - seqNo: {}", seqNo);
        } catch (Exception e) {
            log.error("清理识别状态异常 - seqNo: {}, 错误: {}", seqNo, e.getMessage());
        }
    }

    /**
     * 获取当前连接的设备数量（用于监控）
     */
    public int getActiveRecognitionCount() {
        return coreFlow.checkIsProcessing() ? 1 : 0;
    }
}
