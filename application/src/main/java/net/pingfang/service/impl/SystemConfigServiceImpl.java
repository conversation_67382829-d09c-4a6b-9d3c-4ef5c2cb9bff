package net.pingfang.service.impl;

import net.pingfang.enums.ContainerTypeEnum;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.SystemInfoDTO;
import net.pingfang.model.dto.SystemInfoSimpleDTO;
import net.pingfang.model.entity.SystemConfig;
import net.pingfang.repository.RecognitionRepository;
import net.pingfang.repository.SystemConfigRepository;
import net.pingfang.service.SystemConfigService;
import net.pingfang.util.DateUtils;
import net.pingfang.util.IpUtils;
import java.text.NumberFormat;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
@Service
@Slf4j
public class SystemConfigServiceImpl implements SystemConfigService {
    @Autowired
    private SystemConfigRepository systemConfigRepository;

    @Autowired
    private RecognitionRepository recognitionRepository;

    @Override
    public Result<List<SystemConfig>> queryAll() {
        List<SystemConfig> result = systemConfigRepository.queryAll();
        return Result.success(result);
    }

    @Override
    public Result<String> update(List<SystemConfig> param) {

        systemConfigRepository.updateList(param);

        return Result.success();
    }

    @Override
    public Result<SystemInfoDTO> querySystemInfo() {
        SystemInfoDTO result = new SystemInfoDTO();
        // 设置总记录数
        Integer ctnCount = recognitionRepository.queryTotal();
        result.setTotalRecordCount(ctnCount);
        // 设置短箱数量
        Integer shortCtnCount = recognitionRepository.queryCtnTotalByType(ContainerTypeEnum.SS.getValue(),ContainerTypeEnum.TD.getValue());
        result.setShortCtnCount(shortCtnCount);
        // 设置长箱数量
        Integer longCtnCount = recognitionRepository.queryCtnTotalByType(ContainerTypeEnum.SL.getValue());
        result.setLongCtnCount(longCtnCount);
        // 设置其他箱类型
        Integer otherCtnCount = recognitionRepository.queryCtnTotalByType(ContainerTypeEnum.T.getValue(),
                ContainerTypeEnum.INVERSE_MIX.getValue(),
                ContainerTypeEnum.ALONG_MIX.getValue(),
                ContainerTypeEnum.TT.getValue(),
                ContainerTypeEnum.UNKNOWN.getValue());
        result.setOtherCtnCount(otherCtnCount);
        // 识别平均耗时
        Integer avgRecoTimeConsuming  = recognitionRepository.queryRecoAvgTimeConsuming();
        result.setRecognizeAvgTime(avgRecoTimeConsuming);

        // 设置抓拍平均耗时
        Integer avgSnapTimeConsuming  = recognitionRepository.querySnapAvgTimeConsuming();
        result.setSnapAvgTime(avgSnapTimeConsuming);

        // 查询所有校验情况
        // 查询所有箱数量
        Integer ctnTotal = recognitionRepository.queryCtnTotal();
        if (ctnTotal!= null && ctnTotal!=0){
            // 查询箱号校验成功的数量
            int ctnCheckSuccessTotal =recognitionRepository.queryCtnCheckSuccess();

            // 查询车号校验成功的数量
            int plateNumberCheckSuccessTotal =recognitionRepository.queryPlateNumberCheckSuccess();

            NumberFormat nt = NumberFormat.getPercentInstance();//获取格式化对象
            nt.setMinimumFractionDigits(2);//设置百分数精确度2即保留两位小数
            result.setCtnNoCheckRate(nt.format(ctnCheckSuccessTotal/1.0D/ctnTotal));
            result.setPlateNumberCheckRate(nt.format(plateNumberCheckSuccessTotal/1.0D/ctnTotal));
        }

        return Result.success(result);
    }

    @Override
    public Result<SystemInfoSimpleDTO> querySimpleSystemInfo() {
        SystemInfoSimpleDTO result = new SystemInfoSimpleDTO();
        // 设置服务器ip
        result.setIp(IpUtils.getHostIp());
        // 设置系统运行时间
        result.setWorkTime(DateUtils.getDatePoor(DateUtils.getNowDate(), DateUtils.getServerStartDate()));

        return Result.success(result);
    }
}
