package net.pingfang.service.impl;

import java.util.List;
import lombok.extern.slf4j.Slf4j;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.AddCommandReqDTO;
import net.pingfang.service.SupplierService;
import net.pingfang.util.JsonUtil;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024-10-23 17:19
 */
@Slf4j
@Service
public class SupplierServiceImpl implements SupplierService {
    @Override
    public void addCommand(List<AddCommandReqDTO> reqDTOList) {
        log.info("接收三方指令数据：{}", JsonUtil.toJson(reqDTOList));
    }
}
