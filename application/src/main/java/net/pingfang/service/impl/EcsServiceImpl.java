package net.pingfang.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.EcsIdentifyInfoDTO;
import net.pingfang.service.EcsService;
import net.pingfang.util.JsonUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;

/**
 * ECS系统对接服务实现
 */
@Service
@Slf4j
public class EcsServiceImpl implements EcsService {

    @Resource
    private RestTemplate restTemplate;

    @Value("${ecs.api.url:http://10.66.230.101:8882}")
    private String ecsApiUrl;

    @Value("${ecs.api.identify.path:/ocrCrane/receiveIdentifyInfo}")
    private String ecsIdentifyPath;

    @Value("${ecs.api.auth:Basic b2NyQ3JhbmU6MTIzNDU2}")
    private String ecsApiAuth;

    @Override
    public Result<String> sendIdentifyInfo(EcsIdentifyInfoDTO identifyInfo) {
        try {
            String url = ecsApiUrl + ecsIdentifyPath;
            log.info("向ECS推送识别信息，URL: {}, 数据: {}", url, JsonUtil.toJson(identifyInfo));

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            // 添加固定的Authorization请求头
            headers.add("Authorization", ecsApiAuth);

            HttpEntity<EcsIdentifyInfoDTO> requestEntity = new HttpEntity<>(identifyInfo, headers);
            ResponseEntity<String> responseEntity = restTemplate.postForEntity(url, requestEntity, String.class);

            log.info("ECS推送结果: {}", responseEntity.getBody());

            if (responseEntity.getStatusCode().is2xxSuccessful()) {
                return Result.success("推送成功");
            } else {
                return Result.fail("推送失败，状态码: " + responseEntity.getStatusCodeValue());
            }
        } catch (Exception e) {
            log.error("向ECS推送识别信息异常: {}", e.getMessage(), e);
            return Result.fail("推送异常: " + e.getMessage());
        }
    }
}
