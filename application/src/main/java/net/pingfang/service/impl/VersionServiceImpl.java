package net.pingfang.service.impl;

import com.alibaba.fastjson.JSONObject;
import java.math.BigDecimal;
import net.pingfang.config.ProjectConfig;
import net.pingfang.core.DllFactory;
import net.pingfang.core.algorithm.ailib.Container;
import net.pingfang.core.algorithm.ailib.model.TagVersion;
import net.pingfang.enums.TrueFalseEnum;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.QueryRecordSyncReqDTO;
import net.pingfang.model.entity.Version;
import net.pingfang.model.vo.tos.TosMessageVO;
import net.pingfang.model.vo.tos.TosRecordVO;
import net.pingfang.repository.RecognitionRepository;
import net.pingfang.service.VersionService;
import net.pingfang.util.DateUtils;
import net.pingfang.util.ExternalConfigUtil;
import net.pingfang.util.MD5Utils;
import net.pingfang.util.RestTemplateUtil;
import net.pingfang.util.SplitUtil;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
@Service
@Slf4j
public class VersionServiceImpl implements VersionService {
    @Autowired
    private DllFactory dllFactory;

    @Autowired
    private ProjectConfig projectConfig;

    @Autowired
    private ExternalConfigUtil externalConfigUtil;
    @Autowired
    private RecognitionRepository recognitionRepository;
    @Autowired
    private RestTemplateUtil restTemplateUtil;

    @Value("${gps.url}")
    public String gpsUrl;
    @Value("${gps.imei}")
    private String imei;

    @Override
    public Result<List<Version>> query() {
        List<Version> versionList = new ArrayList<>();

        Container ocrContainerSdk = dllFactory.getOcrContainerSdk();
        TagVersion tagVersion = new TagVersion();
        int result = ocrContainerSdk.GetAlgVersion(tagVersion);
        log.info("算法版本查询接口调用结果:{}", result);

        Version version = new Version();
        version.setName("箱号识别算法");
        version.setVersion(tagVersion.getVersionID() == null?"未获取到版本信息":new String(tagVersion.getVersionID(), StandardCharsets.UTF_8).trim());
        versionList.add(version);

        version = new Version();
        version.setName("应用控制软件版本");
        version.setVersion(projectConfig.getVersion());
        versionList.add(version);
        return Result.success(versionList);
    }

    @Override
    public void test(String seqNo){
//        String gpsData = "123,456";
        if(externalConfigUtil.getSync().equals(TrueFalseEnum.YES.getDescExtOne())){
            //查询识别数据
            QueryRecordSyncReqDTO recordSync = recognitionRepository.querySyncBySeqNo(seqNo);

            //GPS（数据格式：经度,纬度） 数据拆分
            Map<String,Object> gpsMap = new HashMap<>();
            gpsMap.put("gpsUniqueCode",imei);
            ParameterizedTypeReference<Result<String>> responseType = new ParameterizedTypeReference<Result<String>>() {};
            Result resultGps = restTemplateUtil.get(gpsUrl,imei);
            String gpsData = resultGps.getMsg();
            log.info("同步第三方接口GPS：{}",gpsData);
            BigDecimal lon = BigDecimal.ZERO;
            BigDecimal lat = BigDecimal.ZERO;
            List<String> gpsList = SplitUtil.stringToList(gpsData);
            if (!CollectionUtils.isEmpty(gpsList)) {
                //保留小数点
                BigDecimal lonOriginal = new BigDecimal(gpsList.get(0));
                BigDecimal latOriginal = new BigDecimal(gpsList.get(0));
                lon = lonOriginal.divide(BigDecimal.valueOf(1000000));
                lat = latOriginal.divide(BigDecimal.valueOf(1000000));
            }
            //GPS（数据格式：经度,纬度） 数据拆分
//            List<String> gpsList = SplitUtil.stringToList(gpsData);
//            String lon = !CollectionUtils.isEmpty(gpsList) ? gpsList.get(0) : "";
//            String lat = !CollectionUtils.isEmpty(gpsList) ? gpsList.get(1) : "";
            //识别数据
            TosRecordVO tosRecordVO = new TosRecordVO();
            tosRecordVO.setCntr(recordSync.getCtnNoA());
            tosRecordVO.setTruck(recordSync.getPlateNumberA());
            tosRecordVO.setTruckNo(recordSync.getTopPlateA());
            //经纬度、x,y 数据、龙门吊编号
            tosRecordVO.setLon(lon.toString());
            tosRecordVO.setLat(lat.toString());
            tosRecordVO.setHeight(0.0f);
            tosRecordVO.setLenght(0.0f);
            tosRecordVO.setMach("1301");
            //数据整合
            TosMessageVO tosMessageVO = new TosMessageVO();
            tosMessageVO.setUn(externalConfigUtil.getUser());
            tosMessageVO.setPsd(MD5Utils.encryptMD5(externalConfigUtil.getPassword()));
            tosMessageVO.setTimestamp(DateUtils.generate12DigitTimeStamp());
            tosMessageVO.setReqbody(tosRecordVO);
            //调用第三方接口
            String result = restTemplateUtil.post(externalConfigUtil.getUrl(),tosMessageVO);
            log.info("同步第三方接口出参：[{}];入参：[{}]",result, JSONObject.toJSONString(tosMessageVO));
        }else{
            log.info("同步第三方接口开关未打开,不进行同步操作");
        }
    }
}
