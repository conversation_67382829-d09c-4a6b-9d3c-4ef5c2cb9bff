package net.pingfang.service.impl;

import com.sun.jna.Memory;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import lombok.extern.slf4j.Slf4j;
import net.pingfang.config.LicensePlateRecognitionConfig;
import net.pingfang.model.dto.EcsIdentifyInfoDTO;
import net.pingfang.service.EcsService;
import net.pingfang.util.PathConfigUtil;
import net.pingfang.util.RedisUtil;
import net.sdk.bean.basicconfig.reportmess.Data_E_ReportMess;
import net.sdk.bean.basicconfig.reportmess.Data_T_IOStateRsp;
import net.sdk.bean.serviceconfig.imagesnap.Data_T_DCImageSnap;
import net.sdk.bean.serviceconfig.platedevice.Data_T_ControlGate;
import net.sdk.bean.serviceconfig.platedevice.Data_T_RS485Data;
import net.sdk.function.main.NET;
import net.sdk.function.systemcommon.control.message.callback.Callback_FGetReportCBEx;
import net.sdk.function.systemcommon.control.message.callback.Callback_NET_CONTROLCALLBACKEx;
import net.sdk.function.systemcommon.imagesnap.callback.Callback_FGetImageCBEx;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 车牌识别管理器 - 整合了设备连接和车牌识别功能
 * <AUTHOR>
 */
@Slf4j
@Component
public class LicensePlateRecognitionManager implements CommandLineRunner {

    //init NET
    private static final NET net = NET.Singleton.getInstance();
    /**
     * 设备连接集合
     * key: ip，使用相机IP作为KEY,也可以用SN或HANDLE作为KEY
     * value: CameraDevice
     */
    private static final Map<String/*ip*/,CameraDevice> deviceConnectionMap = new ConcurrentHashMap<>();

    private static Timer timer = new Timer();

    @Autowired
    private LicensePlateRecognitionConfig licensePlateConfig;

    @Autowired
    private EcsService ecsService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private PathConfigUtil pathConfigUtil;

    // 静态引用，用于回调函数中访问
    private static EcsService staticEcsService;
    private static LicensePlateRecognitionConfig staticConfig;
    private static RedisUtil staticRedisUtil;
    private static PathConfigUtil staticPathConfigUtil;

    /**
     * 初始化静态引用
     */
    @PostConstruct
    public void init() {
        staticEcsService = this.ecsService;
        staticConfig = this.licensePlateConfig;
        staticRedisUtil = this.redisUtil;
        staticPathConfigUtil = this.pathConfigUtil;
    }

    /**
     * 内部类：摄像头设备连接管理
     */
    public static class CameraDevice {
        private final String ip;
        private String sn;
        private Integer handle = null;
        private Pointer regImageRecvExCallbackUserDataPointer = null;
        private Pointer setControlCallBackUserDataPointer = null;
        private Pointer regReportMessExCallBackUserDataPointer = null;

        public CameraDevice(String ip) {
            this.ip = ip;
        }

        /**
         * 连接设备
         * @param port 连接端口号
         * @param timeoutSeconds 连接超时时间，单位：秒
         * @return handle >= 0 表示连接成功，否则连接失败
         */
        public int connect(short port, short timeoutSeconds) {
            NET net = NET.Singleton.getInstance();
            handle = net.Net_AddCamera(ip);
            if (handle >= 0) {
                int rtn = net.Net_ConnCamera(handle, port, timeoutSeconds);
                if (rtn != 0) {
                    System.err.println("[" + ip + "] conn result = " + rtn);
                    //断开与相机之间的信令链路，释放图片接收资源
                    net.Net_DisConnCamera(handle);
                    //连接未成功，释放handle
                    net.Net_DelCamera(handle);
                    handle = -1;
                }
            } else {
                System.err.println("[" + ip + "] add camera handle = " + handle);
            }
            return handle;
        }

        /**
         * 连接设备（使用缺省端口号）
         * @return handle >= 0 表示连接成功，否则连接失败
         */
        public int connect() {
            return connect((short) 30000, (short) 5);
        }

        /**
         * 关闭设备连接
         */
        public void close() {
            if (handle != null && handle >= 0) {
                NET net = NET.Singleton.getInstance();
                //断开与相机之间的信令链路，释放图片接收资源
                net.Net_DisConnCamera(handle);
                //断开与相机之间的连接，释放相机管理项
                net.Net_DelCamera(handle);
            }

            if (this.regImageRecvExCallbackUserDataPointer != null) {
                long peer = Pointer.nativeValue(this.regImageRecvExCallbackUserDataPointer);
                Native.free(peer);
                Pointer.nativeValue(this.regImageRecvExCallbackUserDataPointer, 0);
            }

            if (this.setControlCallBackUserDataPointer != null) {
                long peer = Pointer.nativeValue(this.setControlCallBackUserDataPointer);
                Native.free(peer);
                Pointer.nativeValue(this.setControlCallBackUserDataPointer, 0);
            }

            if (this.regReportMessExCallBackUserDataPointer != null) {
                long peer = Pointer.nativeValue(this.regReportMessExCallBackUserDataPointer);
                Native.free(peer);
                Pointer.nativeValue(this.regReportMessExCallBackUserDataPointer, 0);
            }

            System.out.println("[" + ip + "] closed");
        }

        /**
         * 启用车牌加密，如果加密，则在注册车牌识别回调函数时，需要传入车牌解密密码，否则无法解密车牌号码
         * @param pwd ,初始(缺省）密码为"000000"
         * @return 状态码0表示成功，其他值详情见E_ReturnCode
         */
        public int enableEnc(String pwd) {
            NET net = NET.Singleton.getInstance();
            return net.Net_EnableEnc(handle, (byte) 0x01, pwd);
        }

        /**
         * 关闭车牌加密
         * @param pwd 加密密码
         * @return 状态码0表示成功，其他值详情见E_ReturnCode
         */
        public int disableEnc(String pwd) {
            NET net = NET.Singleton.getInstance();
            return net.Net_EnableEnc(handle, (byte) 0x00, pwd);
        }

        /**
         * 修改车牌加密密码
         * @param oldPwd 旧密码
         * @param newPwd 新密码
         * @return 状态码0表示成功，其他值详情见E_ReturnCode
         */
        public int modifyEncPwd(String oldPwd, String newPwd) {
            NET net = NET.Singleton.getInstance();
            return net.Net_ModifyEncPwd(handle, oldPwd, newPwd);
        }

        /**
         * 注册车牌实时识别回调函数
         * @param decPwd 车牌解密密码，如果车牌加密，则需要传入解密密码，否则传入null
         * @param callbackUserData 需要在回调函数中返回的Pointer参数内容
         * @param callback
         * @return
         */
        public int regImageRecvEx(String decPwd, String callbackUserData, Callback_FGetImageCBEx.FGetImageCBEx callback) {
            if (callbackUserData != null && !callbackUserData.isEmpty()) {
                if (this.regImageRecvExCallbackUserDataPointer != null) {
                    long peer = Pointer.nativeValue(this.regImageRecvExCallbackUserDataPointer);
                    Native.free(peer);
                    Pointer.nativeValue(this.regImageRecvExCallbackUserDataPointer, 0);
                }
                byte[] jsonBytes = callbackUserData.getBytes(StandardCharsets.UTF_8);
                //加 1 是为了存储字符串结束符 '\0'
                this.regImageRecvExCallbackUserDataPointer = new Memory(jsonBytes.length + 1);
                this.regImageRecvExCallbackUserDataPointer.setString(0, callbackUserData, "UTF-8");
            }

            NET net = NET.Singleton.getInstance();
            if (decPwd != null) {
                net.Net_SetDecPwd(handle, decPwd);
            }
            return net.Net_RegImageRecvEx(handle, callback, (this.regImageRecvExCallbackUserDataPointer == null ? Pointer.NULL : this.regImageRecvExCallbackUserDataPointer));
        }

        /**
         * 注册设备状态变更（上线/下线）回调函数
         * @param callbackUserData 需要在回调函数中返回的Pointer参数内容
         * @param callback
         * @return
         */
        public int setControlCallBackEx(String callbackUserData, Callback_NET_CONTROLCALLBACKEx.NET_CONTROLCALLBACKEx callback) {
            if (callbackUserData != null && !callbackUserData.isEmpty()) {
                if (this.setControlCallBackUserDataPointer != null) {
                    long peer = Pointer.nativeValue(this.setControlCallBackUserDataPointer);
                    Native.free(peer);
                    Pointer.nativeValue(this.setControlCallBackUserDataPointer, 0);
                }
                byte[] jsonBytes = callbackUserData.getBytes(StandardCharsets.UTF_8);
                //加 1 是为了存储字符串结束符 '\0'
                this.setControlCallBackUserDataPointer = new Memory(jsonBytes.length + 1);
                this.setControlCallBackUserDataPointer.setString(0, callbackUserData, "UTF-8");
            }

            NET net = NET.Singleton.getInstance();
            return net.Net_SetControlCallBackEx(handle, callback, (this.setControlCallBackUserDataPointer == null ? Pointer.NULL : this.setControlCallBackUserDataPointer));
        }

        /**
         * 通过注册回调函数，获取上报的报警消息
         * @param callbackUserData 需要在回调函数中返回的Pointer参数内容
         * @param callback
         * @return
         */
        public int regReportMessEx(String callbackUserData, Callback_FGetReportCBEx.FGetReportCBEx callback) {
            if (callbackUserData != null && !callbackUserData.isEmpty()) {
                if (this.regReportMessExCallBackUserDataPointer != null) {
                    long peer = Pointer.nativeValue(this.regReportMessExCallBackUserDataPointer);
                    Native.free(peer);
                    Pointer.nativeValue(this.regReportMessExCallBackUserDataPointer, 0);
                }
                byte[] jsonBytes = callbackUserData.getBytes(StandardCharsets.UTF_8);
                //加 1 是为了存储字符串结束符 '\0'
                this.regReportMessExCallBackUserDataPointer = new Memory(jsonBytes.length + 1);
                this.regReportMessExCallBackUserDataPointer.setString(0, callbackUserData, "UTF-8");
            }

            NET net = NET.Singleton.getInstance();
            return net.Net_RegReportMessEx(handle, callback, (this.regReportMessExCallBackUserDataPointer == null ? Pointer.NULL : this.regReportMessExCallBackUserDataPointer));
        }

        /**
         * 通过相机透传RS485十六进制数据，转换成字节后最大为1024个字节
         * @param rs485Id  RS485接口ID,从0开始编号，0对应相机丝印的A1 B1
         * @param hexStr 16进制字符串，如"03AED47846" 中间不需要任何分隔符
         * @return 状态码0表示成功，其他值详情见E_ReturnCode
         */
        public int sendRS485Data(byte rs485Id, String hexStr) {
            hexStr = hexStr.trim().replaceAll(" ", "");
            hexStr = hexStr.replaceAll(":", "");
            int len = hexStr.length();
            if (len % 2 != 0) {
                throw new IllegalArgumentException("hex string length must be even number :" + hexStr);
            }

            byte[] hex = this.hexToByteArray(hexStr);
            return this.sendRS485Data(rs485Id, hex);
        }

        /**
         * 通过相机透传RS485二进制数据，最大1024个字节
         * @param rs485Id RS485接口ID,从0开始编号，0对应相机丝印的A1 B1
         * @param hex 二进制数据
         * @return 状态码0表示成功，其他值详情见E_ReturnCode
         */
        public int sendRS485Data(byte rs485Id, byte[] hex) {
            int len = hex.length;
            if (len > 1024) {
                throw new IllegalArgumentException("The byte length must be <= 1024 , but current length is " + len);
            }
            Data_T_RS485Data.T_RS485Data.ByReference rs485 = new Data_T_RS485Data.T_RS485Data.ByReference();
            rs485.rs485Id = rs485Id;
            rs485.dataLen = (byte) len;
            System.arraycopy(hex, 0, rs485.data, 0, len);

            NET net = NET.Singleton.getInstance();
            return net.Net_SendRS485Data(handle, rs485);
        }

        /**
         * 下发强制抓拍图片命令，通知相机抓拍一张图片
         * 需要使用Net_RegImageRecv注册的回调函数获取图片信息；
         * @return 状态码0表示成功，其他值详情见E_ReturnCode
         */
        public int imageSnap() {
            NET net = NET.Singleton.getInstance();
            Data_T_DCImageSnap.T_DCImageSnap.ByReference dcImageSnap = new Data_T_DCImageSnap.T_DCImageSnap.ByReference();
            dcImageSnap.uiImageId = (byte) 0x00;
            dcImageSnap.ucLightIndex = (byte) 0x00;
            dcImageSnap.ucLightMode = (byte) 0x00;
            dcImageSnap.usGroupId = (byte) 0x00;
            return net.Net_ImageSnap(handle, dcImageSnap);
        }

        /**
         * 图片抓拍，并将图片保存在指定路径
         * @param fileName 图片文件路径，如："D:\\image.jpg"
         * @return 状态码0表示成功，其他值详情见E_ReturnCode
         */
        public int saveImageToJpeg(String fileName) {
            NET net = NET.Singleton.getInstance();
            return net.Net_SaveImageToJpeg(handle, fileName);
        }

        /**
         * 开闸
         * @return
         */
        public int openGate() {
            Data_T_ControlGate.T_ControlGate.ByReference ptControlGate = new Data_T_ControlGate.T_ControlGate.ByReference();
            ptControlGate.ucState = (byte) 0x01;
            NET net = NET.Singleton.getInstance();
            return net.Net_GateSetup(handle, ptControlGate);
        }

        /**
         * 关闸
         * @return
         */
        public int closeGate() {
            Data_T_ControlGate.T_ControlGate.ByReference ptControlGate = new Data_T_ControlGate.T_ControlGate.ByReference();
            ptControlGate.ucState = (byte) 0x02;
            NET net = NET.Singleton.getInstance();
            return net.Net_GateSetup(handle, ptControlGate);
        }

        /**
         * 停闸
         * @return
         */
        public int stopGate() {
            Data_T_ControlGate.T_ControlGate.ByReference ptControlGate = new Data_T_ControlGate.T_ControlGate.ByReference();
            ptControlGate.ucState = (byte) 0x03;
            NET net = NET.Singleton.getInstance();
            return net.Net_GateSetup(handle, ptControlGate);
        }

        // Getter and Setter methods
        public String getIp() {
            return ip;
        }

        public Integer getHandle() {
            return handle;
        }

        public String getSn() {
            return sn;
        }

        public void setSn(String sn) {
            this.sn = sn;
        }

        private byte[] hexToByteArray(String hex) {
            int len = hex.length();
            byte[] data = new byte[len / 2];
            for (int i = 0; i < len; i += 2) {
                data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                        + Character.digit(hex.charAt(i + 1), 16));
            }
            return data;
        }
    }

    /**
     * 车牌实时识别回调函数 - 只获取识别结果
     * @param tHandle 设备句柄
     * @param uiImageId 图像ID
     * @param ptImageInfo 图像信息
     * @param ptPicInfo 图像数据
     * @param userDataPointer 回调参数（注册回调函数时传入的用户Pointer参数）
     * @return
     */
    private static final Callback_FGetImageCBEx.FGetImageCBEx callback = new Callback_FGetImageCBEx.FGetImageCBEx() {
        @Override
        public int invoke(
                int tHandle,
                int uiImageId,
                net.sdk.bean.serviceconfig.imagesnap.Data_T_ImageUserInfo.T_ImageUserInfo.ByReference ptImageInfo,
                net.sdk.bean.serviceconfig.imagesnap.Data_T_PicInfo.T_PicInfo.ByReference ptPicInfo,
                Pointer userDataPointer) {
            try {
                // 获取摄像头IP
                String cameraIp = userDataPointer == null ? "" : userDataPointer.getString(0,"UTF-8");

                // 解析车牌号码
                String plateNumber = new String(ptImageInfo.szLprResult,"GB2312").trim();

                // 获取抓拍时间
                String snapTime = new String(ptImageInfo.acSnapTime).trim();

                // 获取图片数据
                byte[] panoramaPic = ptPicInfo.ptPanoramaPicBuff.getByteArray(0, ptPicInfo.uiPanoramaPicLen);
                byte[] vehiclePic = ptPicInfo.ptVehiclePicBuff.getByteArray(0, ptPicInfo.uiVehiclePicLen);

                // 输出车牌识别结果
                log.info("车牌识别结果 - 摄像头IP: {}, 车牌号: {}, 抓拍时间: {}, 车牌颜色: {}, 车牌类型: {}, 全景图大小: {}KB, 车牌图大小: {}KB",
                        cameraIp, plateNumber, snapTime, ptImageInfo.ucPlateColor, ptImageInfo.ucLprType,
                        panoramaPic.length / 1024, vehiclePic.length / 1024);

//                // 检查是否在CoreFlow流程中
//                if (!isInCoreFlowProcess()) {
//                    log.debug("当前不在CoreFlow流程中，忽略车牌识别 - 车牌号: {}, 摄像头IP: {}", plateNumber, cameraIp);
//                    return 0;
//                }

                // 尝试从Redis获取当前活跃的seqNo
                String currentSeqNo = getCurrentActiveSeqNo();

//                if (currentSeqNo == null || currentSeqNo.trim().isEmpty()) {
//                    log.warn("未获取到当前活跃的seqNo，忽略车牌识别 - 车牌号: {}, 摄像头IP: {}", plateNumber, cameraIp);
//                    return 0;
//                }

                // 处理图片数据：保存到CoreFlow统一目录
                String[] imagePaths = saveImagesToUnifiedDirectory(cameraIp, plateNumber, snapTime, panoramaPic, vehiclePic, currentSeqNo);

                // 将车牌识别结果关联到seqNo
//                associatePlateNumberWithSeqNo(plateNumber, cameraIp, snapTime, currentSeqNo, imagePaths);

                // 发送车牌识别结果到ECS系统
                sendToEcs(cameraIp, plateNumber, snapTime, ptImageInfo.ucPlateColor, ptImageInfo.ucLprType, imagePaths);

            } catch (Exception e) {
                log.error("车牌识别回调处理异常", e);
            }
            return 0;
        }
    };

    /**
     * 设备连接状态变更回调函数
     */
    private final static Callback_NET_CONTROLCALLBACKEx.NET_CONTROLCALLBACKEx controlCallBackEx = new Callback_NET_CONTROLCALLBACKEx.NET_CONTROLCALLBACKEx() {
        @Override
        public void invoke(int i, byte b, Pointer pointer) {
            String cameraIp = pointer == null ? "" : pointer.getString(0, "UTF-8");
            System.out.println("[" + cameraIp + "]设备连接状态变更["+b+"]：" + (b == 0 ? "无意义" : b == 1 ? "连接中" : b == 2 ? "连接成功" : b == 3 ? "连接失败" : "未知状态"));
            //连接断开或连接失败
            if (b == 3) {
                synchronized (deviceConnectionMap){
                    CameraDevice dc = deviceConnectionMap.remove(cameraIp);
                    if (dc != null) {
                        dc.close();
                    }
                }

                //5秒后重连设备
                timer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        connectDevice(cameraIp,true);
                    }
                }, 5000L);
            }
        }
    };

    /**
     * 报警消息回调函数
     */
    private final static Callback_FGetReportCBEx.FGetReportCBEx reportCbEx = new Callback_FGetReportCBEx.FGetReportCBEx() {
        @Override
        public int invoke(int handle, byte ucType, Pointer ptMessage, Pointer ptUserData) {
            String userData = ptUserData == null ? "" : ptUserData.getString(0, "UTF-8");
            System.out.println("userData = "+userData);

            //线圈输入GPIO上报
            if (Data_E_ReportMess.E_ReportMess.REPORT_MESS_LOOP_DETECTOR.ordinal() == ucType) {
                Data_T_IOStateRsp.T_IOStateRsp.ByReference rsp = new Data_T_IOStateRsp.T_IOStateRsp.ByReference();
                int nSize = rsp.size();
                byte[] readbuf = new byte[nSize];
                ptMessage.read(0, readbuf, 0, nSize);
                rsp.ucIndex = readbuf[0];
                rsp.ucLState = readbuf[1];
                System.out.println("ucIndex:"+rsp.ucIndex);
                System.out.println("ucLState:"+rsp.ucLState);
            }
            //485输入上报
            else if (Data_E_ReportMess.E_ReportMess.REPORT_MESS_RS485_IN_DATA.ordinal() == ucType) {
                Data_T_RS485Data.T_RS485Data.ByReference rs485Data = new Data_T_RS485Data.T_RS485Data.ByReference();
                int nSize = rs485Data.size();
                byte[] readbuf = new byte[nSize];
                ptMessage.read(0, readbuf, 0, nSize);
                rs485Data.rs485Id = readbuf[0];
                rs485Data.ucReserved = readbuf[1];
                short dataLen = bytesToShort(new byte[]{readbuf[3],readbuf[2]});
                rs485Data.dataLen = dataLen;
                System.arraycopy(readbuf,4,rs485Data.data,0,dataLen);
                System.out.println("============= RS485 RX =============");
                System.out.println("rs485Id:" + rs485Data.rs485Id);
                System.out.println("dataLen:" + rs485Data.dataLen);
                System.out.println("data:" + byteArrayToHex(rs485Data.data));
                System.out.println("====================================");

                CameraDevice dc = deviceConnectionMap.get(userData);
                if (dc != null) {
                    //发送数据到RS485（演示）
                    byte[] data = new byte[dataLen];
                    for(int i = 0;i<dataLen;i++) {
                        //过来的数据加1后返回
                        data[i] = (byte) (rs485Data.data[i] + 0x01);
                    }

                    int rtn = dc.sendRS485Data((byte) 0,data);
                    System.out.println("sendRS485Data result = "+rtn);
                }
            }
            //扫描读头信息上报
            else if(Data_E_ReportMess.E_ReportMess.REPORT_USB_KEY_BOARD_INFO.ordinal() == ucType){
                int nsize = 1024;
                byte[] readbuf = new byte[nsize];
                ptMessage.read(0, readbuf, 0, nsize);
                String content = new String(readbuf).trim();
                System.out.println("二维码:" + content);
            }
            return 0;
        }
    };

    /**
     * 连接相机
     * @param cameraIp 相机IP
     * @param autoReConnect 是否自动重连
     */
    public static void connectDevice(String cameraIp,boolean autoReConnect) {
        CameraDevice dc = new CameraDevice(cameraIp);
        int handle = dc.connect();
        if (handle >= 0){
            // 从配置获取车牌号码解密密码
            String pwd = null;
            if (staticConfig != null && staticConfig.getEncryptionEnabled() != null && staticConfig.getEncryptionEnabled()) {
                pwd = staticConfig.getEncryptionPassword();
                log.info("车牌加密已启用 - 摄像头IP: {}", cameraIp);

                // 启用车牌加密
                if (pwd != null && !pwd.trim().isEmpty()) {
                    int rtn = dc.enableEnc(pwd);
                    log.info("启用车牌识别加密结果: {} - 摄像头IP: {}", rtn, cameraIp);

                    if (rtn != 0) {
                        log.warn("启用车牌加密失败 - 摄像头IP: {}, 错误码: {}", cameraIp, rtn);
                        pwd = null; // 加密失败，设置为null
                    }
                } else {
                    log.warn("车牌加密密码未配置 - 摄像头IP: {}", cameraIp);
                    pwd = null;
                }
            } else {
                log.debug("车牌加密未启用 - 摄像头IP: {}", cameraIp);
            }

            //注册设备状态（在线/离线）变更回调
            int rtn = dc.setControlCallBackEx(cameraIp,controlCallBackEx);
            if(rtn!= 0){
                System.err.println("["+cameraIp+"] setControlCallBackEx failed : "+rtn);
            }

            /**
             * 注册车牌实时识别回调函数
             */
            rtn = dc.regImageRecvEx(pwd,cameraIp,callback);
            if(rtn != 0){
                System.err.println("["+cameraIp+"] regImageRecvEx failed : "+rtn);
            }

            /**
             * 注册设备上报的报警消息回调函数
             */
            rtn = dc.regReportMessEx(cameraIp,reportCbEx);
            if(rtn != 0){
                System.err.println("["+cameraIp+"] regReportMessEx failed : "+rtn);
            }

            deviceConnectionMap.put(cameraIp,dc);
        }
        else{
            if(autoReConnect) {
                timer.schedule(new TimerTask() {
                    @Override
                    public void run() {
                        connectDevice(cameraIp,true);
                    }
                }, 5000L);
            }
        }
    }

    public static short bytesToShort(byte[] b) {
        short s = 0;
        short s0 = (short) (b[0] & 0xff);// 最低位
        short s1 = (short) (b[1] & 0xff);
        s0 <<= 8;
        s = (short) (s0 | s1);
        return s;
    }

    public static String byteArrayToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder();
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString().trim().toUpperCase();
    }

    /**
     * Spring Boot 启动时执行
     */
    @Override
    public void run(String... args) throws Exception {
        if (!licensePlateConfig.getEnabled()) {
            log.info("车牌识别功能已禁用，跳过初始化");
            return;
        }

        log.info("开始初始化车牌识别管理器...");

        try {
            // 初始化网络
            int rtn = net.Net_Init();
            log.info("NET初始化结果: {}", rtn);
            if (rtn != 0) {
                log.error("NET初始化失败！");
                return;
            }

            // 连接配置的相机
            List<String> cameraIps = licensePlateConfig.getCameraIps();
            if (cameraIps != null && !cameraIps.isEmpty()) {
                log.info("开始连接车牌识别摄像头，共{}个", cameraIps.size());
                for (String cameraIp : cameraIps) {
                    log.info("正在连接摄像头: {}", cameraIp);
                    connectDevice(cameraIp, true);
                }
            } else {
                log.warn("未配置车牌识别摄像头IP地址");
            }

            // 注册系统退出钩子
            Runtime.getRuntime().addShutdownHook(new Thread(() -> {
                log.info("系统正在关闭，释放车牌识别资源...");
                shutdown();
            }));

            log.info("车牌识别管理器初始化完成");
        } catch (Exception e) {
            log.error("车牌识别管理器初始化失败", e);
        }
    }

    /**
     * 系统关闭时释放资源
     */
    @PreDestroy
    public void shutdown() {
        log.info("开始释放车牌识别资源...");

        synchronized (deviceConnectionMap) {
            // 关闭所有设备连接
            for (Map.Entry<String, CameraDevice> entry : deviceConnectionMap.entrySet()) {
                String cameraIp = entry.getKey();
                CameraDevice deviceConnection = entry.getValue();
                try {
                    log.info("正在关闭摄像头连接: {}", cameraIp);
                    deviceConnection.close();
                } catch (Exception e) {
                    log.error("关闭摄像头连接失败: {}", cameraIp, e);
                }
            }
            deviceConnectionMap.clear();
        }

        // 释放全局资源
        try {
            net.Net_UNinit();
            log.info("NET资源释放完成");
        } catch (Exception e) {
            log.error("释放NET资源失败", e);
        }

        // 停止定时器
        if (timer != null) {
            timer.cancel();
            log.info("定时器已停止");
        }

        log.info("车牌识别资源释放完成");
    }

    /**
     * 获取当前连接的设备数量
     */
    public int getConnectedDeviceCount() {
        return deviceConnectionMap.size();
    }

    /**
     * 获取所有连接的设备IP
     */
    public List<String> getConnectedDeviceIps() {
        return new ArrayList<>(deviceConnectionMap.keySet());
    }

    /**
     * 手动连接指定IP的摄像头
     */
    public boolean connectCamera(String cameraIp) {
        if (cameraIp == null || cameraIp.trim().isEmpty()) {
            log.warn("摄像头IP地址不能为空");
            return false;
        }

        if (deviceConnectionMap.containsKey(cameraIp)) {
            log.warn("摄像头{}已经连接", cameraIp);
            return true;
        }

        log.info("手动连接摄像头: {}", cameraIp);
        connectDevice(cameraIp, true);
        return deviceConnectionMap.containsKey(cameraIp);
    }

    /**
     * 手动断开指定IP的摄像头
     */
    public boolean disconnectCamera(String cameraIp) {
        if (cameraIp == null || cameraIp.trim().isEmpty()) {
            log.warn("摄像头IP地址不能为空");
            return false;
        }

        CameraDevice device = deviceConnectionMap.remove(cameraIp);
        if (device != null) {
            try {
                log.info("手动断开摄像头: {}", cameraIp);
                device.close();
                return true;
            } catch (Exception e) {
                log.error("断开摄像头连接失败: {}", cameraIp, e);
                return false;
            }
        } else {
            log.warn("摄像头{}未连接", cameraIp);
            return false;
        }
    }

    /**
     * 检查指定IP的摄像头是否已连接
     */
    public boolean isCameraConnected(String cameraIp) {
        return deviceConnectionMap.containsKey(cameraIp);
    }

    /**
     * 获取指定IP的摄像头设备
     */
    public CameraDevice getCameraDevice(String cameraIp) {
        return deviceConnectionMap.get(cameraIp);
    }

    /**
     * 重新连接所有配置的摄像头
     */
    public void reconnectAllCameras() {
        log.info("开始重新连接所有摄像头...");

        List<String> cameraIps = licensePlateConfig.getCameraIps();
        if (cameraIps != null && !cameraIps.isEmpty()) {
            for (String cameraIp : cameraIps) {
                if (!isCameraConnected(cameraIp)) {
                    log.info("重新连接摄像头: {}", cameraIp);
                    connectDevice(cameraIp, true);
                }
            }
        }

        log.info("重新连接完成，当前连接设备数量: {}", getConnectedDeviceCount());
    }

    /**
     * 断开所有摄像头连接
     */
    public void disconnectAllCameras() {
        log.info("开始断开所有摄像头连接...");

        synchronized (deviceConnectionMap) {
            List<String> cameraIps = new ArrayList<>(deviceConnectionMap.keySet());
            for (String cameraIp : cameraIps) {
                disconnectCamera(cameraIp);
            }
        }

        log.info("所有摄像头连接已断开");
    }

    /**
     * 发送车牌识别结果到ECS系统（静态方法，用于回调函数）
     * @param cameraIp 摄像头IP
     * @param plateNumber 车牌号
     * @param snapTime 抓拍时间
     * @param plateColor 车牌颜色
     * @param plateType 车牌类型
     * @param imagePaths 图片路径数组 [全景图路径, 车牌图路径]
     */
    private static void sendToEcs(String cameraIp, String plateNumber, String snapTime,
                                  int plateColor, int plateType, String[] imagePaths) {
        try {
            if (staticEcsService == null) {
                log.warn("ECS服务未初始化，跳过发送车牌识别结果");
                return;
            }

            // 构建ECS识别信息
            EcsIdentifyInfoDTO identifyInfo = new EcsIdentifyInfoDTO();
            // 从配置文件获取门吊号，如果配置为空则使用默认值
            String craneNo = (staticConfig != null && staticConfig.getCraneNo() != null) ?
                    staticConfig.getCraneNo() : "TRMG01";
            identifyInfo.setCraneNo(craneNo); // 设置门吊号
            identifyInfo.setIdentifyType(1); // 设置识别类型为拖车（车牌识别）
            identifyInfo.setIdentifyTruckNo(plateNumber); // 设置车牌号

            // 处理图片URL
            if (imagePaths != null && imagePaths.length > 0) {
                java.util.List<String> imgUrls = new java.util.ArrayList<>();
                for (String imagePath : imagePaths) {
                    if (imagePath != null && !imagePath.trim().isEmpty()) {
                        // 转换为相对路径URL（去掉基础路径前缀）
                        String relativeUrl = convertToRelativeUrl(imagePath);
                        if (relativeUrl != null) {
                            imgUrls.add(relativeUrl);
                        }
                    }
                }
                identifyInfo.setImgUrls(imgUrls);
                log.debug("车牌识别图片URL已添加 - 车牌号: {}, 图片数量: {}", plateNumber, imgUrls.size());
            }

            log.info("开始发送车牌识别结果到ECS - 摄像头IP: {}, 车牌号: {}", cameraIp, plateNumber);

            // 异步发送到ECS，避免阻塞回调函数
            new Thread(() -> {
                try {
                    staticEcsService.sendIdentifyInfo(identifyInfo);
                    log.info("车牌识别结果发送到ECS成功 - 车牌号: {}", plateNumber);
                } catch (Exception e) {
                    log.error("发送车牌识别结果到ECS异常 - 车牌号: {}, 错误: {}", plateNumber, e.getMessage(), e);
                }
            }).start();

        } catch (Exception e) {
            log.error("构建ECS识别信息异常 - 车牌号: {}, 错误: {}", plateNumber, e.getMessage(), e);
        }
    }

    /**
     * 将绝对路径转换为前端访问URL
     * @param absolutePath 绝对路径
     * @return 前端可直接访问的完整URL，如果转换失败则返回null
     */
    private static String convertToRelativeUrl(String absolutePath) {
        try {
            if (absolutePath == null || absolutePath.trim().isEmpty()) {
                return null;
            }

            // 获取基础图片目录
            String baseDir = getUnifiedImageDirectory();
            if (baseDir == null || baseDir.trim().isEmpty()) {
                return null;
            }

            // 标准化路径分隔符
            String normalizedAbsolutePath = absolutePath.replace("\\", "/");
            String normalizedBaseDir = baseDir.replace("\\", "/");

            // 确保基础目录以/结尾
            if (!normalizedBaseDir.endsWith("/")) {
                normalizedBaseDir += "/";
            }

            // 如果绝对路径以基础目录开头，则去掉基础目录前缀
            if (normalizedAbsolutePath.startsWith(normalizedBaseDir)) {
                String relativeUrl = normalizedAbsolutePath.substring(normalizedBaseDir.length());
                // 构建完整的前端访问URL
                return buildFrontendAccessUrl(relativeUrl);
            } else {
                // 如果不是以基础目录开头，尝试提取文件名
                int lastSlashIndex = normalizedAbsolutePath.lastIndexOf("/");
                if (lastSlashIndex >= 0 && lastSlashIndex < normalizedAbsolutePath.length() - 1) {
                    String fileName = normalizedAbsolutePath.substring(lastSlashIndex + 1);
                    return buildFrontendAccessUrl(fileName);
                }
            }

            return null;
        } catch (Exception e) {
            log.error("转换图片路径为相对URL失败 - 路径: {}, 错误: {}", absolutePath, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 构建前端访问URL
     * @param relativePath 相对路径
     * @return 前端可直接访问的完整URL
     */
    private static String buildFrontendAccessUrl(String relativePath) {
        try {
            if (relativePath == null || relativePath.trim().isEmpty()) {
                return null;
            }

            // 获取基础URL和URL前缀配置
            String baseUrl = getFrontendBaseUrl();
            String urlPrefix = getImageUrlPrefix();

            // 构建完整URL
            StringBuilder urlBuilder = new StringBuilder();

            // 添加基础URL
            if (baseUrl != null && !baseUrl.trim().isEmpty()) {
                urlBuilder.append(baseUrl);
                if (!baseUrl.endsWith("/")) {
                    urlBuilder.append("/");
                }
            }

            // 添加URL前缀
            if (urlPrefix != null && !urlPrefix.trim().isEmpty()) {
                if (urlPrefix.startsWith("/")) {
                    urlPrefix = urlPrefix.substring(1);
                }
                urlBuilder.append(urlPrefix);
                if (!urlPrefix.endsWith("/")) {
                    urlBuilder.append("/");
                }
            }

            // 添加相对路径
            if (relativePath.startsWith("/")) {
                relativePath = relativePath.substring(1);
            }

            // 转换门吊号格式：TRMG01 -> rmg1, TRMG02 -> rmg2
            relativePath = convertRmgNameInPath(relativePath);

            urlBuilder.append(relativePath);

            return urlBuilder.toString();
        } catch (Exception e) {
            log.error("构建前端访问URL失败 - 相对路径: {}, 错误: {}", relativePath, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 转换路径中的门吊号格式
     * @param path 原始路径
     * @return 转换后的路径
     */
    private static String convertRmgNameInPath(String path) {
        try {
            if (path == null || path.trim().isEmpty()) {
                return path;
            }

            // 不进行门吊号格式转换，保持原始的TRMG01格式
            // 因为Nginx配置和文件存储都使用TRMG01格式
            return path;
        } catch (Exception e) {
            log.error("转换路径中门吊号格式失败 - 路径: {}, 错误: {}", path, e.getMessage(), e);
            return path;
        }
    }

    /**
     * 获取前端基础URL配置
     * @return 前端基础URL，如果未配置则返回默认值
     */
    private static String getFrontendBaseUrl() {
        try {
            if (staticPathConfigUtil != null) {
                // 优先使用专门的前端图片服务器配置
                if (staticPathConfigUtil.getFrontend() != null) {
                    String frontendBaseUrl = staticPathConfigUtil.getFrontend().getBaseUrl();
                    if (frontendBaseUrl != null && !frontendBaseUrl.trim().isEmpty()) {
                        // 去掉末尾的斜杠
                        if (frontendBaseUrl.endsWith("/")) {
                            frontendBaseUrl = frontendBaseUrl.substring(0, frontendBaseUrl.length() - 1);
                        }
                        return frontendBaseUrl;
                    }
                }

                // 如果前端配置为空，回退到IIS地址
                String iisUrl = staticPathConfigUtil.getIisUrl();
                if (iisUrl != null && !iisUrl.trim().isEmpty()) {
                    // 去掉末尾的斜杠
                    if (iisUrl.endsWith("/")) {
                        iisUrl = iisUrl.substring(0, iisUrl.length() - 1);
                    }
                    return iisUrl;
                }
            }
            return "http://localhost:8080";
        } catch (Exception e) {
            log.error("获取前端基础URL配置异常", e);
            return "http://localhost:8080";
        }
    }

    /**
     * 获取图片URL前缀配置
     * @return URL前缀，如果未配置则返回默认值
     */
    private static String getImageUrlPrefix() {
        try {
            // 优先使用专门的前端图片URL前缀配置
            if (staticPathConfigUtil != null && staticPathConfigUtil.getFrontend() != null) {
                String urlPrefix = staticPathConfigUtil.getFrontend().getUrlPrefix();
                if (urlPrefix != null && !urlPrefix.trim().isEmpty()) {
                    return urlPrefix.trim();
                }
            }

            // 默认使用img作为URL前缀
            return "img";
        } catch (Exception e) {
            log.error("获取图片URL前缀配置异常", e);
            return "img";
        }
    }

    /**
     * 检查是否在CoreFlow流程中
     * @return 如果在流程中返回true，否则返回false
     */
    private static boolean isInCoreFlowProcess() {
        try {
            if (staticRedisUtil == null) {
                log.warn("Redis工具未初始化，无法检查CoreFlow流程状态");
                return false;
            }

            // 检查是否有当前活跃的seqNo
            String currentSeqNo = staticRedisUtil.get("CURRENT_SEQ_NO");
            if (currentSeqNo != null && !currentSeqNo.trim().isEmpty()) {
                // 进一步检查流程状态（可选）
                String processStatus = staticRedisUtil.get("PROCESS_STATUS:" + currentSeqNo.trim());
                if (processStatus != null && ("LOCKING".equals(processStatus) || "UNLOCKING".equals(processStatus) || "ACTIVE".equals(processStatus))) {
                    log.debug("当前在CoreFlow流程中 - seqNo: {}, 状态: {}", currentSeqNo, processStatus);
                    return true;
                } else if (processStatus == null) {
                    // 如果没有具体状态，但有seqNo，也认为在流程中
                    log.debug("当前在CoreFlow流程中 - seqNo: {}", currentSeqNo);
                    return true;
                }
            }
            log.debug("当前不在CoreFlow流程中");
            return false;
        } catch (Exception e) {
            log.error("检查CoreFlow流程状态异常", e);
            return false;
        }
    }

    /**
     * 获取当前活跃的seqNo（从Redis缓存中获取）
     * @return 当前活跃的seqNo，如果没有则返回null
     */
    private static String getCurrentActiveSeqNo() {
        try {
            if (staticRedisUtil == null) {
                log.warn("Redis工具未初始化，无法获取当前seqNo");
                return null;
            }

            // 从Redis获取当前活跃的seqNo
            String currentSeqNo = staticRedisUtil.get("CURRENT_SEQ_NO");
            if (currentSeqNo != null && !currentSeqNo.trim().isEmpty()) {
                log.debug("获取到当前活跃seqNo: {}", currentSeqNo);
                return currentSeqNo.trim();
            } else {
                log.debug("当前没有活跃的seqNo");
                return null;
            }
        } catch (Exception e) {
            log.error("获取当前活跃seqNo异常", e);
            return null;
        }
    }

    /**
     * 保存图片到CoreFlow统一目录结构
     * @param cameraIp 摄像头IP
     * @param plateNumber 车牌号
     * @param snapTime 抓拍时间
     * @param panoramaPic 全景图片数据
     * @param vehiclePic 车牌图片数据
     * @param seqNo 序列号
     * @return 图片路径数组 [全景图路径, 车牌图路径]
     */
    private static String[] saveImagesToUnifiedDirectory(String cameraIp, String plateNumber, String snapTime,
                                                         byte[] panoramaPic, byte[] vehiclePic, String seqNo) {
        String[] imagePaths = new String[2]; // [全景图路径, 车牌图路径]

        try {
            // 获取CoreFlow的基础路径和门吊号
            String fileDirPath = getUnifiedImageDirectory();
            String rmgName = getRmgName();

            // 如果没有seqNo，生成一个临时的
            if (seqNo == null || seqNo.trim().isEmpty()) {
                seqNo = "PLATE_" + java.time.LocalDateTime.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            }

            // 获取日期（格式：yyyyMMdd）
            String dateStr = java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));

            // 构建与CoreFlow一致的目录结构：{fileDirPath}/{rmgName}/{日期}/{seqNo}/
            String saveDir = fileDirPath + "/" + rmgName + "/" + dateStr + "/" + seqNo + "/";

            // 确保目录存在
            java.io.File dir = new java.io.File(saveDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (!created) {
                    log.warn("创建CoreFlow统一图片目录失败: {}", saveDir);
                }
            }

            // 生成与CoreFlow一致的文件名：{seqNo}_PLATE_{类型}.jpg
            String filePrefix = seqNo + "_PLATE";

            // 保存全景图片
            if (panoramaPic != null && panoramaPic.length > 0) {
                String panoramaPath = saveDir + filePrefix + "_panorama.jpg";
                try (java.io.FileOutputStream fos = new java.io.FileOutputStream(panoramaPath)) {
                    fos.write(panoramaPic);
                    imagePaths[0] = panoramaPath;
                    log.debug("车牌全景图已保存: {}", panoramaPath);
                }
            }

            // 保存车牌图片
            if (vehiclePic != null && vehiclePic.length > 0) {
                String vehiclePath = saveDir + filePrefix + "_close.jpg";
                try (java.io.FileOutputStream fos = new java.io.FileOutputStream(vehiclePath)) {
                    fos.write(vehiclePic);
                    imagePaths[1] = vehiclePath;
                    log.debug("车牌特写图已保存: {}", vehiclePath);
                }
            }

            log.info("车牌图片已保存到CoreFlow统一目录 - 车牌号: {}, seqNo: {}, 目录: {}", plateNumber, seqNo, saveDir);

        } catch (Exception e) {
            log.error("保存车牌图片到CoreFlow统一目录失败 - 车牌号: {}, seqNo: {}, 错误: {}", plateNumber, seqNo, e.getMessage(), e);
        }

        return imagePaths;
    }

    /**
     * 获取统一的图片保存目录（与CoreFlow一致）
     * @return 统一图片目录路径
     */
    private static String getUnifiedImageDirectory() {
        try {
            if (staticPathConfigUtil != null) {
                // 使用PathConfigUtil获取图片保存路径
                String imagePath = staticPathConfigUtil.getLocalPath();
                if (imagePath != null && !imagePath.trim().isEmpty()) {
                    return imagePath.trim();
                }
            }

            // 如果无法获取配置，使用默认路径
            return "D:/pfkj/img";
        } catch (Exception e) {
            log.error("获取统一图片目录异常", e);
            return "D:/pfkj/img";
        }
    }

    /**
     * 获取门吊号（与CoreFlow一致）
     * @return 门吊号
     */
    private static String getRmgName() {
        try {
            if (staticPathConfigUtil != null) {
                // 使用PathConfigUtil获取门吊号
                String rmgName = staticPathConfigUtil.getRmgName();
                if (rmgName != null && !rmgName.trim().isEmpty()) {
                    return rmgName.trim();
                }
            }

            // 如果无法获取配置，使用默认门吊号
            return "402";
        } catch (Exception e) {
            log.error("获取门吊号异常", e);
            return "402";
        }
    }

    /**
     * 将车牌识别结果关联到seqNo
     * @param plateNumber 车牌号
     * @param cameraIp 摄像头IP
     * @param snapTime 抓拍时间
     * @param seqNo 序列号
     * @param imagePaths 图片路径数组
     */
    private static void associatePlateNumberWithSeqNo(String plateNumber, String cameraIp, String snapTime,
                                                      String seqNo, String[] imagePaths) {
        try {
            if (staticRedisUtil == null) {
                log.warn("Redis工具未初始化，无法关联车牌号与seqNo");
                return;
            }

            // 构建车牌识别数据
            Map<String, Object> plateData = new HashMap<>();
            plateData.put("plateNumber", plateNumber);
            plateData.put("cameraIp", cameraIp);
            plateData.put("snapTime", snapTime);
            plateData.put("timestamp", System.currentTimeMillis());
            plateData.put("panoramaImagePath", imagePaths[0]);
            plateData.put("plateImagePath", imagePaths[1]);

            if (seqNo != null && !seqNo.trim().isEmpty()) {
                // 如果有seqNo，将车牌信息关联到该seqNo
                String redisKey = "PLATE_DATA:" + seqNo;

                // 将车牌数据存储到Redis，设置过期时间为1小时
                staticRedisUtil.setEx(redisKey, com.alibaba.fastjson.JSON.toJSONString(plateData), 1, TimeUnit.HOURS);

                // 同时存储车牌号到seqNo的映射，便于后续查询
                String plateToSeqKey = "PLATE_TO_SEQ:" + plateNumber;
                staticRedisUtil.setEx(plateToSeqKey, seqNo, 1, TimeUnit.HOURS);

                log.info("车牌识别结果已关联到seqNo - 车牌号: {}, seqNo: {}", plateNumber, seqNo);
            } else {
                // 如果没有seqNo，将车牌信息存储为待关联状态
                String pendingKey = "PENDING_PLATE:" + plateNumber + ":" + System.currentTimeMillis();
                staticRedisUtil.setEx(pendingKey, com.alibaba.fastjson.JSON.toJSONString(plateData), 30, TimeUnit.MINUTES);

                log.info("车牌识别结果已存储为待关联状态 - 车牌号: {}", plateNumber);
            }

        } catch (Exception e) {
            log.error("关联车牌号与seqNo异常 - 车牌号: {}, seqNo: {}, 错误: {}", plateNumber, seqNo, e.getMessage(), e);
        }
    }

    /**
     * 公共方法：手动关联车牌号到指定seqNo（供其他服务调用）
     * @param plateNumber 车牌号
     * @param seqNo 序列号
     * @return 是否关联成功
     */
    public boolean associatePlateToSeqNo(String plateNumber, String seqNo) {
        try {
            if (redisUtil == null) {
                log.warn("Redis工具未初始化，无法关联车牌号与seqNo");
                return false;
            }

            // 查找待关联的车牌数据
            String pattern = "PENDING_PLATE:" + plateNumber + ":*";
            Set<String> pendingKeys = redisUtil.keys(pattern);

            if (pendingKeys != null && !pendingKeys.isEmpty()) {
                for (String pendingKey : pendingKeys) {
                    String plateDataJson = redisUtil.get(pendingKey);
                    if (plateDataJson != null) {
                        // 将数据移动到正式的seqNo关联中
                        String redisKey = "PLATE_DATA:" + seqNo;
                        redisUtil.setEx(redisKey, plateDataJson, 1, TimeUnit.HOURS);

                        // 存储车牌号到seqNo的映射
                        String plateToSeqKey = "PLATE_TO_SEQ:" + plateNumber;
                        redisUtil.setEx(plateToSeqKey, seqNo, 1, TimeUnit.HOURS);

                        // 删除待关联数据
                        redisUtil.delete(pendingKey);

                        log.info("车牌号已手动关联到seqNo - 车牌号: {}, seqNo: {}", plateNumber, seqNo);
                        return true;
                    }
                }
            }

            log.warn("未找到待关联的车牌数据 - 车牌号: {}", plateNumber);
            return false;

        } catch (Exception e) {
            log.error("手动关联车牌号与seqNo异常 - 车牌号: {}, seqNo: {}, 错误: {}", plateNumber, seqNo, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 根据seqNo获取关联的车牌信息
     * @param seqNo 序列号
     * @return 车牌信息，如果没有则返回null
     */
    public String getPlateDataBySeqNo(String seqNo) {
        try {
            if (redisUtil == null) {
                return null;
            }

            String redisKey = "PLATE_DATA:" + seqNo;
            return redisUtil.get(redisKey);

        } catch (Exception e) {
            log.error("根据seqNo获取车牌信息异常 - seqNo: {}, 错误: {}", seqNo, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 设置CoreFlow流程状态（供CoreFlow调用）
     * @param seqNo 序列号
     * @param status 流程状态：LOCKING(上锁中), UNLOCKING(解锁中), ACTIVE(活跃), COMPLETED(完成), null(清除状态)
     */
    public void setCoreFlowProcessStatus(String seqNo, String status) {
        try {
            if (redisUtil == null) {
                log.warn("Redis工具未初始化，无法设置CoreFlow流程状态");
                return;
            }

            if (seqNo == null || seqNo.trim().isEmpty()) {
                log.warn("seqNo为空，无法设置CoreFlow流程状态");
                return;
            }

            if (status == null || "COMPLETED".equals(status)) {
                // 清除流程状态
                redisUtil.delete("CURRENT_SEQ_NO");
                redisUtil.delete("PROCESS_STATUS:" + seqNo);
                log.info("CoreFlow流程状态已清除 - seqNo: {}", seqNo);
            } else {
                // 设置当前活跃的seqNo和流程状态
                redisUtil.setEx("CURRENT_SEQ_NO", seqNo, 2, TimeUnit.HOURS);
                redisUtil.setEx("PROCESS_STATUS:" + seqNo, status, 2, TimeUnit.HOURS);
                log.info("CoreFlow流程状态已设置 - seqNo: {}, 状态: {}", seqNo, status);
            }

        } catch (Exception e) {
            log.error("设置CoreFlow流程状态异常 - seqNo: {}, 状态: {}, 错误: {}", seqNo, status, e.getMessage(), e);
        }
    }

    /**
     * 开始CoreFlow流程（上锁开始时调用）
     * @param seqNo 序列号
     */
    public void startCoreFlowProcess(String seqNo) {
        setCoreFlowProcessStatus(seqNo, "LOCKING");
        log.info("CoreFlow流程已开始 - seqNo: {}", seqNo);
    }

    /**
     * 结束CoreFlow流程（解锁完成时调用）
     * @param seqNo 序列号
     */
    public void endCoreFlowProcess(String seqNo) {
        setCoreFlowProcessStatus(seqNo, "COMPLETED");
        log.info("CoreFlow流程已结束 - seqNo: {}", seqNo);
    }

    /**
     * 设置解锁状态（解锁开始时调用）
     * @param seqNo 序列号
     */
    public void setUnlockingStatus(String seqNo) {
        setCoreFlowProcessStatus(seqNo, "UNLOCKING");
        log.info("CoreFlow解锁状态已设置 - seqNo: {}", seqNo);
    }

    /**
     * 实例方法：保存车牌识别图片到指定目录（使用配置文件路径）
     * @param cameraIp 摄像头IP
     * @param plateNumber 车牌号
     * @param snapTime 抓拍时间
     * @param panoramaPic 全景图片数据
     * @param vehiclePic 车牌图片数据
     */
    public void saveLicensePlateImages(String cameraIp, String plateNumber, String snapTime,
                                       byte[] panoramaPic, byte[] vehiclePic) {
        try {
            // 从配置文件读取保存路径
            String baseDir = licensePlateConfig.getImageSavePath();
            if (baseDir == null || baseDir.trim().isEmpty()) {
                baseDir = "D:/license_plate_images/";
            }

            // 确保路径以/结尾
            if (!baseDir.endsWith("/") && !baseDir.endsWith("\\")) {
                baseDir += "/";
            }

            // 创建目录结构：基础路径/日期/摄像头IP/
            String dateDir = snapTime.length() >= 8 ? snapTime.substring(0, 8) :
                    java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyyMMdd"));
            String saveDir = baseDir + dateDir + "/" + cameraIp + "/";

            // 确保目录存在
            java.io.File dir = new java.io.File(saveDir);
            if (!dir.exists()) {
                boolean created = dir.mkdirs();
                if (!created) {
                    log.warn("创建目录失败: {}", saveDir);
                }
            }

            // 生成文件名（时间戳_车牌号）
            String timestamp = snapTime.replaceAll("[^0-9]", ""); // 移除非数字字符
            if (timestamp.isEmpty()) {
                timestamp = String.valueOf(System.currentTimeMillis());
            }
            String cleanPlateNumber = plateNumber.replaceAll("[^a-zA-Z0-9\u4e00-\u9fa5]", ""); // 保留中文、英文、数字
            String filePrefix = timestamp + "_" + cleanPlateNumber;

            // 保存全景图片
            if (panoramaPic != null && panoramaPic.length > 0) {
                String panoramaPath = saveDir + filePrefix + "_panorama.jpg";
                try (java.io.FileOutputStream fos = new java.io.FileOutputStream(panoramaPath)) {
                    fos.write(panoramaPic);
                    log.debug("全景图片已保存: {}", panoramaPath);
                }
            }

            // 保存车牌图片
            if (vehiclePic != null && vehiclePic.length > 0) {
                String vehiclePath = saveDir + filePrefix + "_plate.jpg";
                try (java.io.FileOutputStream fos = new java.io.FileOutputStream(vehiclePath)) {
                    fos.write(vehiclePic);
                    log.debug("车牌图片已保存: {}", vehiclePath);
                }
            }

            log.info("车牌图片保存成功 - 车牌号: {}, 保存目录: {}", plateNumber, saveDir);

        } catch (Exception e) {
            log.error("保存车牌图片失败 - 车牌号: {}, 错误: {}", plateNumber, e.getMessage(), e);
        }
    }
}
