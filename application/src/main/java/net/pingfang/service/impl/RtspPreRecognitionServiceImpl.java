package net.pingfang.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.core.hardware.rtsp.RtspBestResult;
import net.pingfang.model.entity.RtspPreRecognitionResult;
import net.pingfang.model.dto.RtspPreRecognitionQueryDTO;
import net.pingfang.model.dto.RtspPreRecognitionListDTO;
import net.pingfang.model.dto.RtspPreRecognitionDetailDTO;
import net.pingfang.mapper.RtspPreRecognitionResultMapper;
import net.pingfang.service.RtspPreRecognitionService;
import net.pingfang.util.PathConfigUtil;
import net.pingfang.config.ProjectConfig;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import net.pingfang.model.common.Page;

import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * RTSP预识别服务实现类
 * <AUTHOR>
 * @Date 2025/06/27
 * @Description: RTSP预识别服务实现
 */
@Slf4j
@Service
public class RtspPreRecognitionServiceImpl implements RtspPreRecognitionService {
    
    @Autowired
    private RtspPreRecognitionResultMapper mapper;

    @Autowired
    private PathConfigUtil pathConfigUtil;

    @Autowired
    private ProjectConfig projectConfig;

    @Autowired
    private ObjectMapper objectMapper;
    
    @Override
    public void savePreRecognitionResult(String seqNo, 
                                       RtspBestResult bestResult,
                                       Map<Integer, RtspBestResult> allCameraResults,
                                       String endReason, 
                                       LocalDateTime startTime) {
        savePreRecognitionResultInternal(seqNo, bestResult, allCameraResults, 
                                       endReason, startTime, "SUCCESS", null);
    }
    
    @Override
    public void savePreRecognitionResultWithError(String seqNo,
                                                RtspBestResult bestResult,
                                                Map<Integer, RtspBestResult> allCameraResults,
                                                String endReason,
                                                LocalDateTime startTime,
                                                String errorMessage) {
        savePreRecognitionResultInternal(seqNo, bestResult, allCameraResults,
                                       endReason, startTime, "FAILED", errorMessage);
    }
    
    /**
     * 内部保存方法
     */
    private void savePreRecognitionResultInternal(String seqNo,
                                                RtspBestResult bestResult,
                                                Map<Integer, RtspBestResult> allCameraResults,
                                                String endReason,
                                                LocalDateTime startTime,
                                                String status,
                                                String errorMessage) {
        try {
            RtspPreRecognitionResult entity = new RtspPreRecognitionResult();
            
            // 基础信息
            entity.setSeqNo(seqNo);
            entity.setCraneNo(pathConfigUtil.getRmgName());
            entity.setStatus(status);
            
            // 识别结果信息
            if (bestResult != null && bestResult.getRecognitionResult() != null) {
                entity.setContainerNumber(bestResult.getRecognitionResult().getCtnNo());
                entity.setConfidence(bestResult.getRecognitionResult().getTrust());
            } else {
                entity.setContainerNumber("NO_RESULT");
                entity.setConfidence(0.0f);
            }
            
            // 图片数量
            entity.setImageCount(allCameraResults != null ? allCameraResults.size() : 0);

            // 图片信息处理
            if (allCameraResults != null && !allCameraResults.isEmpty()) {
                // 构建图片信息JSON
                String allCameraImagesJson = buildAllCameraImagesJson(allCameraResults);
                entity.setAllCameraImages(allCameraImagesJson);

                // 设置最佳结果信息
                if (bestResult != null) {
                    entity.setBestCameraId(bestResult.getCameraId());
                    entity.setBestImagePath(bestResult.getImagePath());
                    entity.setBestImageUrl(convertToUrl(bestResult.getImagePath()));
                }
            }

            // 流程信息
            LocalDateTime endTime = LocalDateTime.now();
            entity.setStartTime(startTime);
            entity.setEndTime(endTime);
            entity.setDuration(Duration.between(startTime, endTime).toMillis());
            
            // 错误信息
            if (errorMessage != null) {
                entity.setErrorMessage(errorMessage);
            }
            
            // 创建信息
            entity.setCreateBy("SYSTEM");
            entity.setCreateTime(endTime);
            
            mapper.insert(entity);
            
            log.info("✅ 预识别结果已保存到数据库 - seqNo:{}, 箱号:{}, 耗时:{}ms", 
                    seqNo, entity.getContainerNumber(), entity.getDuration());
            
        } catch (Exception e) {
            log.error("❌ 保存预识别结果到数据库失败 - seqNo:{}", seqNo, e);
        }
    }
    
    @Override
    public Page<RtspPreRecognitionListDTO> getPreRecognitionList(RtspPreRecognitionQueryDTO queryDTO) {
        try {
            // 简化查询 - 暂时查询所有数据
            List<RtspPreRecognitionResult> allResults = mapper.selectAll();

            // 手动分页
            int start = (queryDTO.getPage() - 1) * queryDTO.getSize();
            int end = Math.min(start + queryDTO.getSize(), allResults.size());
            List<RtspPreRecognitionResult> pageContent = allResults.subList(start, end);

            // 转换为DTO
            List<RtspPreRecognitionListDTO> dtoList = pageContent.stream()
                    .map(this::convertToListDTO)
                    .collect(Collectors.toList());

            // 使用项目标准的Page类
            Page<RtspPreRecognitionListDTO> page = new Page<>();
            page.setTotal(allResults.size());
            page.setRecords(dtoList);

            return page;

        } catch (Exception e) {
            log.error("查询预识别记录列表失败", e);
            Page<RtspPreRecognitionListDTO> emptyPage = new Page<>();
            emptyPage.setTotal(0);
            emptyPage.setRecords(Collections.emptyList());
            return emptyPage;
        }
    }

    @Override
    public RtspPreRecognitionDetailDTO getPreRecognitionDetail(Long id) {
        try {
            RtspPreRecognitionResult entity = mapper.selectById(id);
            if (entity == null) {
                log.warn("未找到ID为{}的预识别记录", id);
                return null;
            }

            RtspPreRecognitionDetailDTO dto = new RtspPreRecognitionDetailDTO();
            BeanUtils.copyProperties(entity, dto);

            return dto;

        } catch (Exception e) {
            log.error("获取预识别详情失败 - ID:{}", id, e);
            return null;
        }
    }

    @Override
    public List<Map<String, Object>> getPreRecognitionImages(Long id) {
        try {
            RtspPreRecognitionResult entity = mapper.selectById(id);
            if (entity == null) {
                log.warn("未找到ID为{}的预识别记录", id);
                return Collections.emptyList();
            }

            String allCameraImagesJson = entity.getAllCameraImages();
            if (allCameraImagesJson == null || allCameraImagesJson.trim().isEmpty()) {
                log.warn("预识别记录ID:{}没有图片信息", id);
                return Collections.emptyList();
            }

            // 解析JSON获取图片列表
            Map<String, Object> imageData = objectMapper.readValue(allCameraImagesJson, Map.class);
            List<Map<String, Object>> cameras = (List<Map<String, Object>>) imageData.get("cameras");

            if (cameras == null) {
                return Collections.emptyList();
            }

            log.info("获取预识别图片列表成功 - ID:{}, 图片数量:{}", id, cameras.size());
            return cameras;

        } catch (Exception e) {
            log.error("获取预识别图片列表失败 - ID:{}", id, e);
            return Collections.emptyList();
        }
    }

    /**
     * 转换为列表DTO
     */
    private RtspPreRecognitionListDTO convertToListDTO(RtspPreRecognitionResult entity) {
        RtspPreRecognitionListDTO dto = new RtspPreRecognitionListDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 构建所有相机图片信息JSON
     */
    private String buildAllCameraImagesJson(Map<Integer, RtspBestResult> allCameraResults) {
        try {
            Map<String, Object> data = new HashMap<>();
            List<Map<String, Object>> cameras = new ArrayList<>();

            if (allCameraResults != null) {
                for (Map.Entry<Integer, RtspBestResult> entry : allCameraResults.entrySet()) {
                    Integer cameraId = entry.getKey();
                    RtspBestResult result = entry.getValue();

                    Map<String, Object> cameraInfo = new HashMap<>();
                    cameraInfo.put("cameraId", cameraId);
                    cameraInfo.put("cameraName", "相机" + cameraId);

                    if (result != null) {
                        cameraInfo.put("imageName", extractFileName(result.getImagePath()));
                        cameraInfo.put("imagePath", result.getImagePath());
                        cameraInfo.put("imageUrl", convertToUrl(result.getImagePath()));
                        cameraInfo.put("fileSize", 1024000L); // 默认文件大小
                        cameraInfo.put("imageWidth", 1920);   // 默认宽度
                        cameraInfo.put("imageHeight", 1080);  // 默认高度
                        cameraInfo.put("captureTime", LocalDateTime.now().toString());

                        if (result.getRecognitionResult() != null) {
                            cameraInfo.put("containerNo", result.getRecognitionResult().getCtnNo());
                            cameraInfo.put("containerIso", result.getRecognitionResult().getIso());
                            cameraInfo.put("confidence", result.getRecognitionResult().getTrust());
                            cameraInfo.put("checkPassed", result.getRecognitionResult().getBcheck());
                        } else {
                            cameraInfo.put("containerNo", null);
                            cameraInfo.put("containerIso", null);
                            cameraInfo.put("confidence", 0.0);
                            cameraInfo.put("checkPassed", false);
                        }
                    } else {
                        cameraInfo.put("imageName", null);
                        cameraInfo.put("imagePath", null);
                        cameraInfo.put("imageUrl", null);
                        cameraInfo.put("fileSize", 0L);
                        cameraInfo.put("imageWidth", 0);
                        cameraInfo.put("imageHeight", 0);
                        cameraInfo.put("containerNo", null);
                        cameraInfo.put("containerIso", null);
                        cameraInfo.put("confidence", 0.0);
                        cameraInfo.put("checkPassed", false);
                        cameraInfo.put("captureTime", null);
                    }

                    cameras.add(cameraInfo);
                }
            }

            data.put("cameras", cameras);
            data.put("totalCount", cameras.size());

            // 计算平均置信度
            double avgConfidence = cameras.stream()
                .mapToDouble(c -> {
                    Object confidence = c.getOrDefault("confidence", 0.0);
                    if (confidence instanceof Float) {
                        return ((Float) confidence).doubleValue();
                    } else if (confidence instanceof Double) {
                        return (Double) confidence;
                    } else if (confidence instanceof Number) {
                        return ((Number) confidence).doubleValue();
                    } else {
                        return 0.0;
                    }
                })
                .average()
                .orElse(0.0);
            data.put("averageConfidence", avgConfidence);

            return objectMapper.writeValueAsString(data);
        } catch (Exception e) {
            log.error("构建相机图片信息JSON失败", e);
            return "{}";
        }
    }

    /**
     * 转换图片路径为URL（使用与ECS上传相同的逻辑）
     */
    private String convertToUrl(String imagePath) {
        if (imagePath == null || imagePath.isEmpty()) {
            return null;
        }

        // 如果已经是完整URL，直接返回
        if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
            return imagePath;
        }

        try {
            // 处理图片路径，转换为HTTP可访问的URL（与ECS上传逻辑一致）
            String actualFilePath = imagePath;

            // 如果路径包含项目文件目录，则去除该部分
            if (projectConfig != null && projectConfig.getFileDirPath() != null
                && actualFilePath.startsWith(projectConfig.getFileDirPath())) {
                actualFilePath = actualFilePath.substring(projectConfig.getFileDirPath().length());
            }

            // 确保路径分隔符正确（Windows -> Unix）
            actualFilePath = actualFilePath.replace("\\", "/");
            if (!actualFilePath.startsWith("/")) {
                actualFilePath = "/" + actualFilePath;
            }

            // 使用IIS URL作为基础URL（与ECS上传一致）
            String iisUrl = pathConfigUtil.getIisUrl();
            if (iisUrl != null && !iisUrl.isEmpty()) {
                // 确保IIS URL以/结尾
                if (!iisUrl.endsWith("/")) {
                    iisUrl += "/";
                }
                return iisUrl + actualFilePath.substring(1); // 去掉开头的/避免双斜杠
            }

            // 如果IIS URL不可用，使用默认配置
            return "http://localhost:8080/api/images" + actualFilePath;

        } catch (Exception e) {
            log.error("转换图片路径为URL失败 - 原路径: {}, 错误: {}", imagePath, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从路径中提取文件名
     */
    private String extractFileName(String filePath) {
        if (filePath == null || filePath.isEmpty()) {
            return null;
        }

        int lastSlash = filePath.lastIndexOf('/');
        if (lastSlash >= 0 && lastSlash < filePath.length() - 1) {
            return filePath.substring(lastSlash + 1);
        }

        return filePath;
    }
}
