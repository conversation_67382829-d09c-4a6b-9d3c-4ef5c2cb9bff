package net.pingfang.service.impl;

import java.io.File;
import java.io.IOException;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.LogReqDTO;
import net.pingfang.model.dto.QueryLogListResDTO;
import net.pingfang.service.LogService;
import net.pingfang.util.FileUtil;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.nio.file.attribute.FileTime;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
@Service
@Slf4j
public class LogServiceImpl implements LogService {

    @Value("${logging.file.path}")
    private String logFilePath;

    @Override
    public Result<List<QueryLogListResDTO>> queryLogList(LogReqDTO param) throws IOException {
        //1、校验
        LocalDateTime startTime = param.getStartTime();
        LocalDateTime endTime = param.getEndTime();
        if (startTime == null || endTime == null) {
            return Result.fail("开始、结束时间必填！");
        }
        long startTimeMillis = Timestamp.valueOf(startTime).getTime();
        long endTimeMillis = Timestamp.valueOf(endTime).getTime();
        List<QueryLogListResDTO> fileList = new ArrayList<>();
        //2、查询符合的文件
        File logDir = new File(logFilePath);
        File[] files = logDir.listFiles();
        File tempFile = null;
        if (files != null && files.length > 0) {
            for (int i = 0; i < files.length; i++) {
                // 指定自己的目标文件
                tempFile = files[i];
                // 根据文件的绝对路径获取Path
                Path path = Paths.get(tempFile.getAbsolutePath());
                // 根据path获取文件的基本属性类
                BasicFileAttributes attrs = Files.readAttributes(path, BasicFileAttributes.class);
                // 从基本属性类中获取文件创建时间
                FileTime creationTime = attrs.creationTime();
                FileTime lastModifiedTime = attrs.lastModifiedTime();
                // 将文件创建时间转成毫秒
                long creationTimeMillis = creationTime.toMillis();
                long lastModifiedTimeMillis = lastModifiedTime.toMillis();
                if (creationTimeMillis >= startTimeMillis && lastModifiedTimeMillis <= endTimeMillis) {
                    QueryLogListResDTO resDTO = new QueryLogListResDTO();
                    resDTO.setAbsolutePath(tempFile.getAbsolutePath());
                    resDTO.setCreationTime(Instant.ofEpochMilli(creationTimeMillis)
                            .atZone(ZoneId.of("Asia/Shanghai"))
                            .toLocalDateTime());
                    fileList.add(resDTO);
                }
            }
        }
        //3、响应
        if (!CollectionUtils.isEmpty(fileList) && fileList.size() > 0) {
            fileList = fileList.stream()
                    .sorted(Comparator.comparing(QueryLogListResDTO::getCreationTime).reversed())
                    .collect(Collectors.toList());
        }
        return Result.success(fileList);
    }

    @Override
    public Result<List<String>> queryLogFile(String filePath) {
        if (StringUtils.isBlank(filePath)) {
            return Result.fail("文件地址不能为空！");
        }
        List<String> contents = new ArrayList<>();
        FileUtil.previewFilesToList(filePath, contents);
        return Result.success(contents);
    }

    @Override
    public void downloadLogFile(String filePath, HttpServletResponse response) {
        if (StringUtils.isBlank(filePath)) {
            return;
        }
        List<String> contents = new ArrayList<>();
        String fileName = filePath.substring(filePath.lastIndexOf("\\") + 1, filePath.lastIndexOf(".") - 1);
        FileUtil.downloadFiles(filePath, fileName, response);
    }
}
