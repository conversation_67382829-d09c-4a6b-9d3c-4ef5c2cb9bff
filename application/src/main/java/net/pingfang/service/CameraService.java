package net.pingfang.service;

import net.pingfang.model.common.Result;
import net.pingfang.model.dto.CameraControlDTO;
import net.pingfang.model.dto.CameraPrePositionControlDTO;
import net.pingfang.model.dto.CamersToneDTO;
import net.pingfang.model.entity.Camera;
import net.pingfang.model.entity.RecognizeConfig;
import java.util.List;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
public interface CameraService {

    Result<String> add(Camera camera);

    Result<List<Camera>> queryAll();

    Result<String> update(Camera camera);

    Result<String> delete(Long id);

    Result<String> control(Camera<PERSON>ontrolDTO param);

    Result<String> restart(Long id);

    Result<String> controlPrePosition(CameraPrePositionControlDTO param);

    Result<String> tone(CamersToneDTO param);

    Result<List<RecognizeConfig>> queryPresetConfig(Long laneId, Long cameraId);
}
