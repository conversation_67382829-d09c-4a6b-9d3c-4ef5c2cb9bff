package net.pingfang.service;

import net.pingfang.model.common.Result;
import net.pingfang.model.dto.SystemInfoDTO;
import net.pingfang.model.dto.SystemInfoSimpleDTO;
import net.pingfang.model.entity.SystemConfig;
import java.util.List;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/6/5
 */
public interface SystemConfigService {
    Result<List<SystemConfig>> queryAll();

    Result<String> update(List<SystemConfig> param);

    Result<SystemInfoDTO> querySystemInfo();

    Result<SystemInfoSimpleDTO> querySimpleSystemInfo();
}
