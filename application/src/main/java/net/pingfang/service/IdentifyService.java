package net.pingfang.service;

import java.util.List;

import net.pingfang.model.dto.message.MessageIdentifyDTO;
import net.pingfang.model.dto.message.MessageImgDTO;
import net.pingfang.model.dto.message.MessageLockDTO;
import net.pingfang.model.dto.message.MessageUnLockDTO;
import net.pingfang.model.entity.ImageDTO;
import net.pingfang.model.entity.Record;

/**
 * <AUTHOR>
 * @since 2023-09-12 16:40
 */
public interface IdentifyService {

    void lock (MessageLockDTO lockDTO);

    void identify (MessageIdentifyDTO identifyDTO);

    void unLock (MessageUnLockDTO unLockDTO);

    void img (MessageImgDTO imgDTO);
    
    void workRecord(Record recordSync);
    
    void workRecordImg(List<ImageDTO> imgList);

}
