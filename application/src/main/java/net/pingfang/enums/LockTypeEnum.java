package net.pingfang.enums;

import lombok.Getter;

/**
 * 开闭锁状态
 * <AUTHOR>
 * @since 2023-11-01 9:53
 */
@Getter
public enum LockTypeEnum {

    UNKNOWN_TO_LOCK(1, "从上锁到上锁、从未知到上锁"),
    LOCK_TO_UNLOCK(2, "从上锁到解锁、从未知到解锁"),
    UNLOCK_TO_LOCK(3,"从解锁到上锁"),
    UNLOCK_TO_UNLOCK(4,"从解锁到解锁"),
    EXCEPTION(0,"plc数据异常"),
    ;

    private int value;
    private String desc;

    LockTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}
