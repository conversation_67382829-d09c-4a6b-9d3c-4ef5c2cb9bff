package net.pingfang.enums;

import lombok.Getter;

/**
 * 是否有危标 todo coed 值待定
 * <AUTHOR>
 * @since 2023-10-09 10:56
 */
@Getter
public enum IsDangerousLableEnum {

    YES(3, "有危标","true"),
    NO(2, "无危标","false"),
    UNKNOWN(1, "未知",""),
    ;

    private Integer code;
    private String codeDesc;
    private String codeDescOne;


    IsDangerousLableEnum(Integer code, String codeDesc,String codeDescOne) {
        this.code = code;
        this.codeDesc = codeDesc;
        this.codeDescOne = codeDescOne;
    }

    public static String getCodeDescOneByValue(Integer value) {
        for (IsDangerousLableEnum isDangerousLableEnum : IsDangerousLableEnum.values()) {
            if (isDangerousLableEnum.getCode().equals(value)) {
                return isDangerousLableEnum.getCodeDescOne();
            }
        }
        return null;
    }
}
