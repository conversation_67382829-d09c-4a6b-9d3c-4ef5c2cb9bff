package net.pingfang.enums;

import lombok.Getter;

/**
 * 车牌中文字符映射关系
 *
 * @title: PlateNumberEnum
 * @author: cb
 * @date: 2025-03-06 16:46
 * @version: 1.0
 */
@Getter
public enum PlateNumberEnum {
    ONE("0010", "京"),
    TWO("0020", "津"),
    THREE("0030","冀"),
    FOUR("0040","晋"),
    FIVE("0050","蒙"),
    SIX("0060","辽"),
    SEVEN("0070","吉"),
    EIGHT("0080","黑"),
    NINE("0090","沪"),
    TEN("0100","苏"),
    ELEVEN("0110","浙"),
    TWELVE("0120","皖"),
    THIRTEEN("0130","闽"),
    FOURTEEN("0140","赣"),
    FIFTEEN("0150","鲁"),
    SIXTEEN("0160","豫"),
    SEVENTEEN("0170","鄂"),
    EIGHTEEN("0180","湘"),
    NINETEEN("0190","粤"),
    TWENTY("0200","桂"),
    TWENTY_ONE("0210","琼"),
    TWENTY_TWO("0220","渝"),
    TWENTY_THREE("0230","川"),
    TWENTY_FOUR("0240","贵"),
    TWENTY_FIVE("0250","云"),
    TWENTY_SIX("0260","藏"),
    TWENTY_SEVEN("0270","陕"),
    TWENTY_EIGHT("0280","甘"),
    TWENTY_NINE("0290","青"),
    THIRTY("0300","宁"),
    THIRTY_ONE("0310","新"),
    THIRTY_TWO("0320","挂"),
    THIRTY_THREE("0330","领"),
    THIRTY_FOUR("0340","使"),
    THIRTY_FIVE("0350","警"),
    THIRTY_SIX("0360","学"),
    THIRTY_SEVEN("0370","港"),
    THIRTY_EIGHT("0380","澳"),

    ;

    private String code;
    private String word;

    PlateNumberEnum(String code, String word) {
        this.code = code;
        this.word = word;
    }

    public static String getWordByCode(String code) {
        for (PlateNumberEnum value : PlateNumberEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getWord();
            }
        }
        return "无";
    }
}
