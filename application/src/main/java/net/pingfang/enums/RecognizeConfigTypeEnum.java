package net.pingfang.enums;

import lombok.Getter;
import org.springframework.util.StringUtils;

/**
 * 预置位类型
 */
@Getter
public enum RecognizeConfigTypeEnum {
    // 短箱识别
    SHORT_CTN("短箱识别", 1),

    // 长箱识别
    LONG_CTN("长箱识别", 2),

    // 海侧全景
    SEA_ALL("海侧全景", 3),

    // 陆侧全景
    LAND_ALL("陆侧全景", 4),

    // 车顶号识别
    PLATE_TOP("车顶号识别", 5),

    // 双箱1识别
    DOUBLE_FIRST("双箱1识别", 6),

    // 双箱2识别
    DOUBLE_SECOND("双箱2识别", 7),

    // 箱门识别
    DOOR("箱门识别", 8),

    // 角柱特写
    JZ("角柱特写", 9),

    // 上锁抓拍
    LOCK("上锁抓拍", 10),

    // 解锁抓拍
    UNLOCK("解锁抓拍", 11),

    // 车牌抓拍
    PLATE("车牌抓拍", 12),

    ;


    private Integer value;
    private String desc;


    RecognizeConfigTypeEnum(String desc, Integer value) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (RecognizeConfigTypeEnum enu : RecognizeConfigTypeEnum.values()) {
            if (enu.getValue().equals(code)) {
                return enu.getDesc();
            }
        }
        return null;
    }


    public static Integer getCodeByDesc(String desc) {
        if (StringUtils.isEmpty(desc)) {
            return null;
        }
        for (RecognizeConfigTypeEnum containerTypeEnum : RecognizeConfigTypeEnum.values()) {
            if (containerTypeEnum.getDesc().equalsIgnoreCase(desc)) {
                return containerTypeEnum.getValue();
            }
        }
        return null;
    }

}
