package net.pingfang.enums;

import lombok.Getter;

/**
 * 车道相机配置-指令(1-设置预置位, 2-调用相机预置位, 3-调用车道预置位)
 * <AUTHOR>
 * @since 2023-10-17 14:48
 */
@Getter
public enum CameraCommandEnum {
    position(1, "设置预置位"),
    camera_position(2, "调用相机预置位"),
    lane_position(3,"调用车道预置位"),
    ;

    private Integer value;
    private String desc;

    CameraCommandEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }
}
