package net.pingfang.enums;

import lombok.Getter;

import java.util.Optional;

/**
 * 安全系统的作业类型枚举
 *
 * @title: SafeWorkTypeEnum
 * @author: cb
 * @date: 2025-04-03 17:47
 * @version: 1.0
 */
@Getter
public enum SafeWorkTypeEnum {

    CAR_TRAIN(1, "集卡","火车"),
    TRAIN_CAR(2, "火车","集卡"),
    HEAP_TRAIN(3,"堆场", "火车"),
    TRAIN_HEAP(4, "火车","堆场"),
    CAR_HEAP(5, "集卡", "堆场"),
    HEAP_CAR(6, "堆场","集卡"),
    HEAP_HEAP(7, "堆场","堆场"),
    TRAIN_TRAIN(8, "火车","火车"),
    CAR_CAR(9, "集卡", "集卡"),

    ;

    private final Integer value;
    private final String lockLane;
    private final String unlockLane;


    SafeWorkTypeEnum(int value, String lockLane, String unlockLane) {
        this.value = value;
        this.lockLane = lockLane;
        this.unlockLane = unlockLane;
    }

    public static Optional<Integer> getValueByLanes(String lockLane, String unlockLane) {
        for (SafeWorkTypeEnum type : SafeWorkTypeEnum.values()) {
            if (type.lockLane.equals(lockLane) && type.unlockLane.equals(unlockLane)) {
                return Optional.of(type.value);
            }
        }
        return Optional.empty();
    }
}
