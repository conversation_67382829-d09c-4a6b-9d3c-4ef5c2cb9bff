package net.pingfang.enums;

import lombok.Getter;

/**
 * 开闭锁状态
 * <AUTHOR>
 * @since 2023-11-01 9:53
 */
@Getter
public enum HeightTypeEnum {

    DOWN_TO_DOWN(1, "从识别高度以下到识别高度以下"),
    DOWN_TO_UP(2, "从识别高度以下到识别高度以上"),
    UP_TO_UP(3,"从识别高度以上到识别高度以上"),
    UP_TO_DOWN(4,"从识别高度以上到识别高度以下"),
    EXCEPTION(0,"plc数据异常"),
    ;

    private int value;
    private String desc;

    HeightTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

}
