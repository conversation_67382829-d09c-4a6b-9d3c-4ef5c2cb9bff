package net.pingfang.util;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Base64;

import lombok.extern.slf4j.Slf4j;

/**
 * 图片转base64
 * 
 * @title: ImageToBase64.java 
 * @package net.pingfang.util 
 * @description: TODO
 * @author: lzl
 * @date: 2024年4月28日 下午4:39:38 
 * @version: 3.0
 */
@Slf4j
public class ImageToBase64Util {
	
	public static void main(String[] args) {
		String img = "D:\\image\\1234\\234\\111.jpg";
		//System.out.println(imgToBase64(img).length());
		File file = new File(img);
		System.out.println(file.getName());
	}
	
	/**
	 * 图片转化base64
	 * 
	  * @Title: imgToBase64  
	  * @Description: 方法描述  
	  * @param @param imagePath
	  * @param @return 参数  
	  * @return String 返回类型  
	  * @throws
	 */
	public static final String imgToBase64(String imagePath) {		
		try {
            // 读取图片文件为字节数组
            byte[] imageBytes = Files.readAllBytes(Paths.get(imagePath));

            // 将字节数组编码为Base64字符串
            String base64Image = Base64.getEncoder().encodeToString(imageBytes);

            // 输出Base64字符串
           return base64Image;
        } catch (IOException e) {
           log.error("Error reading image file: " + e.getMessage());
            return null;
        }
		
	}

}
