package net.pingfang.util;

import java.io.BufferedOutputStream;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.List;
import java.util.zip.GZIPInputStream;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * 文件工具类
 * <AUTHOR>
 * @Date 2023/7/14 16:49
 * @Description:
*/
@Slf4j
public class FileUtil {


    private static final String UTF_8 = "UTF-8";
    private static final String GB_2312 = "GB2312";
    private static final String GBK = "gbk";

    private static final String ROW = "\n";

    private static final String LOG = ".log";
    private static final String GZ = ".gz";


    /**
     * 文件预览（TXT）存储到list中
     * @param filePath 文件绝对路径
     * @param contents 读取的文件内容
     */
    public static void previewFilesToList(String filePath,List<String> contents) {
        File file = new File(filePath);
        if(!file.exists()){
            log.error("文件不存在:{}",filePath);
            return;
        }
        //根据不同文件类型处理   D:\log\rmg\spring.log.2023-07-14.0.gz    D:\log\rmg\spring.log
        if(file.getName().endsWith(LOG)){
            //TXT 文件处理
            String line = null;
            try (FileInputStream fileInputStream = new FileInputStream(file);) {
                BufferedReader br = new BufferedReader(new InputStreamReader(fileInputStream));
                //循环文件中每一行的数据
                while (null != (line = br.readLine())){
                    contents.add(line);
                }
            } catch (IOException e) {
                log.error("txt文件读取:{}", ExceptionUtil.getStackTrace(e));
            }
        }else{
            //GZ 文件处理
            StringBuffer sb = null;
            try (final InputStream inStream = new FileInputStream(file);
                    final GZIPInputStream gzip = new GZIPInputStream(inStream);
                    final InputStreamReader reader = new InputStreamReader(gzip);
                    final BufferedReader in = new BufferedReader(reader);) {
                String read;
                while ((read = in.readLine()) != null ){
                    contents.add(read);
                }
            } catch (IOException e) {
                log.error("gz文件读取:{}", ExceptionUtil.getStackTrace(e));
            }
        }
    }

    /**
     * 文件下载（TXT）
     * @param filePath 文件绝对路径
     * @param fileName 导出文件名
     * @param response http
     */
    public static void downloadFiles(String filePath,String fileName,HttpServletResponse response) {
        File file = new File(filePath);
        if(!file.exists()){
            log.error("文件不存在:{}",filePath);
        }
        response.setContentType("text/plain;charset=GB2312");
        response.setCharacterEncoding("GB2312");
        response.setHeader("Content-Disposition","attachment;filename="+fileName);

        //根据不同文件类型处理
        if(file.getName().endsWith(LOG)){
            //TXT 文件处理
            InputStream inStream = null;
            try {
                inStream = new FileInputStream(file);
                // 循环取出流中的数据
                byte[] b = new byte[1024];
                int len;
                while ((len = inStream.read(b)) > 0) {
                    response.getOutputStream().write(b, 0, len);
                }
                inStream.close();
                response.getOutputStream().close();
            } catch (FileNotFoundException e) {
                log.error("txt文件下载 not found:{}", ExceptionUtil.getStackTrace(e));
            } catch (IOException e){
                log.error("txt文件下载 error:{}", ExceptionUtil.getStackTrace(e));
            }
        }else{
            //GZ 文件处理
            StringBuffer sb = null;
            //创建输出流
            BufferedOutputStream buff = null;
            ServletOutputStream outSTr = null;
            try (final InputStream inStream = new FileInputStream(file);
                    final GZIPInputStream gzip = new GZIPInputStream(inStream);
                    final InputStreamReader reader = new InputStreamReader(gzip);
                    final BufferedReader in = new BufferedReader(reader);) {

                sb = new StringBuffer();
                String read;
                while ((read = in.readLine()) != null ){
                    sb.append(read).append(ROW);
                }
                if(!StringUtils.isEmpty(sb.toString())){
                    outSTr = response.getOutputStream();
                    buff = new BufferedOutputStream(outSTr);
                    //制定格式
                    buff.write(sb.toString().getBytes("UTF-8"));
                    //关闭输出流
                    buff.flush();
                    buff.close();
                }
            } catch (IOException e) {
                e.printStackTrace();log.error("gz文件下载 error:{}", ExceptionUtil.getStackTrace(e));
            }catch (Exception e) {
                e.printStackTrace();
            } finally {
                try {
                    buff.close();
                    outSTr.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

    /**
     * 获取压缩文件内容
     * @param file
     */
    public static String getZipContent(File file) {
        StringBuffer sb = null;
        try (final InputStream inStream = new FileInputStream(file);
                final GZIPInputStream gzip = new GZIPInputStream(inStream);
                final InputStreamReader reader = new InputStreamReader(gzip);
                final BufferedReader in = new BufferedReader(reader);) {

            sb = new StringBuffer();
            String read;
            while ((read = in.readLine()) != null ){
                sb.append(read);
            }
            return sb.toString();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }




    public static void main(String[] args) {
        String filePath = "D:\\log\\rmg\\spring.log.2023-07-14.0.gz";

        File file = new File(filePath);
        if(!file.exists()){
            log.error("文件不存在:{}",filePath);
        }
        System.out.println("名字："+file.getName());
        StringBuffer sb = null;
        try (final InputStream inStream = new FileInputStream(file);
                final GZIPInputStream gzip = new GZIPInputStream(inStream);
                final InputStreamReader reader = new InputStreamReader(gzip);
                final BufferedReader in = new BufferedReader(reader);) {

            sb = new StringBuffer();
            String read;
            while ((read = in.readLine()) != null ){
                sb.append(read);
            }
            System.out.println(sb.toString());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
