package net.pingfang.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import net.pingfang.config.WebSocketConfig;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import javax.websocket.*;
import javax.websocket.server.ServerEndpoint;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@ServerEndpoint(value = "/websocket", configurator = WebSocketConfig.class)
public class WebSocketUtil {

    public static final String PID = "PID";

    public static final String SUCCESS = "success";

    public static final String CLOSE = "close";

    /**
     * 存放所有在线的客户端
     */
    private final static Map<String, Session> clients = new ConcurrentHashMap<>();


    public static final String CHECK_PREFIX = "CHECK_";


    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session) {
        Map<String, Object> userProperties = session.getUserProperties();
        String pid = (String) userProperties.get(PID);
        clients.put(pid, session);
        log.debug("有新连接加入,当前在线人数为：{}", clients.size());
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose(Session session) {
        Map<String, Object> userProperties = session.getUserProperties();
        String pid = (String) userProperties.get(PID);
        clients.remove(pid);
        log.debug("有新连接close,当前在线人数为：{}", clients.size());

    }

    @OnError
    public void onError(Session session, Throwable error) {
        Map<String, Object> userProperties = session.getUserProperties();
        String pid = (String) userProperties.get(PID);
        clients.remove(pid);
        log.debug("有新连接close,当前在线人数为：{}", clients.size());
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        try {
            Map<String, Object> userProperties = session.getUserProperties();
            String pid = (String) userProperties.get(PID);
            // 返回心跳检测结果
            if (message != null && message.startsWith(CHECK_PREFIX)) {
                log.debug("服务端收到客户端[{}]的消息:{}", pid, message);
                this.sendMessage(SUCCESS, session);
                return;
            }


            if (CLOSE.equalsIgnoreCase(message)) {
                onClose(session);
                return;
            }
            batchSendMessage(message, session);
            log.info("服务端收到客户端[{}]的消息:{}", pid, message);
        }catch (Exception exception){
            log.error("消息处理异常:{}", ExceptionUtil.getStackTrace(exception));
        }


    }

    /**
     * 点对点发消息
     *
     * @param message 消息内容
     */
    private void sendMessage(String message, Session session) {
        if (session==null){
            log.info("未获取到session对象,无法发送消息");
            return;
        }

        Map<String, Object> userProperties = session.getUserProperties();
        String pid = (String) userProperties.get(PID);
        log.debug("服务端给客户端[{}]发送消息{}", pid, message);
        try {
            synchronized (clients) {
                session.getBasicRemote().sendText(message);
            }
        } catch (Exception exception) {
            log.error("send message error:{}", exception.getMessage());
        }
    }

    /**
     * 群发消息 排除掉其中一个
     *
     * @param message 消息内容
     * @param session 会话对象
     */
    private void batchSendMessage(String message, Session session) {
        log.info("服务端给客户端发送消息{}", message);
        synchronized (clients) {
            for (Map.Entry<String, Session> sessionEntry : clients.entrySet()) {
                if (session == sessionEntry.getValue()) {
                    continue;
                }
                Session toSession = sessionEntry.getValue();
                try {
                    toSession.getBasicRemote().sendText(message);
                } catch (Exception ex) {
                    log.error(ex.getMessage());
                }
            }
        }
    }

    /**
     * 群发消息
     *
     * @param message 消息内容
     */
    private void batchSendMessage(String message) {
        synchronized (clients) {
            for (Map.Entry<String, Session> sessionEntry : clients.entrySet()) {
                Session toSession = sessionEntry.getValue();
                try {
                    toSession.getBasicRemote().sendText(message);
                } catch (Exception ex) {
                    log.error(ex.getMessage());
                }
            }
        }
    }


    /**
     * 返送消息给web前端
     *
     * @param message 消息体
     */
    public void sendMessageToWeb(Object message) {
        log.debug("sendMessageToWeb message:{}", JsonUtil.toJson(message));
        try {
            batchSendMessage(JSON.toJSONString(message, SerializerFeature.WriteMapNullValue));
            log.debug("sendMessageToWeb message success");
        } catch (Exception exception) {
            log.error("sendMessageToWeb fail:{}", ExceptionUtil.getStackTrace(exception));
        }
    }

}
