package net.pingfang.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpDelete;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpPut;
import org.apache.http.config.SocketConfig;
import org.apache.http.conn.ClientConnectionManager;
import org.apache.http.conn.scheme.Scheme;
import org.apache.http.conn.scheme.SchemeRegistry;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.conn.ssl.TrustSelfSignedStrategy;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.ssl.SSLContexts;

public class HttpUtils {

	public static HttpResponse doGet(String host, String path, Map<String, String> headers, Map<String, String> querys)
			throws Exception {
		HttpClient httpClient = wrapClient(host);

		HttpGet request = new HttpGet(buildUrl(host, path, querys));
		for (Entry e : headers.entrySet()) {
			request.addHeader((String) e.getKey(), (String) e.getValue());
		}

		return httpClient.execute(request);
	}

	public static HttpResponse doPost(String host, String path, Map<String, String> headers, Map<String, String> querys,
									  Map<String, String> bodys) throws Exception {
		HttpClient httpClient = wrapClient(host);

		HttpPost request = new HttpPost(buildUrl(host, path, querys));
		Entry e;
		for (Iterator localIterator = headers.entrySet().iterator(); localIterator.hasNext();) {
			e = (Entry) localIterator.next();
			request.addHeader((String) e.getKey(), (String) e.getValue());
		}

		if (bodys != null) {
			Object nameValuePairList = new ArrayList();

			for (String key : bodys.keySet()) {
				((List) nameValuePairList).add(new BasicNameValuePair(key, (String) bodys.get(key)));
			}
			UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity((List) nameValuePairList, "utf-8");
			formEntity.setContentType("application/x-www-form-urlencoded; charset=UTF-8");
			request.setEntity(formEntity);
		}

		return (HttpResponse) httpClient.execute(request);
	}

	public static HttpResponse doPost(String host, String path, Map<String, String> headers, Map<String, String> querys,
									  String body) throws Exception {
		HttpClient httpClient = wrapClient(host);

		HttpPost request = new HttpPost(buildUrl(host, path, querys));
		for (Entry e : headers.entrySet()) {
			request.addHeader((String) e.getKey(), (String) e.getValue());
		}

		if (StringUtils.isNotBlank(body)) {
			request.setEntity(new StringEntity(body, "utf-8"));
		}

		return httpClient.execute(request);
	}

	public static HttpResponse doPost(String host, String path, String method, Map<String, String> headers,
									  Map<String, String> querys, byte[] body) throws Exception {
		HttpClient httpClient = wrapClient(host);

		HttpPost request = new HttpPost(buildUrl(host, path, querys));
		for (Entry e : headers.entrySet()) {
			request.addHeader((String) e.getKey(), (String) e.getValue());
		}

		if (body != null) {
			request.setEntity(new ByteArrayEntity(body));
		}

		return httpClient.execute(request);
	}

	public static HttpResponse doPost(String host, String path, Map<String, String> headers, Map<String, String> querys,
									  MultipartEntityBuilder builder) throws Exception {
		HttpClient httpClient = wrapClient(host);

		HttpPost request = new HttpPost(buildUrl(host, path, querys));
		for (Entry e : headers.entrySet()) {
			request.addHeader((String) e.getKey(), (String) e.getValue());
		}
		// 构建 HttpEntity
		if (builder!=null) {
			HttpEntity multipart = builder.build();
			request.setEntity(multipart);
		}

		return httpClient.execute(request);
	}

	public static HttpResponse doPut(String host, String path, String method, Map<String, String> headers,
									 Map<String, String> querys, String body) throws Exception {
		HttpClient httpClient = wrapClient(host);

		HttpPut request = new HttpPut(buildUrl(host, path, querys));
		for (Entry e : headers.entrySet()) {
			request.addHeader((String) e.getKey(), (String) e.getValue());
		}

		if (StringUtils.isNotBlank(body)) {
			request.setEntity(new StringEntity(body, "utf-8"));
		}

		return httpClient.execute(request);
	}

	public static HttpResponse doPut(String host, String path, String method, Map<String, String> headers,
									 Map<String, String> querys, byte[] body) throws Exception {
		HttpClient httpClient = wrapClient(host);

		HttpPut request = new HttpPut(buildUrl(host, path, querys));
		for (Entry e : headers.entrySet()) {
			request.addHeader((String) e.getKey(), (String) e.getValue());
		}

		if (body != null) {
			request.setEntity(new ByteArrayEntity(body));
		}

		return httpClient.execute(request);
	}

	public static HttpResponse doDelete(String host, String path, String method, Map<String, String> headers,
										Map<String, String> querys) throws Exception {
		HttpClient httpClient = wrapClient(host);

		HttpDelete request = new HttpDelete(buildUrl(host, path, querys));
		for (Entry e : headers.entrySet()) {
			request.addHeader((String) e.getKey(), (String) e.getValue());
		}

		return httpClient.execute(request);
	}

	private static String buildUrl(String host, String path, Map<String, String> querys)
			throws UnsupportedEncodingException {
		StringBuilder sbUrl = new StringBuilder();
		sbUrl.append(host);
		if (!StringUtils.isBlank(path)) {
			sbUrl.append(path);
		}
		if (null != querys) {
			StringBuilder sbQuery = new StringBuilder();
			for (Entry query : querys.entrySet()) {
				if (0 < sbQuery.length()) {
					sbQuery.append("&");
				}
				if ((StringUtils.isBlank((CharSequence) query.getKey()))
						&& (!StringUtils.isBlank((CharSequence) query.getValue()))) {
					sbQuery.append((String) query.getValue());
				}
				if (!StringUtils.isBlank((CharSequence) query.getKey())) {
					sbQuery.append((String) query.getKey());
					if (!StringUtils.isBlank((CharSequence) query.getValue())) {
						sbQuery.append("=");
						sbQuery.append(URLEncoder.encode((String) query.getValue(), "utf-8"));
					}
				}
			}
			if (0 < sbQuery.length()) {
				sbUrl.append("?").append(sbQuery);
			}
		}

		return sbUrl.toString();
	}

	private static HttpClient wrapClient(String host) {
		HttpClient httpClient = null;
		if (host.startsWith("https://")) {
			try {
				SSLContextBuilder sslContextBuilder = SSLContexts.custom()
						.loadTrustMaterial(new TrustSelfSignedStrategy());
				httpClient = HttpClients.custom()
						.setSSLContext(sslContextBuilder.build())
						.setSSLHostnameVerifier(NoopHostnameVerifier.INSTANCE)
						.setDefaultSocketConfig(SocketConfig.custom().setSoKeepAlive(true).setSoTimeout(300000).build())
						.build();
			} catch (NoSuchAlgorithmException e) {
				e.printStackTrace();
			} catch (KeyStoreException e) {
				e.printStackTrace();
			} catch (KeyManagementException e) {
				e.printStackTrace();
			}
		}else {
			httpClient = HttpClientBuilder.create() //
					.setDefaultSocketConfig(SocketConfig.custom().setSoKeepAlive(true).setSoTimeout(300000).build())
					.build();
		}

		return httpClient;
	}

	private static void sslClient(HttpClient httpClient) {
		try {


			SSLContext ctx = SSLContext.getInstance("TLS");
			X509TrustManager tm = new X509TrustManager() {
				public X509Certificate[] getAcceptedIssuers() {
					return null;
				}

				public void checkClientTrusted(X509Certificate[] xcs, String str) {
				}

				public void checkServerTrusted(X509Certificate[] xcs, String str) {
				}
			};
			ctx.init(null, new TrustManager[] { tm }, null);
			SSLSocketFactory ssf = new SSLSocketFactory(ctx);
			ssf.setHostnameVerifier(SSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
			ClientConnectionManager ccm = httpClient.getConnectionManager();
			SchemeRegistry registry = ccm.getSchemeRegistry();
			registry.register(new Scheme("https", 443, ssf));
		} catch (KeyManagementException | NoSuchAlgorithmException ex) {
			throw new RuntimeException(ex);
		}
	}
}
