package net.pingfang.util;

/**
 * @Author: CM
 * @Date: 2022/3/24 19:12
 * @Description: 分页工具类
 */
public class PageUtil {
    /**
     * 获取数据起始索引
     *
     * @param currentPage 当前页
     * @param pageSize    分页数量
     * @return 起始索引
     */
    public static int getStartIndex(int currentPage, int pageSize) {
        int startIndex = (currentPage - 1) * pageSize;
        return Math.max(startIndex, 0);
    }
}
