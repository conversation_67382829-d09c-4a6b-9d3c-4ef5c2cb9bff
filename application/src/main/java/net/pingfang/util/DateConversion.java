package net.pingfang.util;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 
 * @title: StringDateToLocalDateTime.java
 * @package net.pingfang.util
 * @description: 字符串日期LocalDateTime互转
 * @author: lzl
 * @date: 2022年5月7日 下午1:51:52
 * @version: 3.0
 */

public class DateConversion {

	public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

	/**
	 * 字符串类型转日期类型
	 * 
	 * @Title: StringToLocalDateTime
	 * @Description: String 日期转LocalDateTime
	 * @param @param  dateStr
	 * @param @return 参数
	 * @return LocalDateTime 返回类型
	 * @throws
	 */
	public static LocalDateTime stringToLocalDateTime(String dateStr) {
		try {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATETIME_FORMAT);
			LocalDateTime localDateTime = LocalDateTime.parse(dateStr, formatter);
			return localDateTime;
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 日期类型转化字符串类型
	 * 
	 * @Title: localDateTimeToString
	 * @Description: LocalDateTime日期转String
	 * @param @param  date
	 * @param @return 参数
	 * @return String 返回类型
	 * @throws
	 */
	public static String localDateTimeToString(LocalDateTime date) {
		try {
			DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATETIME_FORMAT);
			String dateStr = date.format(formatter);
			return dateStr;
		} catch (Exception e) {
			return null;
		}
	}

	/**
	 * 字符串类型转日期类型
	 * 
	  * @Title: stringToDate  
	  * @Description: 方法描述  
	  * @param @param datetime
	  * @param @return 参数  
	  * @return Date 返回类型  
	  * @throws
	 */
	public static Date stringToDate(String datetime) {

		SimpleDateFormat sdf = new SimpleDateFormat(DATETIME_FORMAT);
		Date myDate = null;
		try {
			myDate = sdf.parse(datetime);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return myDate;
	}

	/**
	 *  日期类型转字符串类型
	 * 
	  * @Title: dateToString  
	  * @Description: 方法描述  
	  * @param @param datetime
	  * @param @return 参数  
	  * @return String 返回类型  
	  * @throws
	 */
	public static String dateToString(Date datetime) {

		SimpleDateFormat sdf = new SimpleDateFormat(DATETIME_FORMAT);
		String myDate = null;
		try {
			myDate = sdf.format(datetime);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return myDate;
	}

	public static void main(String[] args) {
		System.out.println(stringToLocalDateTime("2021-09-03 21:00:01"));
		System.out.println(localDateTimeToString(LocalDateTime.now()));
	}

}
