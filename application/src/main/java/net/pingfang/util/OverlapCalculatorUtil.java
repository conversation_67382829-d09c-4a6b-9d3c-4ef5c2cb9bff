package net.pingfang.util;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/8/18
 */
public class OverlapCalculatorUtil {
    public static double calculateOverlapArea(Rectangle rect1, Rectangle rect2) {
        // 计算重叠部分的坐标范围
        double overlapX1 = Math.max(rect1.x1, rect2.x1);
        double overlapY1 = Math.max(rect1.y1, rect2.y1);
        double overlapX2 = Math.min(rect1.x3, rect2.x3);
        double overlapY2 = Math.min(rect1.y3, rect2.y3);

        // 计算重叠的宽度和高度
        double overlapWidth = Math.max(0, overlapX2 - overlapX1);
        double overlapHeight = Math.max(0, overlapY2 - overlapY1);

        // 计算重叠面积
        double overlapArea = overlapWidth * overlapHeight;

        return overlapArea;
    }

    public static double calculateOverlapPercentage(Rectangle rect1, Rectangle rect2) {
        double areaRect1 = calculateRectangleArea(rect1);
        double areaRect2 = calculateRectangleArea(rect2);
        double overlapArea = calculateOverlapArea(rect1, rect2);

        // 计算重叠百分比
        double overlapPercentage = (overlapArea / (areaRect1 + areaRect2 - overlapArea)) * 100;

        return overlapPercentage;
    }

    public static double calculateRectangleArea(Rectangle rect) {
        // 计算矩形面积
        double width = Math.abs(rect.x2 - rect.x1);
        double height = Math.abs(rect.y4 - rect.y1);
        double area = width * height;

        return area;
    }

    public static void main(String[] args) {
        Rectangle rect1 = new Rectangle(0,0,100,0,100,100,0,100);

        // 设置rect1的顶点坐标

        Rectangle rect2 = new Rectangle(50,0,100,0,100,100,50,100);
        // 设置rect2的顶点坐标

        double overlapArea = calculateOverlapArea(rect1, rect2);
        System.out.println("重叠面积：" + overlapArea);

        double overlapPercentage = calculateOverlapPercentage(rect1, rect2);
        System.out.println("重叠百分比：" + overlapPercentage + "%");
    }
}

