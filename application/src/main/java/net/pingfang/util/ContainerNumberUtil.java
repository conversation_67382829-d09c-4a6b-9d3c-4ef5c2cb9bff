package net.pingfang.util;

import org.apache.commons.lang3.StringUtils;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/8/15
 */
public class ContainerNumberUtil {

    public static void main(String[] args) {
        System.out.println(verifyCntrCode("TCNU6094912"));
    }

    public static boolean verifyCntrCode(String cntrNo ){

        if (StringUtils.isEmpty(cntrNo)) {
            return false;
        }
        int length = cntrNo.length();
        if (length != 11) {
            return false;

        }
        String sub1 = StringUtils.substring(cntrNo, 0, 4);
        if (!sub1.matches("[A-Z]+")) {
            return false;

        }
        String sub2 = StringUtils.substring(cntrNo, 4, length-1);
        if(!sub2.matches("[0-9]*")){
            return false;

        }
        String sub3 = StringUtils.substring(cntrNo,  length-1, length);
        if(!sub3.matches("[0-9]*")){
            return false;
        }
        //定义一个字符串，集装箱编号的每个字符必定出自这串字符，其中11，22，33用？代替，获取对应值，满足0-38
        String charCode = "0123456789A?BCDEFGHIJK?LMNOPQRSTU?VWXYZ";
        char[] codeChars = cntrNo.toCharArray();//将接收到的字符串转换为字符数组，方便后续遍历
        int num = 0;//初始化用于规则计算后累计的变量，用于计算结束后%第11位

        for (int i = 0; i < 10; i++) {
            int idx = charCode.indexOf(codeChars[i]);//遍历取出第i个字符，并从charCode字符串中查找这个字符所在位置,这个位置的值就是对应值
            if (idx == -1 || charCode.charAt(idx)=='?') {//如果查找不到或者找到替代符号（？）一样，那么这个字符不是规则中所要求的字符，需要校验的字符串不符合集装箱号的规则，退出循环
                return false;
            }
            idx =  (int) (idx * Math.pow(2, i));//这里的意思应该是说对应值*2的（n-1）次方;原文表达（2、第N位的箱号对应值再分别乘以2N－1 （N＝1，2，3………..10））是误导的关键
            num += idx;//累加
        }
        num = (num % 11) % 10;//取模11

        if(Integer.parseInt(String.valueOf(codeChars[10]))!= num){
            return false;
        }
        return true;
    }


}
