package net.pingfang.util;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;

/**
 * 创建RTSP预识别结果表的工具类（小写表名）
 * 运行此类来创建数据库表
 */
public class CreateRtspTable {
    
    private static final String DB_URL = "********************************************************************";
    private static final String DB_USER = "postgres";
    private static final String DB_PASSWORD = "pfkj2016";
    
    public static void main(String[] args) {
        try {
            // 加载PostgreSQL驱动
            Class.forName("org.postgresql.Driver");
            
            // 连接数据库
            Connection conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            Statement stmt = conn.createStatement();
            
            System.out.println("连接数据库成功！");
            
            // 删除表（如果存在）
            String dropTableSQL1 = "DROP TABLE IF EXISTS \"T_RTSP_PRE_RECOGNITION_RESULT\"";
            String dropTableSQL2 = "DROP TABLE IF EXISTS t_rtsp_pre_recognition_result";
            stmt.execute(dropTableSQL1);
            stmt.execute(dropTableSQL2);
            System.out.println("删除旧表完成");

            // 创建表（小写，不使用双引号）
            String createTableSQL = "CREATE TABLE t_rtsp_pre_recognition_result (" +
                "id BIGSERIAL PRIMARY KEY," +
                "seq_no VARCHAR(50) NOT NULL," +
                "crane_no VARCHAR(20) NOT NULL," +
                "status VARCHAR(20) NOT NULL DEFAULT 'PROCESSING'," +
                "container_number VARCHAR(20)," +
                "confidence REAL," +
                "image_count INTEGER DEFAULT 0," +
                "task_status INTEGER," +
                "work_type INTEGER," +
                "container_height REAL," +
                "position_difference REAL," +
                "start_time TIMESTAMP," +
                "end_time TIMESTAMP," +
                "duration BIGINT," +
                "error_message VARCHAR(500)," +
                "all_camera_images TEXT," +
                "best_camera_id INTEGER," +
                "best_image_path VARCHAR(500)," +
                "best_image_url VARCHAR(500)," +
                "create_by VARCHAR(50)," +
                "create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP," +
                "update_by VARCHAR(50)," +
                "update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ")";
            
            stmt.execute(createTableSQL);
            System.out.println("创建表完成");
            
            // 创建索引（小写表名）
            String[] indexSQLs = {
                "CREATE INDEX idx_rtsp_result_seq_no ON t_rtsp_pre_recognition_result(seq_no)",
                "CREATE INDEX idx_rtsp_result_crane_no ON t_rtsp_pre_recognition_result(crane_no)",
                "CREATE INDEX idx_rtsp_result_status ON t_rtsp_pre_recognition_result(status)",
                "CREATE INDEX idx_rtsp_result_create_time ON t_rtsp_pre_recognition_result(create_time)",
                "CREATE INDEX idx_rtsp_result_container_number ON t_rtsp_pre_recognition_result(container_number)"
            };

            for (String indexSQL : indexSQLs) {
                stmt.execute(indexSQL);
            }
            System.out.println("创建索引完成");

            // 创建触发器函数
            String triggerFunctionSQL = "CREATE OR REPLACE FUNCTION update_updated_time_column() " +
                "RETURNS TRIGGER AS $$ " +
                "BEGIN " +
                "NEW.update_time = CURRENT_TIMESTAMP; " +
                "RETURN NEW; " +
                "END; " +
                "$$ language 'plpgsql'";

            stmt.execute(triggerFunctionSQL);
            System.out.println("创建触发器函数完成");

            // 创建触发器（小写表名）
            String triggerSQL = "CREATE TRIGGER update_rtsp_result_updated_time " +
                "BEFORE UPDATE ON t_rtsp_pre_recognition_result " +
                "FOR EACH ROW EXECUTE FUNCTION update_updated_time_column()";

            stmt.execute(triggerSQL);
            System.out.println("创建触发器完成");
            
            // 关闭连接
            stmt.close();
            conn.close();
            
            System.out.println("✅ RTSP预识别结果表创建完成！");
            
        } catch (Exception e) {
            System.err.println("❌ 创建表失败：" + e.getMessage());
            e.printStackTrace();
        }
    }
}
