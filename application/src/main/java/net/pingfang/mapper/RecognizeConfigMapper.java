package net.pingfang.mapper;

import net.pingfang.model.dto.QueryRecognizeConfigResDTO;
import net.pingfang.model.entity.RecognizeConfig;
import net.pingfang.model.entity.Record;
import net.pingfang.model.vo.business.RecognizeConfigVO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/8/17
 */
@Mapper
public interface RecognizeConfigMapper {

    List<QueryRecognizeConfigResDTO> queryByLaneId(@Param("laneId") Long laneId);

    void deleteExId(@Param("idList") List<Long> idList);

    void deleteById(@Param("id") Long id);

    void addBatch(@Param("dataList") List<RecognizeConfig> dataList);

    void updatePreset(@Param("laneId") Long laneId, @Param("cameraId") Long cameraId, @Param("type") Integer type);

    List<RecognizeConfig> queryPresetConfig(@Param("laneId") Long laneId, @Param("cameraId") Long cameraId);

    void add(@Param("param") RecognizeConfig param);

    void updateById(@Param("param") RecognizeConfig param);

    List<RecognizeConfigVO> queryWorkConfigByType(@Param("laneId") Long laneId, @Param("type") Integer type);

    Record queryRecordBySeqNo(@Param("seqNo") String seqNo);

    RecognizeConfig queryConfig(@Param("laneId") Long laneId, @Param("cameraId") Long cameraId, @Param("type") Integer type);

    List<RecognizeConfigVO> queryAll();

}
