package net.pingfang.mapper;

import net.pingfang.model.entity.Img;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @since 2023-07-19 11:40
 */
@Mapper
public interface ImgMapper {

    int insertBatch(@Param("list") List<Img> list);

    List<Img> queryByRecordIds(@Param("list")List<Long> ids);

    List<Img> queryBySeqNo(@Param("seqNo") String seqNo);

    void insert(@Param("item") Img img);

    List<Img> queryBySeqNoAndType(@Param("seqNo") String seqNo, @Param("type") Integer type);

    List<String> queryUrlByCameraKeyWord(@Param("cameraKeyWord")String cameraKeyWord);

    /**
     * 根据ID删除图片记录
     * @param id 图片ID
     */
    void deleteById(@Param("id") Long id);
}
