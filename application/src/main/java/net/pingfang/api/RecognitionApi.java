package net.pingfang.api;

import net.pingfang.model.annotation.Log;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.*;
import net.pingfang.model.entity.Img;
import net.pingfang.service.RecognitionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 识别相关API
 *
 * @author: CM
 * @date: 2023/6/5
 */
@RestController
@RequestMapping("/recognize")
public class RecognitionApi {

    @Autowired
    private RecognitionService recognitionService;

    /**
     * 手动检测(开始/结束)
     * @return
     */
    @PostMapping("/manual-recognize")
    @Log(2)
    public Result<String> manualRecognize(@RequestBody ManualRecognizeDTO param){
        return recognitionService.manualRecognize(param);
    }

    /**
     * 手动检测箱号
     * @return
     */
    @PostMapping("/manual-recognize-ctn")
    @Log(2)
    public Result<String> manualRecognizeCtn(@RequestParam(value = "cameraId", required = false) Long cameraId){
        return recognitionService.manualRecognizeCtn(cameraId);
    }

    /**
     * 手动检测车顶号
     * @return
     */
    @PostMapping("/manual-recognize-top")
    @Log(2)
    public Result<String> manualRecognizeTop(@RequestParam(value = "cameraId", required = false) Long cameraId){
        return recognitionService.manualRecognizeTop(cameraId);
    }

    /**
     * 分页查询历史记录
     * @param param 入参
     * @return
     */
    @PostMapping("/query-record")
    public Result<QueryRecordResDTO> queryRecord(@RequestBody QueryRecordReqDTO param){
        return recognitionService.queryRecord(param);
    }

    /**
     * 复制t_img表中的相机名称里包含特定关键字的图片到picture.path（如D:/PFKJ/IMAGE/）下的指定路径下
     *
     */
    @PostMapping("/copyImagesByKeywordToAbstractPath")
    public Result<Integer> copyImagesByKeywordToAbstractPath(String cameraKeyWord,String abstractPath){
        return recognitionService.copyImagesByCameraKeyWordToAbstractPath(cameraKeyWord,abstractPath);
    }

    /**
     * 根据seqNo查询图片数据
     * @param seqNo 入参
     * @return
     */
    @GetMapping("/query-img")
    public Result<List<Img>> queryImg(@RequestParam("seqNo") String seqNo){
        return recognitionService.queryImg(seqNo);
    }

    /**
     * 手动重传
     * @param param 入参
     * @return
     */
    @PostMapping("/manual-upload")
    @Log(2)
    public Result<String> manualUpload(@RequestBody ManualUploadReqDTO param){
        return recognitionService.manualUpload(param);
    }

    /**
     * 图片/视频下载
     * @param param
     */
    @PostMapping("/download")
    public void download(@RequestBody DownloadReqDTO param,HttpServletResponse response){
        recognitionService.download(param,response);
    }

    /**
     * 记录报表导出
     * @param param
     */
    @PostMapping("/export")
    public void export(@RequestBody QueryRecordReqDTO param, HttpServletResponse response){
        recognitionService.export(param,response);
    }

}
