package net.pingfang.api;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.core.algorithm.ailib.TopPlateAlgorithmService;
import net.pingfang.model.common.Result;
import net.pingfang.model.entity.Version;
import net.pingfang.service.VersionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 版本信息相关API
 *
 * @author: CM
 * @date: 2023/6/5
 */
@RestController
@RequestMapping("/version")
@Slf4j
public class VersionApi {
    @Autowired
    private VersionService versionService;

    @Autowired
    private TopPlateAlgorithmService topPlateAlgorithmService;

    @Value("${truck.work.lane.no}")
    private String truckWorkLaneNo;

    /**
     * 查询软件及算法版本信息
     * @return
     */
    @GetMapping("/query")
    public Result<List<Version>> query(){
        return versionService.query();
    }

    @GetMapping("/test")
    public String test( ){
//        String filePath = "D:/PFKJ/AILib_new_1/imgs_cdh/20240801132004751.jpg";
//        // 检测
//        DetectResults detectResults = topPlateAlgorithmService.detPicture(filePath);
//        log.info("获取车顶号算法检测结果：{}，图片路径为：{}",  JsonUtil.toJson(detectResults),filePath);
//
//        if (detectResults == null || detectResults.det_num == 0) {
//            log.info("识别任务:{},摄像头:{}未获取到车顶号算法检测结果");
//        }
//
//        // 识别
//        RecTopPlate recTopPlate = topPlateAlgorithmService.recPicture(filePath, detectResults);
//        if (recTopPlate != null && !StringUtils.isEmpty(recTopPlate.getTopPlate())) {
//           //  String topPlate = recTopPlate.getTopPlate();
//           // log.info("识别的车顶号：{}获取到车顶号算法检测结果：{}",topPlate, recTopPlate);
//        }
//        return recTopPlate.getTopPlate()==null ? "未识别到" : recTopPlate.getTopPlate();
        System.out.println(truckWorkLaneNo.contains("堆箱区1-1"));
        return truckWorkLaneNo;
    }
}
