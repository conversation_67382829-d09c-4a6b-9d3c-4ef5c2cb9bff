package net.pingfang.api;

import net.pingfang.model.annotation.Log;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.CameraControlDTO;
import net.pingfang.model.dto.CameraPrePositionControlDTO;
import net.pingfang.model.dto.CamersToneDTO;
import net.pingfang.model.entity.Camera;
import net.pingfang.model.entity.RecognizeConfig;
import net.pingfang.service.CameraService;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 相机相关API
 *
 * @author: CM
 * @date: 2023/6/5
 */
@RestController
@RequestMapping("/camera")
public class CameraApi {
    @Autowired
    private CameraService cameraService;

    /**
     * 查询全部
     *
     * @return
     */
    @GetMapping("/query-all")
    public Result<List<Camera>> queryAll() {
        return cameraService.queryAll();
    }

    /**
     * 新增
     *
     * @param camera 入参
     * @return
     */
    @Log(2)
    @PostMapping("/add")
    public Result<String> add(@RequestBody Camera camera) {
        return cameraService.add(camera);
    }

    /**
     * 更新
     *
     * @param camera 入参
     * @return
     */
    @Log(2)
    @PostMapping("/update")
    public Result<String> update(@RequestBody Camera camera) {
        return cameraService.update(camera);
    }

    /**
     * 删除
     *
     * @param id 主键
     * @return
     */
    @Log(2)
    @GetMapping("/delete")
    public Result<String> delete(@RequestParam("id") Long id) {
        return cameraService.delete(id);
    }

    /**
     * 相机云台控制
     *
     * @param param 入参
     * @return
     */
    @Log(2)
    @PostMapping("/control")
    public Result<String> control(@RequestBody CameraControlDTO param) {
        return cameraService.control(param);
    }

    /**
     * 预置位控制
     *
     * @param param 入参
     * @return
     */
    @PostMapping("/control-pre-position")
    @Log(2)
    public Result<String> controlPrePosition(@RequestBody CameraPrePositionControlDTO param) {
        return cameraService.controlPrePosition(param);
    }

    /**
     * 查询相机涉及的预置位配置下拉
     *
     * @param laneId   车道id
     * @param cameraId 相机id
     * @return
     */
    @GetMapping("/query-preset-config")
    public Result<List<RecognizeConfig>> queryPresetConfig(@RequestParam("laneId") Long laneId,
                                                           @RequestParam("cameraId") Long cameraId) {
        return cameraService.queryPresetConfig(laneId, cameraId);
    }


    /**
     * 相机重启
     *
     * @param id 主键id
     * @return
     */
    @GetMapping("/restart")
    @Log(2)
    public Result<String> restart(@RequestParam("id") Long id) {
        return cameraService.restart(id);
    }

    /**
     * 相机色调
     *
     * @param param
     * @return
     */
    @PostMapping("/tone")
    @Log(2)
    public Result<String> tone(@RequestBody CamersToneDTO param) {
        return cameraService.tone(param);
    }

}
