package net.pingfang.test;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.core.hardware.rtsp.RtspService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.lang.reflect.Field;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * RTSP线程池测试控制器
 * 用于测试线程池异常场景和修复效果
 * 
 * <AUTHOR>
 * @date 2025-07-23
 */
@RestController
@RequestMapping("/test/rtsp-threadpool")
@Slf4j
public class RtspThreadPoolTestController {

    @Autowired
    private RtspService rtspService;

    /**
     * 测试1：模拟线程池被意外关闭的场景
     * 这会触发之前版本的问题：任务提交失败但没有适当处理
     */
    @PostMapping("/simulate-shutdown")
    public String simulateThreadPoolShutdown() {
        try {
            log.info("=== 开始模拟线程池意外关闭场景 ===");
            
            // 通过反射获取RtspService的executor
            Field executorField = RtspService.class.getDeclaredField("executor");
            executorField.setAccessible(true);
            ExecutorService executor = (ExecutorService) executorField.get(rtspService);
            
            // 检查线程池当前状态
            log.info("关闭前线程池状态 - isShutdown: {}, isTerminated: {}", 
                    executor.isShutdown(), executor.isTerminated());
            
            // 模拟意外关闭线程池
            executor.shutdown();
            log.warn("⚠️ 已模拟线程池意外关闭");
            
            // 等待一下确保关闭完成
            Thread.sleep(1000);
            
            // 检查关闭后状态
            log.info("关闭后线程池状态 - isShutdown: {}, isTerminated: {}", 
                    executor.isShutdown(), executor.isTerminated());
            
            return "线程池已模拟关闭，现在可以测试RTSP抓拍功能";
            
        } catch (Exception e) {
            log.error("模拟线程池关闭失败", e);
            return "模拟失败: " + e.getMessage();
        }
    }

    /**
     * 测试2：强制关闭线程池（更严重的场景）
     */
    @PostMapping("/force-shutdown")
    public String forceShutdownThreadPool() {
        try {
            log.info("=== 开始强制关闭线程池场景 ===");
            
            Field executorField = RtspService.class.getDeclaredField("executor");
            executorField.setAccessible(true);
            ExecutorService executor = (ExecutorService) executorField.get(rtspService);
            
            log.info("强制关闭前线程池状态 - isShutdown: {}, isTerminated: {}", 
                    executor.isShutdown(), executor.isTerminated());
            
            // 强制立即关闭
            executor.shutdownNow();
            log.warn("⚠️ 已强制关闭线程池");
            
            Thread.sleep(500);
            
            log.info("强制关闭后线程池状态 - isShutdown: {}, isTerminated: {}", 
                    executor.isShutdown(), executor.isTerminated());
            
            return "线程池已强制关闭，现在可以测试RTSP抓拍功能";
            
        } catch (Exception e) {
            log.error("强制关闭线程池失败", e);
            return "强制关闭失败: " + e.getMessage();
        }
    }

    /**
     * 测试3：检查线程池状态
     */
    @GetMapping("/check-status")
    public String checkThreadPoolStatus() {
        try {
            Field executorField = RtspService.class.getDeclaredField("executor");
            executorField.setAccessible(true);
            ExecutorService executor = (ExecutorService) executorField.get(rtspService);
            
            boolean isShutdown = executor.isShutdown();
            boolean isTerminated = executor.isTerminated();
            
            log.info("当前线程池状态 - isShutdown: {}, isTerminated: {}", isShutdown, isTerminated);
            
            String status = String.format("线程池状态: isShutdown=%s, isTerminated=%s", isShutdown, isTerminated);
            
            if (isShutdown) {
                status += " ⚠️ 线程池已关闭，任务提交将失败";
            } else {
                status += " ✅ 线程池正常运行";
            }
            
            return status;
            
        } catch (Exception e) {
            log.error("检查线程池状态失败", e);
            return "检查失败: " + e.getMessage();
        }
    }

    /**
     * 测试4：重新初始化线程池（模拟修复）
     */
    @PostMapping("/reinitialize")
    public String reinitializeThreadPool() {
        try {
            log.info("=== 开始重新初始化线程池 ===");
            
            Field executorField = RtspService.class.getDeclaredField("executor");
            executorField.setAccessible(true);
            
            // 创建新的线程池
            ExecutorService newExecutor = java.util.concurrent.Executors.newFixedThreadPool(4);
            executorField.set(rtspService, newExecutor);
            
            log.info("✅ 线程池已重新初始化");
            
            return "线程池已重新初始化，现在应该可以正常工作";
            
        } catch (Exception e) {
            log.error("重新初始化线程池失败", e);
            return "重新初始化失败: " + e.getMessage();
        }
    }

    /**
     * 测试5：启动RTSP抓拍（用于验证修复效果）
     */
    @PostMapping("/start-rtsp")
    public String startRtspCapture() {
        try {
            log.info("=== 开始测试RTSP抓拍启动 ===");
            
            String result = rtspService.start();
            
            log.info("RTSP启动结果: {}", result);
            
            return "RTSP启动结果: " + result;
            
        } catch (Exception e) {
            log.error("启动RTSP抓拍失败", e);
            return "启动失败: " + e.getMessage();
        }
    }

    /**
     * 测试6：停止RTSP抓拍
     */
    @PostMapping("/stop-rtsp")
    public String stopRtspCapture() {
        try {
            log.info("=== 开始停止RTSP抓拍 ===");
            
            String result = rtspService.end();
            
            log.info("RTSP停止结果: {}", result);
            
            return "RTSP停止结果: " + result;
            
        } catch (Exception e) {
            log.error("停止RTSP抓拍失败", e);
            return "停止失败: " + e.getMessage();
        }
    }

    /**
     * 测试7：模拟应用重启场景
     */
    @PostMapping("/simulate-restart")
    public String simulateApplicationRestart() {
        try {
            log.info("=== 模拟应用重启场景 ===");
            
            // 1. 先启动RTSP
            log.info("步骤1: 启动RTSP抓拍");
            rtspService.start();
            Thread.sleep(2000);
            
            // 2. 模拟应用关闭时的线程池关闭
            log.info("步骤2: 模拟应用关闭");
            Field executorField = RtspService.class.getDeclaredField("executor");
            executorField.setAccessible(true);
            ExecutorService executor = (ExecutorService) executorField.get(rtspService);
            executor.shutdown();
            
            // 3. 等待一段时间
            Thread.sleep(1000);
            
            // 4. 尝试再次启动（这会触发问题）
            log.info("步骤3: 尝试重新启动RTSP");
            String result = rtspService.start();
            
            return "模拟重启完成，结果: " + result;
            
        } catch (Exception e) {
            log.error("模拟应用重启失败", e);
            return "模拟重启失败: " + e.getMessage();
        }
    }

    /**
     * 测试8：压力测试 - 快速启停
     */
    @PostMapping("/stress-test")
    public String stressTest(@RequestParam(defaultValue = "5") int cycles) {
        try {
            log.info("=== 开始压力测试 - {} 个周期 ===", cycles);
            
            for (int i = 1; i <= cycles; i++) {
                log.info("压力测试周期 {}/{}", i, cycles);
                
                // 启动
                rtspService.start();
                Thread.sleep(1000);
                
                // 停止
                rtspService.end();
                Thread.sleep(500);
            }
            
            return String.format("压力测试完成 - %d个周期", cycles);
            
        } catch (Exception e) {
            log.error("压力测试失败", e);
            return "压力测试失败: " + e.getMessage();
        }
    }
}
