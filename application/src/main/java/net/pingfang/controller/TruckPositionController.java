package net.pingfang.controller;

import lombok.extern.slf4j.Slf4j;
import net.pingfang.service.impl.TruckTopRecognitionManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 集卡到位信号接收控制器
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/ecs")
public class TruckPositionController {

    @Autowired
    private TruckTopRecognitionManager truckTopRecognitionManager;

    /**
     * 接收ECS集卡到位信号
     * 接口路径：/ecs/receiveTruckInPlate
     * 方法：POST
     * 入参：{"craneNo": "TRMG01"}
     * 返回：{"code": 2000, "msg": "success", "data": {}}
     *
     * @param request 请求参数，包含craneNo
     * @return 响应结果
     */
    @PostMapping("/receiveTruckInPlate")
    public ResponseEntity<Map<String, Object>> receiveTruckInPlate(@RequestBody Map<String, String> request) {
        Map<String, Object> response = new HashMap<>();

        try {
            String craneNo = request.get("craneNo");

            if (craneNo == null || craneNo.trim().isEmpty()) {
                log.warn("接收到集卡到位信号，但craneNo为空");
                response.put("code", 4000);
                response.put("msg", "craneNo不能为空");
                response.put("data", new HashMap<>());
                return ResponseEntity.badRequest().body(response);
            }

            log.info("接收到ECS集卡到位信号 - 门吊号: {}", craneNo);

            // 启动车顶号识别
            boolean started = truckTopRecognitionManager.startTruckTopRecognition(craneNo);

            if (started) {
                response.put("code", 2000);
                response.put("msg", "success");
                response.put("data", new HashMap<>());
                log.info("车顶号识别已启动 - 门吊号: {}", craneNo);
            } else {
                response.put("code", 5000);
                response.put("msg", "启动车顶号识别失败");
                response.put("data", new HashMap<>());
                log.error("启动车顶号识别失败 - 门吊号: {}", craneNo);
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("处理集卡到位信号异常", e);
            response.put("code", 5000);
            response.put("msg", "服务器内部错误: " + e.getMessage());
            response.put("data", new HashMap<>());
            return ResponseEntity.status(500).body(response);
        }
    }



    /**
     * 健康检查接口
     * @return 服务状态
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("code", 2000);
        response.put("msg", "服务正常");
        Map<String, Object> data = new HashMap<>();
        data.put("service", "truck-position-service");
        data.put("status", "running");
        response.put("data", data);
        return ResponseEntity.ok(response);
    }
}
