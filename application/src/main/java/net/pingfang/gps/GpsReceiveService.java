package net.pingfang.gps;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.Socket;
import net.pingfang.core.flow.CoreFlow;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @CreateTime 2023/7/26 17:53
 * @Description
 */
@Slf4j
@Component
public class GpsReceiveService implements CommandLineRunner {

    @Value("${gps.transfer.ip:*************}")
    private String gpsTransferIp;
    @Value("${gps.transfer.port:12347}")
    private Integer gpsTransferPort;
    @Value("${gps.imei}")
    private String imei;

    public static Socket socket;

    @Value("${gps.receive.open:true}")
    private Boolean gpsReceiveOpen;

    private static final int BUFFER_SIZE = 1024;


    @Override
    public void run(String... args) throws Exception {
        if (!gpsReceiveOpen) {
            return;
        }

        while (true) {
            try (Socket socket = new Socket(gpsTransferIp, gpsTransferPort);
                 InputStream in = socket.getInputStream();
                 OutputStream out = socket.getOutputStream()) {

                byte[] buffer = new byte[BUFFER_SIZE];

                out.write(("PF_SERVER_" + imei).getBytes());
                out.flush();
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    String serverMessage = new String(buffer, 0, bytesRead);
                    log.debug("接收到GPS消息:{} " , serverMessage);
                    CoreFlow.gpsData = serverMessage;
                }
            } catch (IOException ex) {
                CoreFlow.gpsData = null;
                log.error("Connection lost, retrying...");
                try {
                    Thread.sleep(5000);
                } catch (InterruptedException e) {
                    CoreFlow.gpsData = null;
                    Thread.currentThread().interrupt();
                }
            }
        }
    }
}
