# RTSP预识别流程中的车顶号抓拍功能

## 功能概述

在RTSP预识别流程中，根据特定的作业条件自动触发车顶号抓拍功能。这个功能集成在现有的RTSP预识别流程中，当满足特定条件时会自动启动车顶号识别。

## 触发条件

车顶号抓拍需要同时满足以下所有条件：

### 1. 状态为作业中
- **条件**: `taskStatus == 1`
- **说明**: 只有在作业状态下才进行车顶号抓拍

### 2. 作业类型限制
- **条件**: `workType >= 1 && workType <= 8`
- **说明**: 排除9、10、11三种定位类型的作业（这些作业不涉及抓箱子）

### 3. 小车位置处于作业车道
- **条件**: `lane != null && !lane.equals("未知")`
- **说明**: 通过PLC Y坐标查询到的车道信息必须有效

### 4. 吊具距离箱面高度达到预设值
- **条件**: 使用RTSP配置的高度范围进行判断
- **说明**: 高度必须在配置的最小和最大识别高度之间

### 5. 特殊作业类型
只有以下两种作业类型才会触发车顶号抓拍：
- **集卡到堆场**: `SafeWorkTypeEnum.CAR_HEAP.getValue() = 5`
- **集卡到火车**: `SafeWorkTypeEnum.CAR_TRAIN.getValue() = 1`

## 实现逻辑

### 1. 集成点
车顶号抓拍功能集成在 `RtspService.start()` 方法中：

```java
// 检查是否需要进行车顶号抓拍
checkAndStartTruckTopCapture();
```

### 2. 条件检查流程
```java
private void checkAndStartTruckTopCapture() {
    // 1. 获取当前PLC数据
    Plc currentPlc = getCurrentPlcData();
    
    // 2. 检查各项条件
    boolean taskStatusCondition = currentPlc.getTaskStatus() == 1;
    boolean workTypeCondition = currentPlc.getWorkType() >= 1 && currentPlc.getWorkType() <= 8;
    boolean laneCondition = currentPlc.getLane() != null && !currentPlc.getLane().equals("未知");
    boolean heightCondition = checkContainerHeightForTruckTop(currentPlc);
    
    // 3. 检查特殊作业类型
    if (所有条件满足) {
        boolean isSpecialWorkType = (集卡到堆场 || 集卡到火车);
        if (isSpecialWorkType) {
            startTruckTopCaptureInRtsp();
        }
    }
}
```

### 3. 车顶号抓拍执行
```java
private void startTruckTopCaptureInRtsp() {
    // 1. 获取车顶号识别摄像头配置
    List<RecognizeConfigVO> truckTopCameras = recognizeConfigRepository.queryWorkConfigByType(
        null, RecognizeConfigTypeEnum.PLATE_TOP.getValue());
    
    // 2. 异步执行车顶号抓拍
    for (RecognizeConfigVO cameraConfig : truckTopCameras) {
        executor.submit(() -> {
            captureTruckTopInRtsp(cameraConfig);
        });
    }
}
```

### 4. 单个摄像头抓拍流程
```java
private void captureTruckTopInRtsp(RecognizeConfigVO cameraConfig) {
    // 1. 注册摄像头
    // 2. 设置预置位
    // 3. 抓拍图片
    // 4. 识别车顶号
    // 5. 发送结果到ECS（无论是否识别到车顶号）
}
```

### 5. ECS推送功能
```java
private void sendTruckTopResultToEcs(String truckTopNo, String imagePath, RecognizeConfigVO cameraConfig) {
    // 1. 构建EcsIdentifyInfoDTO
    // 2. 设置门吊号、识别类型、车顶号
    // 3. 转换图片路径为URL
    // 4. 异步发送到ECS
}
```

#### ECS推送数据格式：
**有识别结果时：**
```json
{
  "craneNo": "TRMG01",
  "identifyType": 1,
  "identifyTruckRoofNo": "ABC123",
  "imgUrls": [
    "http://10.66.234.156:18882/rtsp/20250709/SEQ20250709143001/truck_top_recognition/truck_top_001_1720512345678.jpg"
  ]
}
```

**无识别结果时：**
```json
{
  "craneNo": "TRMG01",
  "identifyType": 1,
  "identifyTruckRoofNo": null,
  "imgUrls": []
}
```

## 配置要求

### 1. 数据库配置
需要在数据库中配置车顶号识别摄像头：
```sql
-- 查询车顶号识别摄像头配置
SELECT * FROM "T_RECOGNIZE_CONFIG" TRC
INNER JOIN "T_CAMERA" TC ON TC.ID = TRC.CAMERA_ID
WHERE TRC.type = 5; -- RecognizeConfigTypeEnum.PLATE_TOP.getValue()
```

### 2. RTSP配置
在 `application.yml` 中配置RTSP识别高度范围：
```yaml
rtsp:
  recognition:
    minHeight: 5.0  # 最小识别高度（米）
    maxHeight: 7.5  # 最大识别高度（米）
```

### 3. 算法库配置
确保车顶号识别算法库已正确配置：
```properties
# 车顶号识别算法库
dll.topPlate=D:\\PFKJ\\ai\\algorithm\\AILib_vehicle.dll
```

## 图片存储路径

车顶号图片存储在RTSP预识别的子目录中，单独开一个车顶号识别目录：
```
{fileDirPath}/rtsp/{日期}/{seqNo}/truck_top_recognition/truck_top_{摄像头代码}_{时间戳}.jpg
```

示例：
```
D:\PFKJ\Img\rtsp\20250709\SEQ20250709143001\truck_top_recognition\truck_top_001_1720512345678.jpg
```

## 日志监控

### 关键日志信息
```
车顶号抓拍条件检查 - 任务状态:true, 作业类型:true, 车道:true, 高度条件:true
检测到特殊作业类型（集卡到堆场/集卡到火车），启动车顶号抓拍 - 作业类型:5, seqNo:SEQ20250709143001
开始RTSP车顶号抓拍 - seqNo:SEQ20250709143001, 摄像头数量:2
开始车顶号抓拍 - seqNo:SEQ20250709143001, 摄像头:车顶号识别摄像头1
车顶号图片抓拍成功 - seqNo:SEQ20250709143001, 摄像头:车顶号识别摄像头1
识别到车顶号: ABC123 - 图片:D:\PFKJ\Img\rtsp\..., 可信度:0.95
开始发送RTSP车顶号识别结果到ECS - seqNo:SEQ20250709143001, 车顶号:ABC123, 摄像头:车顶号识别摄像头1
RTSP车顶号识别结果发送到ECS成功 - seqNo:SEQ20250709143001, 车顶号:ABC123
RTSP流程中识别到车顶号: ABC123 - seqNo:SEQ20250709143001, 摄像头:车顶号识别摄像头1
```

### 调试日志
```
车顶号抓拍检查 - 无PLC数据
车顶号抓拍条件不满足 - 任务状态:false, 作业类型:true, 车道:true, 高度:true
作业类型3不需要车顶号抓拍
未找到车顶号识别摄像头配置，跳过车顶号抓拍
```

## 注意事项

### 1. PLC数据获取
当前 `getCurrentPlcData()` 方法需要根据实际的PlcCoreService API来实现。可能的实现方式：
- 从PlcCoreService的缓存中获取最新数据
- 通过事件监听机制获取当前PLC状态
- 从数据库查询最新的PLC记录

### 2. 性能考虑
- 车顶号抓拍是异步执行的，不会阻塞RTSP预识别流程
- 每个摄像头的抓拍任务独立执行
- 使用现有的executor线程池

### 3. 错误处理
- 所有异常都有完整的日志记录
- 单个摄像头抓拍失败不会影响其他摄像头
- 车顶号抓拍失败不会影响RTSP预识别的主流程

### 4. 扩展性
- 可以轻松添加更多的触发条件
- 可以扩展支持更多的作业类型
- 识别结果自动推送到ECS系统
- 复用了集卡到位车顶号识别的ECS推送逻辑

### 5. ECS集成
- **复用代码**：使用与TruckTopRecognitionManager相同的ECS推送逻辑
- **数据格式**：与集卡到位车顶号识别保持一致的数据格式
- **异步推送**：不阻塞RTSP预识别主流程
- **错误处理**：完整的ECS推送异常处理和重试机制

## 测试验证

### 1. 功能测试
1. 配置车顶号识别摄像头
2. 设置作业类型为集卡到堆场(5)或集卡到火车(1)
3. 确保PLC数据满足所有触发条件
4. 启动RTSP预识别流程
5. 观察日志确认车顶号抓拍被触发

### 2. 条件测试
分别测试各个条件的边界情况：
- 不同的作业类型
- 不同的高度范围
- 不同的车道状态
- 不同的任务状态

### 3. 性能测试
- 验证车顶号抓拍不影响RTSP预识别性能
- 测试多摄像头并发抓拍的稳定性
- 验证异常情况下的恢复能力
