# 车顶号识别简化逻辑测试文档

## 修改内容总结

### 1. 去除的限制条件
- ❌ 不再检查是否在CoreFlow流程中
- ❌ 不再检查当前活跃的seqNo
- ❌ 不再依赖Redis中的流程状态

### 2. 简化后的处理逻辑
1. **接收ECS命令** → 立即响应处理
2. **获取摄像头配置** → 如果没有配置，发送空结果到ECS
3. **生成任务ID** → 使用时间戳生成唯一任务ID
4. **异步执行识别** → 不阻塞接口响应
5. **发送结果到ECS** → 有结果发结果，没结果发空

### 3. 新增方法
- `executeSimplifiedRecognitionTask()` - 简化的识别任务执行
- `recognizeFromCameraSimplified()` - 简化的摄像头识别
- `captureImageSimplified()` - 简化的图片抓拍
- `sendRecognitionResultToEcsSimplified()` - 简化的ECS推送
- `sendEmptyResultToEcs()` - 发送空结果到ECS
- `convertImagePathToUrl()` - 图片路径转URL

### 4. 接口测试

#### 测试用例1：正常识别流程
```bash
curl -X POST http://localhost:8080/ecs/receiveTruckInPlate \
  -H "Content-Type: application/json" \
  -d '{"craneNo": "TRMG01"}'
```

**预期结果：**
- 返回 `{"code": 2000, "msg": "success"}`
- 启动车顶号识别任务
- 识别到结果后推送到ECS

#### 测试用例2：无摄像头配置
```bash
# 如果数据库中没有车顶号识别摄像头配置
curl -X POST http://localhost:8080/ecs/receiveTruckInPlate \
  -H "Content-Type: application/json" \
  -d '{"craneNo": "TRMG01"}'
```

**预期结果：**
- 返回 `{"code": 2000, "msg": "success"}`
- 立即发送空结果到ECS

#### 测试用例3：识别超时
```bash
# 识别超时（30秒后）
curl -X POST http://localhost:8080/ecs/receiveTruckInPlate \
  -H "Content-Type: application/json" \
  -d '{"craneNo": "TRMG01"}'
```

**预期结果：**
- 返回 `{"code": 2000, "msg": "success"}`
- 30秒后发送空结果到ECS

### 5. ECS推送数据格式

#### 有识别结果时：
```json
{
  "craneNo": "TRMG01",
  "identifyType": 1,
  "identifyTruckRoofNo": "ABC123",
  "imgUrls": [
    "http://*************:18882/truck_top/TRMG01/20250709/TRUCK_TOP_1720512345678/truck_top_001_1720512345678.jpg"
  ]
}
```

#### 无识别结果时：
```json
{
  "craneNo": "TRMG01",
  "identifyType": 1,
  "identifyTruckRoofNo": null,
  "imgUrls": []
}
```

### 6. 日志监控

关键日志信息：
```
接收到ECS集卡到位信号，开始启动车顶号识别 - 门吊号: TRMG01
开始启动车顶号识别任务 - 任务ID: TRUCK_TOP_1720512345678, 门吊号: TRMG01
车顶号识别任务已启动 - 任务ID: TRUCK_TOP_1720512345678
开始执行简化车顶号识别任务 - 任务ID: TRUCK_TOP_1720512345678, 门吊号: TRMG01, 超时时间: 30秒
识别到车顶号: ABC123 - 任务ID: TRUCK_TOP_1720512345678, 摄像头: 车顶号识别摄像头
开始发送车顶号识别结果到ECS - 任务ID: TRUCK_TOP_1720512345678, 车顶号: ABC123, 门吊号: TRMG01
车顶号识别结果发送到ECS成功 - 任务ID: TRUCK_TOP_1720512345678, 车顶号: ABC123
车顶号识别任务已完成并清理 - 任务ID: TRUCK_TOP_1720512345678
```

### 7. 配置参数

```properties
# 车顶号识别超时时间（秒）
truck-top-recognition.timeout=30

# 车顶号识别间隔时间（秒）
truck-top-recognition.interval=2

# IIS图片服务器URL
pingfang.picture.iisUrl=http://*************:18882/
```

### 8. 优势

1. **响应速度快** - 去除复杂的条件检查，立即响应
2. **容错性强** - 任何情况下都会给ECS返回结果
3. **逻辑简单** - 易于理解和维护
4. **独立性强** - 不依赖其他业务流程状态
5. **可靠性高** - 异常情况下也能正常处理

### 9. 注意事项

1. 确保摄像头配置正确
2. 确保IIS图片服务器可访问
3. 确保ECS接口地址正确
4. 监控识别任务的执行情况
5. 定期清理过期的图片文件
