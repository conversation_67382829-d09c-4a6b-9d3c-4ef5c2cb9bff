<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE project [
        <!ELEMENT project (modelVersion|artifactId|groupId|version|properties|parent|dependencies|build)*>
        <!ATTLIST project
                xmlns CDATA #REQUIRED
                xmlns:xsi CDATA #REQUIRED
                xsi:schemaLocation CDATA #REQUIRED>
        <!ELEMENT modelVersion (#PCDATA)>
        <!ELEMENT artifactId (#PCDATA)>
        <!ELEMENT groupId (#PCDATA)>
        <!ELEMENT version (#PCDATA)>
        <!ELEMENT properties (maven.compiler.source|maven.compiler.target)*>
        <!ELEMENT maven.compiler.source (#PCDATA)>
        <!ELEMENT maven.compiler.target (#PCDATA)>
        <!ELEMENT parent (groupId|artifactId|version)*>
        <!ELEMENT dependencies (dependency)*>
        <!ELEMENT dependency (groupId|artifactId|version|optional|classifier|scope|systemPath)*>
        <!ELEMENT optional (#PCDATA)>
        <!ELEMENT classifier (#PCDATA)>
        <!ELEMENT scope (#PCDATA)>
        <!ELEMENT systemPath (#PCDATA)>
        <!ELEMENT build (plugins|resources)*>
        <!ELEMENT plugins (plugin)*>
        <!ELEMENT plugin (groupId|artifactId|version|configuration|executions)*>
        <!ELEMENT configuration (includeSystemScope|configFile|projectName|excludes|includes|outputDirectory|includeScope)*>
        <!ELEMENT includeSystemScope (#PCDATA)>
        <!ELEMENT configFile (#PCDATA)>
        <!ELEMENT projectName (#PCDATA)>
        <!ELEMENT excludes (exclude)*>
        <!ELEMENT includes (include)*>
        <!ELEMENT executions (execution)*>
        <!ELEMENT execution (id|phase|goals|configuration)*>
        <!ELEMENT id (#PCDATA)>
        <!ELEMENT phase (#PCDATA)>
        <!ELEMENT goals (goal)*>
        <!ELEMENT goal (#PCDATA)>
        <!ELEMENT outputDirectory (#PCDATA)>
        <!ELEMENT includeScope (#PCDATA)>
        <!ELEMENT resources (resource)*>
        <!ELEMENT resource (directory|includes|filtering|excludes)*>
        <!ELEMENT directory (#PCDATA)>
        <!ELEMENT include (#PCDATA)>
        <!ELEMENT filtering (#PCDATA)>
        <!ELEMENT exclude (#PCDATA)>
        ]>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <artifactId>application</artifactId>
    <groupId>net.pingfang</groupId>
    <version>V0.0.0</version>
    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.3.5.RELEASE</version>
    </parent>

    <dependencies>
        <!-- plc -->
        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.28.Final</version>
        </dependency>

        <!--web启动-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!--配置-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!--spring security-->
        <!--        <dependency>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-security</artifactId>
                </dependency>-->

        <!-- jwt -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.1</version>
        </dependency>

        <!--mybatis-->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>2.2.2</version>
        </dependency>


        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.4.5</version>
        </dependency>

        <!--postgre-->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.3.3</version>
        </dependency>

        <!--aop-->
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>

        <!--excel-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.1.0</version>
        </dependency>

        <!--获取请求浏览器工具类-->
        <dependency>
            <groupId>eu.bitwalker</groupId>
            <artifactId>UserAgentUtils</artifactId>
            <version>1.21</version>
        </dependency>

        <!--JSON-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

        <!--log4j2-->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.17.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>2.17.2</version>
        </dependency>

        <!--slf4j-->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <version>2.17.2</version>
        </dependency>

        <!--lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.22</version>
        </dependency>

        <!--redis-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <!--websocket-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.8.1</version>
        </dependency>

        <!-- 获取系统信息 -->
        <dependency>
            <groupId>com.github.oshi</groupId>
            <artifactId>oshi-core</artifactId>
            <version>6.1.6</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.6</version>
        </dependency>
        <!--雪花id依赖-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.20</version>
        </dependency>

        <!-- JNA依赖 -->
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
            <version>5.12.1</version>
        </dependency>
        <dependency>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna-platform</artifactId>
            <version>5.12.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.13</version>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>31.1-jre</version>
        </dependency>

        <!-- JavaCV依赖，用于RTSP视频流处理 -->
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>javacv</artifactId>
            <version>1.5.8</version>
        </dependency>
        <!-- 根据操作系统选择平台依赖 -->
        <dependency>
            <groupId>org.bytedeco</groupId>
            <artifactId>ffmpeg</artifactId>
            <version>5.1.2-1.5.8</version>
            <classifier>windows-x86_64</classifier> <!-- 或linux-x86_64 -->
        </dependency>

        <!-- 本地第三方jar包依赖 -->
        <dependency>
            <groupId>com.hikvision</groupId>
            <artifactId>netsdk-windows</artifactId>
            <version>1.0.13</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/lib/netsdk-windows-1.0.13.jar</systemPath>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.3.5.RELEASE</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.github.shalousun</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>2.4.5</version>
                <configuration>
                    <!--指定生成文档的使用的配置文件,配置文件放在自己的项目中-->
                    <configFile>./src/main/resources/smart-doc.json</configFile>
                    <!--指定项目名称-->
                    <projectName>苏州物流园内河轨道吊理货</projectName>
                    <!--smart-doc实现自动分析依赖树加载第三方依赖的源码，如果一些框架依赖库加载不到导致报错，这时请使用excludes排除掉-->
                    <excludes>
                        <!--格式为：groupId:artifactId;参考如下-->
                        <!--也可以支持正则式如：com.alibaba:.* -->
                        <!--                        <exclude>com.alibaba:fastjson</exclude>-->
                    </excludes>
                    <!--includes配置用于配置加载外部依赖源码,配置后插件会按照配置项加载外部源代码而不是自动加载所有，因此使用时需要注意-->
                    <!--smart-doc能自动分析依赖树加载所有依赖源码，原则上会影响文档构建效率，因此你可以使用includes来让插件加载你配置的组件-->
                    <includes>
                        <!--格式为：groupId:artifactId;参考如下-->
                        <!--也可以支持正则式如：com.alibaba:.* -->
                        <!--                        <include>com.alibaba:fastjson</include>-->
                    </includes>
                </configuration>
                <!--                <executions>-->
                <!--                    <execution>-->
                <!--                        &lt;!&ndash;如果不需要在执行编译时启动smart-doc，则将phase注释掉&ndash;&gt;-->
                <!--                        <phase>compile</phase>-->
                <!--                        <goals>-->
                <!--                            &lt;!&ndash;smart-doc提供了html、openapi、markdown等goal，可按需配置&ndash;&gt;-->
                <!--                            <goal>html</goal>-->
                <!--                        </goals>-->
                <!--                    </execution>-->
                <!--                </executions>-->
            </plugin>

            <!-- Maven依赖复制插件，确保lib目录下的jar包被包含 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                            <includeScope>system</includeScope>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>

        <resources>
            <resource>
                <!-- 需要打包的目录 -->
                <directory>src/main/resources</directory>
                <!--  目录中的文件类型 -->
                <includes>
                    <include>**/banner.txt</include>
                    <include>**/*.txt</include>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                    <include>**/*.xlsx</include>
                    <include>**/*.xls</include>
                    <include>**/*.ftl</include>
                    <include>**/*.jpg</include>
                    <include>**/*.json</include>
                    <include>**/*.dll</include>
                    <include>**/*.jasper</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <!--配置文件名和获取的文件名一致-->
                    <include>application.properties</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>false</filtering>
                <excludes>
                    <exclude>application.properties</exclude>
                </excludes>
            </resource>
        </resources>
    </build>

</project>
