# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.nar

# Maven target directories
target/
application/target/
gps/target/
plc/target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/

### IntelliJ IDEA ###
.idea/modules.xml
.idea/jarRepositories.xml
.idea/compiler.xml
.idea/libraries/
*.iws
*.iml
*.ipr

### Eclipse ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

### Mac OS ###
.DS_Store

### Windows ###
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

### Java ###
*.ear
*.zip
*.tar.gz
*.rar
hs_err_pid*

### Maven ###
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

### Gradle ###
.gradle
**/build/
!src/**/build/
gradle-app.setting
!gradle-wrapper.jar
!gradle-wrapper.properties

### Spring Boot ###
spring-boot-*.log
application-*.log

### Logs ###
logs/
log/
*.log.*
*.out

### Database ###
*.db
*.sqlite
*.sqlite3

### Configuration Files (敏感信息) ###
application-prod.properties
application-dev.properties
application-test.properties
# 保留示例配置文件
!application-example.properties

### IDE ###
.idea/
*.iws
*.iml
*.ipr
out/

# Eclipse
.project
.classpath
.settings/
.metadata
bin/
tmp/
*.bak
*~.nib
local.properties
.loadpath
.recommenders
.factorypath

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/
.eclipse/
.metadata/

### Temporary Files ###
*.tmp
*.temp
*.swp
*.swo
*~
.#*

### OS Generated Files ###
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

### Project Specific ###
# Application specific
logs/
images/
*.jpeg
*.jpg
*.png
*.gif
*.bmp

# Test images (keep the specific test image)
!10.66.233.110_01_20250730143322234.jpeg

# 视频文件
*.mp4
*.avi
*.mov
*.wmv

# 大文件
*.iso
*.dmg

# 备份文件
*.backup
*.old

# 缓存文件
.cache/
cache/

# 本地环境配置
.env
.env.local
.env.*.local

# 测试覆盖率报告
coverage/
*.lcov

# 依赖目录
node_modules/

# 文档生成
docs/generated/

# 本地数据库文件
*.h2.db
*.mv.db
*.trace.db

# 本地上传文件目录（根据实际情况调整）
uploads/

# 算法库文件（如果不需要版本控制）
# *.dll
# *.so

# 配置文件中的敏感信息
**/application-secret.properties
**/database.properties
**/redis.properties
