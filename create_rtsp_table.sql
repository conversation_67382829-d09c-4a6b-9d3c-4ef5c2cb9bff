-- RTSP预识别结果表创建脚本
-- 执行此脚本来创建T_RTSP_PRE_RECOGNITION_RESULT表

-- 删除表（如果存在）
DROP TABLE IF EXISTS "T_RTSP_PRE_RECOGNITION_RESULT";

-- 创建主表：RTSP预识别结果表
CREATE TABLE "T_RTSP_PRE_RECOGNITION_RESULT" (
    "id" BIGSERIAL PRIMARY KEY,
    "seq_no" VARCHAR(50) NOT NULL,
    "crane_no" VARCHAR(20) NOT NULL,
    
    -- 识别结果信息
    "status" VARCHAR(20) NOT NULL DEFAULT 'PROCESSING',
    "container_number" VARCHAR(20),
    "confidence" REAL,
    "image_count" INTEGER DEFAULT 0,
    
    -- 触发条件信息
    "task_status" INTEGER,
    "work_type" INTEGER,
    "container_height" REAL,
    "position_difference" REAL,
    
    -- 时间信息
    "start_time" TIMESTAMP,
    "end_time" TIMESTAMP,
    "duration" BIGINT,
    
    -- 错误信息
    "error_message" VARCHAR(500),
    
    -- 图片信息 (JSON格式存储所有相机图片信息)
    "all_camera_images" TEXT,
    
    -- 最佳结果信息
    "best_camera_id" INTEGER,
    "best_image_path" VARCHAR(500),
    "best_image_url" VARCHAR(500),
    
    -- 标准字段
    "create_by" VARCHAR(50),
    "create_time" TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    "update_by" VARCHAR(50),
    "update_time" TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX "idx_rtsp_result_seq_no" ON "T_RTSP_PRE_RECOGNITION_RESULT"("seq_no");
CREATE INDEX "idx_rtsp_result_crane_no" ON "T_RTSP_PRE_RECOGNITION_RESULT"("crane_no");
CREATE INDEX "idx_rtsp_result_status" ON "T_RTSP_PRE_RECOGNITION_RESULT"("status");
CREATE INDEX "idx_rtsp_result_create_time" ON "T_RTSP_PRE_RECOGNITION_RESULT"("create_time");
CREATE INDEX "idx_rtsp_result_container_number" ON "T_RTSP_PRE_RECOGNITION_RESULT"("container_number");

-- 添加表注释
COMMENT ON TABLE "T_RTSP_PRE_RECOGNITION_RESULT" IS 'RTSP预识别结果表';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."id" IS '主键ID';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."seq_no" IS '序列号';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."crane_no" IS '吊机号';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."status" IS '识别状态(SUCCESS,FAILED,PROCESSING)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."container_number" IS '识别的箱号';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."confidence" IS '识别置信度(0.0-1.0)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."image_count" IS '图片数量';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."task_status" IS '任务状态(1-作业中)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."work_type" IS '作业类型(1-8)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."container_height" IS '箱面高度';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."position_difference" IS '位置差异';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."start_time" IS '开始时间';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."end_time" IS '结束时间';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."duration" IS '识别耗时(毫秒)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."error_message" IS '错误信息';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."all_camera_images" IS '所有相机图片信息(JSON格式)';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."best_camera_id" IS '最佳结果相机ID';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."best_image_path" IS '最佳结果图片路径';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."best_image_url" IS '最佳结果图片URL';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."create_by" IS '创建人';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."create_time" IS '创建时间';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."update_by" IS '更新人';
COMMENT ON COLUMN "T_RTSP_PRE_RECOGNITION_RESULT"."update_time" IS '更新时间';

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_time_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW."update_time" = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为表创建更新时间触发器
CREATE TRIGGER update_rtsp_result_updated_time 
    BEFORE UPDATE ON "T_RTSP_PRE_RECOGNITION_RESULT" 
    FOR EACH ROW EXECUTE FUNCTION update_updated_time_column();

-- 建表完成提示
SELECT 'RTSP预识别结果表创建完成！' AS message;
