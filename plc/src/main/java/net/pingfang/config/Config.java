package net.pingfang.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.Data;

@Component
@Data
public class Config {

	/**
	 * tcp服务监听端口
	 */
	@Value("${socket.server.port}")
	private int socketServerPort;
	/*
	 * tcp服务通讯编码
	 */
	@Value("${socket.server.coding}")
	private String socketServerCoding;

	/**
	 * 门吊ID (1-门吊1；2-门吊2；3-门吊3)
	 */
	@Value("${plc.crane.id}")
	private int craneId;

	/**
	 * 系统类型 (1-OCR, 2-CCTV)
	 */
	@Value("${plc.system.type}")
	private int systemType;
	
}
