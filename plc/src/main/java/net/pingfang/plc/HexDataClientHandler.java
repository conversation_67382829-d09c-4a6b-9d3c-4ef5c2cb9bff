package net.pingfang.plc;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;

/**
 * XXX
 *
 * @author: CM
 * @date: 2023/8/31
 */
@Slf4j
@Component
@ChannelHandler.Sharable
public class HexDataClientHandler extends ChannelInboundHandlerAdapter {
    @Autowired
    private RestTemplateUtil restTemplateUtil;
    @Autowired
    private HexDataClient hexDataClient;
    @Value("${application.url}")
    public String applicationUrl;


    public static void main(String[] args) {

    }


    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        log.debug("plc 初次接收:{}", msg);
        if (msg == null) {
            log.info("plc数据为null");
            return;
        }

        try {
            String plcData = (String) msg;
            log.debug("接收到PLC数据: {}", plcData);

            boolean dataProcessedSuccessfully = false;

            // 如果数据以GITS开头，则使用新的协议解析器解析数据
            if (plcData.startsWith("47495453")) { // GITS
                try {
                    // 使用更新的协议解析器
                    Plc plc = PlcProtocolParser.parseResponse(plcData);
                    if (plc != null) {
                        log.info("GITS协议解析PLC数据成功: plcX={}, plcY={}, plcZ={}, 重量={}, 开闭锁状态={}, 吊具类型={}, 任务状态={}, 作业类型={}, 箱面高度={}",
                                plc.getPlcX(), plc.getPlcY(), plc.getPlcZ(), plc.getWeight(), plc.getLock(),
                                plc.getContainerType(), plc.getTaskStatus(), plc.getWorkType(), plc.getSpreaderFromContainerHeight());
                        // 发给应用程序
                        try {
                            restTemplateUtil.post(applicationUrl, plc, Result.class);
                            dataProcessedSuccessfully = true;
                        } catch (Exception exception) {
                            log.error("发送plc->应用软件失败: {}", exception.getMessage());
                        }
                    } else {
                        log.warn("GITS协议解析返回null，数据可能无效");
                    }
                } catch (Exception e) {
                    log.error("解析GITS协议数据异常: {}", e.getMessage());
                }
            }

            // 如果不是新协议或解析失败，尝试使用旧协议解析
            if (!dataProcessedSuccessfully && plcData.length() == 58) {
                try {
                    Plc plc = new Plc();
                    //小车海陆侧判断  0x00表示陆侧，0x01表示海侧
                    int hl = hexToDecimal(plcData.substring(18, 22));
                    plc.setSeaLand(hl);
                    // 大车运行方向 0x00表示停止，0x01表示大车向左，0x02表示大车向右
                    int dc = hexToDecimal(plcData.substring(22, 26));
                    plc.setMoveDir(dc);
                    // 吊箱类型  0x00表示无效，0x01表示20FT，0x02表示40FT，0x03表示45FT，0x04表示双箱
                    int dx = hexToDecimal(plcData.substring(26, 30));
                    plc.setContainerType(getContainerType(dx));
                    // 锁扣上锁状态 0x00表示无效，0x01表示开锁状态，0x02表示闭锁状态
                    int lock = hexToDecimal(plcData.substring(30, 34));
                    plc.setLock(getLock(lock));
                    // 重量 无
                    int weight = hexToDecimal(plcData.substring(34, 38));
                    plc.setWeight(weight * 10);
                    log.info("plc-解析: 小车海陆侧:{},大车运行方向:{},吊箱类型:{},锁扣上锁状态:{},重量:{}",
                            hl == 0 ? "陆侧" : "海侧",
                            dc == 0 ? "停止" : (dc == 1 ? "向左" : "向右"),
                            dx == 0 ? "未知" : (dx == 1 ? "20尺" : (dx == 2 ? "40尺" : (dx == 3 ? "45尺" : "双箱"))),
                            lock == 0 ? "未知" : (lock == 1 ? "开锁" : "闭锁"),
                            "未知");

                    // 发给应用程序
                    try {
                        restTemplateUtil.post(applicationUrl, plc, Result.class);
                        dataProcessedSuccessfully = true;
                    } catch (Exception exception) {
                        log.info("发送plc->应用软件失败");
                    }
                } catch (Exception exception) {
                    log.info("旧协议解析异常:{}", ExceptionUtil.getStackTrace(exception));
                }
            }

            // 只要收到了数据（不管能否解析），都说明对方还活着，重置无响应计数器
            hexDataClient.onDataReceived();

            if (!dataProcessedSuccessfully) {
                log.warn("PLC数据解析失败或格式不正确，数据长度: {}, 数据内容: {}", plcData.length(), plcData);
            }

        } catch (Exception exception) {
            log.error("处理PLC异常:{}", ExceptionUtil.getStackTrace(exception));
            // 即使处理异常，但收到了数据，也重置计数器
            hexDataClient.onDataReceived();
        }
    }

    public int hexToDecimal(String hexString) {
        // 去除可能的前缀 "0x"
        if (hexString.startsWith("0x") || hexString.startsWith("0X")) {
            hexString = hexString.substring(2);
        }
        // 将16进制字符串转换为整数
        return Integer.parseInt(hexString, 16);
    }

    /**
     * 将十六进制字符串转换为Float类型
     * @param hexString 十六进制字符串
     * @return Float值
     */
    public float hexToFloat(String hexString) {
        try {
            // 去除可能的前缀 "0x"
            if (hexString.startsWith("0x") || hexString.startsWith("0X")) {
                hexString = hexString.substring(2);
            }

            // 处理字节顺序，确保是正确的字节顺序
            // 如果数据是大端存储，需要调整字节顺序
            if (hexString.length() == 8) {
                // 将十六进制字符串转换为整数
                int intBits = Integer.parseUnsignedInt(hexString, 16);

                // 将整数位转换为Float
                return Float.intBitsToFloat(intBits);
            } else {
                log.error("十六进制字符串长度不正确: {}", hexString);
                return 0.0f;
            }
        } catch (Exception e) {
            log.error("转换浮点数异常: {} - {}", hexString, e.getMessage());
            return 0.0f;
        }
    }

    @Override
    public void exceptionCaught(ChannelHandlerContext ctx, Throwable cause) {
        log.error("PLC连接异常: {}", ExceptionUtil.getStackTrace(cause));
        ctx.close();

        // 通知客户端处理连接丢失，而不是直接退出程序
        log.warn("PLC连接异常，触发重连机制");
        hexDataClient.handleConnectionLost();
    }

    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        log.warn("PLC连接断开");
        super.channelInactive(ctx);

        // 通知客户端处理连接丢失
        hexDataClient.handleConnectionLost();
    }

    private String byteToString(ByteBuf byteBuf) {
        String body = null;
        try {
            byte[] bytes = new byte[byteBuf.readableBytes()];
            byteBuf.readBytes(bytes);
            body = new String(bytes, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        return body;
    }

    public Integer getLock(int plc) {
        // 0x00表示无效，0x01表示开锁状态，0x02表示闭锁状态
        if (plc == 0) {
            return null;
        }
        if (plc == 1) {
            return LockSignEnum.START.getValue();
        }
        if (plc == 2) {
            return LockSignEnum.END.getValue();
        }
        return null;
    }


    public Integer getContainerType(int plc) {
        // 吊箱类型	W02	集装箱尺寸。0x00表示无效，0x01表示20FT，0x02表示40FT，0x03表示45FT，0x04表示双箱
        if (plc == 0) {
            return null;
        }
        if (plc == 1) {
            return ContainerTypeEnum.SS.getValue();
        }
        if (plc == 2) {
            return ContainerTypeEnum.SL.getValue();
        }
        if (plc == 3) {
            return ContainerTypeEnum.SL.getValue();
        }
        if (plc == 4) {
            return ContainerTypeEnum.TD.getValue();
        }
        return null;
    }

    /**
     * 获取容器类型的描述信息
     * @param plc 容器类型编码
     * @return 容器类型描述
     */
    public String getContainerTypeDesc(int plc) {
        if (plc == 0) {
            return "未知";
        }
        if (plc == 1) {
            return "20尺";
        }
        if (plc == 2) {
            return "40尺";
        }
        if (plc == 3) {
            return "45尺";
        }
        if (plc == 4) {
            return "双箱";
        }
        return "未知";
    }

    /**
     * 解析GITS协议数据
     * @param plcData GITS协议数据字符串
     * @return Plc对象
     */
    private Plc parseGitsProtocol(String plcData) {
        // 检查数据格式是否符合协议要求
        if (!plcData.startsWith("47495453")) { // GITS
            log.info("plc数据包头不符合要求:{}", plcData);
            return null;
        }

        // 检查消息类型
        if (!plcData.substring(8, 12).equals("0001")) {
            log.info("plc消息类型不符合要求:{}", plcData);
            return null;
        }

        // 检查数据长度是否为84个字符（特定格式）
        if (plcData.length() != 112) {
            log.info("plc数据长度不符合要求，应为84个字符，实际为{}个字符: {}", plcData.length(), plcData);
            return null;
        }

        // 获取数据长度 - 位于字节16-19 (十六进制字符32-39)
        int dataLength = hexToDecimal(plcData.substring(24, 28));
        log.info("数据体长度: {} 字节", dataLength);

        // 检查数据长度是否为42字节（十六进制为2A）
        if (dataLength != 42) {
            log.info("plc数据体长度不符合要求，应为42字节，实际为{}字节", dataLength);
            return null;
        }

        // 解析数据体
        String dataBody = plcData.substring(28);
        log.info("数据体: {}", dataBody);

        Plc plc = new Plc();
        int index = 0;

        try {
            // 解析实际数据格式：000000004108CCCD4118CCCD0000000001020000019657640B6500000000000000000000000000000000

            // 前4个字节可能是保留字节，跳过
            index += 8;

            // 小车坐标 4个字节 Float
            if (index + 8 <= dataBody.length()) {
                float cartPosition = hexToFloat(dataBody.substring(index, index + 8));
                plc.setPlcX((int)cartPosition);
                log.info("小车坐标: {}", cartPosition);
                index += 8;
            }

            // 吊具坐标 4个字节 Float
            if (index + 8 <= dataBody.length()) {
                float spreaderHeight = hexToFloat(dataBody.substring(index, index + 8));
                plc.setPlcY((int)spreaderHeight);
                log.info("吊具坐标: {}", spreaderHeight);
                index += 8;
            }

            // 吊具重量 4个字节 Float
            if (index + 8 <= dataBody.length()) {
                float weight = hexToFloat(dataBody.substring(index, index + 8));
                plc.setWeight((int)weight);
                log.info("吊具重量: {}", weight);
                index += 8;
            }

            // 开闭锁状态 1个字节（1-闭锁 2-开锁）
            if (index + 2 <= dataBody.length()) {
                int lockStatus = hexToDecimal(dataBody.substring(index, index + 2));
                plc.setLock(lockStatus == 2 ? 0 : 1); // 转换为系统内部的锁状态表示：0-开锁,1-闭锁
                log.info("开闭锁状态: {}", lockStatus == 1 ? "闭锁" : "开锁");
                index += 2;
            }

            // 吊具类型 1个字节（吊具类型(20,40,45)）
            if (index + 2 <= dataBody.length()) {
                int containerTypeCode = hexToDecimal(dataBody.substring(index, index + 2));
                plc.setContainerType(getContainerType(containerTypeCode));
                log.info("吊具类型: {}", getContainerTypeDesc(containerTypeCode));
                index += 2;
            }

            return plc;
        } catch (Exception e) {
            log.error("解析数据体异常: {}", e.getMessage());
            return null;
        }
    }
}
