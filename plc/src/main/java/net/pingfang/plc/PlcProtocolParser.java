package net.pingfang.plc;

import lombok.extern.slf4j.Slf4j;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;

/**
 * PLC协议解析器
 * 采用pingpong方式从ECS不断获取实时plc数据
 *
 * 新协议格式：
 * - 大车坐标4个字节 Float
 * - 小车坐标4个字节 Float：41 08 CC CD=8.55
 * - 吊具坐标plcz4个字节 Float：41 18 CC CD=9.55
 * - 吊具重量4个字节 Float
 * - 开闭锁状态1个字节（1-闭锁 2-开锁）：01
 * - 吊具类型1个字节（吊具类型(20,40,45)）：02
 * - 数据推送时间8个字节
 * - 吊具上下伸速度4个字节
 * - 大车指令输出速度4个字节
 * - 小车速度 4个字节
 * - 电气房侧大车速度4个字节
 * - 任务状态Short0-无任务正在作业1-任务正在作业中2个字节
 * - 作业类型Short（0-未知1-列车至堆场2-集卡至堆场3-列车至集卡4-堆场至列车5-堆场至集卡6-集卡至列车7-堆场内倒箱8-列车至列车9-堆场定位10-火车定位11-火车车头定位）2个字节
 * - 吊具距离箱面高度（如吊具未到达任务排上方，值为999）,单位mFloat4个字节
 * - 小车位置 起点Float4个字节 目标车道8~10
 * - 大车位置 起点Float4个字节
 * - 吊具位置 起点Float4个字节
 *
 * 发示例：47 49 54 53 00 01 00 01 00 01 00 00 00 00
 * 收示例：47 49 54 53 00 01 00 01 00 00 00 00 00 2A 00 00 00 00 41 08 CC CD 41 18 CC CD 00 00 00 00 01 02 00 00 01 96 32 1E 3A 56 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
 *
 * @author: CM
 * @date: 2023/5/31
 * @updated: 2025/6/26 - 支持新的完整协议格式
 */
@Slf4j
public class PlcProtocolParser {

    // 包头固定值 "GITS"
    private static final String HEADER = "47495453";

    /**
     * 解析PLC协议数据（在原有基础上追加新字段）
     *
     * @param hexData 十六进制字符串
     * @return Plc对象
     */
    public static Plc parseResponse(String hexData) {
        log.info("=== 开始解析PLC数据 ===");
        log.info("输入数据: {}", hexData);
        log.debug("数据长度: {} 字符", hexData == null ? 0 : hexData.length());

        // 检查数据有效性
        if (hexData == null || hexData.isEmpty()) {
            log.error("PLC数据为空");
            return null;
        }

        // 检查包头
        if (!hexData.startsWith(HEADER)) {
            log.error("PLC数据包头不正确，期望:{}, 实际:{}", HEADER, hexData.substring(0, Math.min(8, hexData.length())));
            return null;
        }

        log.debug("包头检查通过: {}", HEADER);

        try {
            Plc plc = new Plc();

            // 解析数据长度 - 修正位置
            // GITS协议格式：包头(8) + 消息类型(4) + 门吊ID(4) + 系统标识(4) + 数据长度(8) = 28字符
            log.debug("准备解析数据长度字段，从位置20到28");

            if (hexData.length() < 28) {
                log.error("数据长度不足28字符，无法解析包头");
                return null;
            }

            String dataLengthHexStr = hexData.substring(20, 28);
            log.debug("数据长度字段 (位置20-28): '{}'", dataLengthHexStr);

            int dataLengthHex;
            try {
                dataLengthHex = Integer.parseInt(dataLengthHexStr, 16);
                log.debug("数据长度解析成功: '{}' -> {} (十进制)", dataLengthHexStr, dataLengthHex);
            } catch (NumberFormatException e) {
                log.error("解析数据长度失败: '{}', 错误: {}", dataLengthHexStr, e.getMessage());
                return null;
            }

            // 检查数据长度：包头(28字符) + 数据长度(dataLengthHex字节 * 2字符/字节)
            int expectedLength = 28 + dataLengthHex * 2;
            log.info("长度检查: 包头28字符 + 数据体{}字节*2 = 期望{}字符，实际{}字符",
                     dataLengthHex, expectedLength, hexData.length());

            if (hexData.length() < expectedLength) {
                log.error("PLC数据长度不足: 期望{}, 实际{}, 数据: {}", expectedLength, hexData.length(), hexData);
                return null;
            }

            // ========== 原有字段解析（保持不变） ==========
            log.debug("开始解析PLC数据字段，数据总长度: {}", hexData.length());

            // 大车坐标 (plcX) - 4字节 Float
            String plcXHex = hexData.substring(28, 36);
            float plcX = hexStringToFloat(plcXHex);
            plc.setPlcX((int)plcX);
            log.info("大车坐标(plcX): {} -> {:.2f}米 (整数值: {})", plcXHex, plcX, (int)plcX);

            // 小车坐标 (plcY) - 4字节 Float
            String plcYHex = hexData.substring(36, 44);
            float plcY = hexStringToFloat(plcYHex);
            plc.setPlcY((int)plcY);
            log.info("小车坐标(plcY): {} -> {:.2f}米 (整数值: {}) [目标车道8~10]", plcYHex, plcY, (int)plcY);

            // 吊具坐标 (plcZ) - 4字节 Float（新增字段）
            String plcZHex = hexData.substring(44, 52);
            float plcZ = hexStringToFloat(plcZHex);
            plc.setPlcZ(plcZ);
            log.info("吊具坐标(plcZ): {} -> {:.2f}米", plcZHex, plcZ);

            // 吊具重量 - 4字节 Float
            String weightHex = hexData.substring(52, 60);
            float weight = hexStringToFloat(weightHex);
            plc.setWeight((int)weight);
            log.info("吊具重量: {} -> {:.2f}公斤 (整数值: {}公斤)", weightHex, weight, (int)weight);

            // 开闭锁状态 - 1字节 (1-闭锁 2-开锁)
            int lockStatus = Integer.parseInt(hexData.substring(60, 62), 16);
            plc.setLock(lockStatus == 2 ? 0 : 1); // 2-开锁转为0，1-闭锁转为1
            String lockStatusDesc = lockStatus == 1 ? "闭锁" : (lockStatus == 2 ? "开锁" : "未知");
            log.info("开闭锁状态: {} -> 原始值:{}, 状态:{}", hexData.substring(60, 62), lockStatus, lockStatusDesc);

            // 吊具类型 - 1字节
            int containerType = Integer.parseInt(hexData.substring(62, 64), 16);
            plc.setContainerType(containerType);
            String containerTypeDesc = getContainerTypeDescription(containerType);
            log.info("吊具类型: {} -> 类型代码:{}, 描述:{}", hexData.substring(62, 64), containerType, containerTypeDesc);

            // ========== 新增字段解析（追加在原有字段后面） ==========
            int offset = 64; // 从原有字段结束位置开始

            // 数据推送时间 - 8字节
            if (offset + 16 <= hexData.length()) {
                String pushTimeHex = hexData.substring(offset, offset + 16);
                long pushTime = hexStringToLong(pushTimeHex);
                plc.setPushTime(pushTime);
                log.info("数据推送时间: {} -> {} (时间戳)", pushTimeHex, pushTime);
                offset += 16;
            }

            // 吊具上下伸速度 - 4字节 Float
            if (offset + 8 <= hexData.length()) {
                String hoistSpeedHex = hexData.substring(offset, offset + 8);
                float hoistSpeed = hexStringToFloat(hoistSpeedHex);
                plc.setHoistSpeed(hoistSpeed);
                log.info("吊具上下伸速度: {} -> {:.2f}米/秒", hoistSpeedHex, hoistSpeed);
                offset += 8;
            }

            // 大车指令输出速度 - 4字节 Float
            if (offset + 8 <= hexData.length()) {
                String gantryCommandSpeedHex = hexData.substring(offset, offset + 8);
                float gantryCommandSpeed = hexStringToFloat(gantryCommandSpeedHex);
                plc.setGantryCommandSpeed(gantryCommandSpeed);
                log.info("大车指令输出速度: {} -> {:.2f}米/秒", gantryCommandSpeedHex, gantryCommandSpeed);
                offset += 8;
            }

            // 小车速度 - 4字节 Float
            if (offset + 8 <= hexData.length()) {
                String trolleySpeedHex = hexData.substring(offset, offset + 8);
                float trolleySpeed = hexStringToFloat(trolleySpeedHex);
                plc.setTrolleySpeed(trolleySpeed);
                log.info("小车速度: {} -> {:.2f}米/秒", trolleySpeedHex, trolleySpeed);
                offset += 8;
            }

            // 电气房侧大车速度 - 4字节 Float
            if (offset + 8 <= hexData.length()) {
                String gantryElectricalSpeedHex = hexData.substring(offset, offset + 8);
                float gantryElectricalSpeed = hexStringToFloat(gantryElectricalSpeedHex);
                plc.setGantryElectricalSpeed(gantryElectricalSpeed);
                log.info("电气房侧大车速度: {} -> {:.2f}米/秒", gantryElectricalSpeedHex, gantryElectricalSpeed);
                offset += 8;
            }

            // 任务状态 - 2字节 Short (0-无任务正在作业 1-任务正在作业中)
            if (offset + 4 <= hexData.length()) {
                String taskStatusHex = hexData.substring(offset, offset + 4);
                short taskStatus = hexStringToShort(taskStatusHex);
                plc.setTaskStatus(taskStatus);
                String taskStatusDesc = taskStatus == 0 ? "无任务正在作业" : (taskStatus == 1 ? "任务正在作业中" : "未知状态");
                log.info("任务状态: {} -> 状态代码:{}, 描述:{}", taskStatusHex, taskStatus, taskStatusDesc);
                offset += 4;
            }

            // 作业类型 - 2字节 Short
            if (offset + 4 <= hexData.length()) {
                String workTypeHex = hexData.substring(offset, offset + 4);
                short workType = hexStringToShort(workTypeHex);
                plc.setWorkType(workType);
                String workTypeDesc = getWorkTypeDescription(workType);
                log.info("作业类型: {} -> 类型代码:{}, 描述:{}", workTypeHex, workType, workTypeDesc);
                offset += 4;
            }

            // 吊具距离箱面高度 - 4字节 Float
            if (offset + 8 <= hexData.length()) {
                String spreaderHeightHex = hexData.substring(offset, offset + 8);
                float spreaderFromContainerHeight = hexStringToFloat(spreaderHeightHex);
                plc.setSpreaderFromContainerHeight(spreaderFromContainerHeight);
                String heightDesc = spreaderFromContainerHeight == 999.0f ? "未到达任务排上方" : String.format("%.2f米", spreaderFromContainerHeight);
                log.info("吊具距离箱面高度: {} -> {}", spreaderHeightHex, heightDesc);
                offset += 8;
            }

            // 小车位置起点 - 4字节 Float
            if (offset + 8 <= hexData.length()) {
                String trolleyStartHex = hexData.substring(offset, offset + 8);
                float trolleyStartPosition = hexStringToFloat(trolleyStartHex);
                plc.setTrolleyStartPosition(trolleyStartPosition);
                log.info("小车位置起点: {} -> {:.2f}米", trolleyStartHex, trolleyStartPosition);
                offset += 8;
            }

            // 大车位置起点 - 4字节 Float
            if (offset + 8 <= hexData.length()) {
                String gantryStartHex = hexData.substring(offset, offset + 8);
                float gantryStartPosition = hexStringToFloat(gantryStartHex);
                plc.setGantryStartPosition(gantryStartPosition);
                log.info("大车位置起点: {} -> {:.2f}米", gantryStartHex, gantryStartPosition);
                offset += 8;
            }

            // 吊具位置起点 - 4字节 Float
            if (offset + 8 <= hexData.length()) {
                String hoistStartHex = hexData.substring(offset, offset + 8);
                float hoistStartPosition = hexStringToFloat(hoistStartHex);
                plc.setHoistStartPosition(hoistStartPosition);
                log.info("吊具位置起点: {} -> {:.2f}米", hoistStartHex, hoistStartPosition);
                offset += 8;
            }

            // 输出完整解析结果摘要
            log.info("=== PLC数据解析完成 ===");
            log.info("基础数据: 大车坐标={:.2f}米, 小车坐标={:.2f}米, 吊具坐标={:.2f}米, 重量={}公斤",
                    plcX, plcY, plcZ, (int)weight);
            log.info("状态信息: 开闭锁={}, 吊具类型={}, 任务状态={}, 作业类型={}",
                    lockStatus == 1 ? "闭锁" : (lockStatus == 2 ? "开锁" : "未知"),
                    getContainerTypeDescription(containerType),
                    plc.getTaskStatus() != null ? (plc.getTaskStatus() == 0 ? "无任务" : "作业中") : "未知",
                    plc.getWorkType() != null ? getWorkTypeDescription(plc.getWorkType()) : "未知");
            if (plc.getSpreaderFromContainerHeight() != null) {
                log.info("高度信息: 吊具距箱面={}",
                        plc.getSpreaderFromContainerHeight() == 999.0f ? "未到达" : String.format("%.2f米", plc.getSpreaderFromContainerHeight()));
            }

            return plc;
        } catch (Exception e) {
            log.error("解析PLC数据异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 生成请求数据
     *
     * @param craneId 门吊ID (1-门吊1；2-门吊2；3-门吊3)
     * @param systemId 系统标识 (1-OCR, 2-CCTV)
     * @return 十六进制字符串
     */
    public static String generateRequest(int craneId, int systemId) {
        StringBuilder sb = new StringBuilder();

        // 包头固定4个字节：47 49 54 53
        sb.append("47495453");

        // 消息类型固定2个字节：00 01
        sb.append("0001");

        // 交互门吊固定2个字节
        sb.append(String.format("%04X", craneId));

        // 系统标识固定2个字节
        sb.append(String.format("%04X", systemId));

        // 数据长度固定4个字节：00 00 00 00
        sb.append("00000000");

        return sb.toString();
    }

    /**
     * 将十六进制字符串转换为浮点数
     *
     * @param hexString 十六进制字符串（8个字符，4个字节）
     * @return 浮点数
     */
    private static float hexStringToFloat(String hexString) {
        byte[] bytes = new byte[4];
        for (int i = 0; i < 4; i++) {
            bytes[i] = (byte) Integer.parseInt(hexString.substring(i * 2, i * 2 + 2), 16);
        }

        ByteBuffer buffer = ByteBuffer.wrap(bytes);
        buffer.order(ByteOrder.BIG_ENDIAN);
        return buffer.getFloat();
    }

    /**
     * 将十六进制字符串转换为长整数
     *
     * @param hexString 十六进制字符串（16个字符，8个字节）
     * @return 长整数
     */
    private static long hexStringToLong(String hexString) {
        byte[] bytes = new byte[8];
        for (int i = 0; i < 8; i++) {
            bytes[i] = (byte) Integer.parseInt(hexString.substring(i * 2, i * 2 + 2), 16);
        }

        ByteBuffer buffer = ByteBuffer.wrap(bytes);
        buffer.order(ByteOrder.BIG_ENDIAN);
        return buffer.getLong();
    }

    /**
     * 将十六进制字符串转换为短整数
     *
     * @param hexString 十六进制字符串（4个字符，2个字节）
     * @return 短整数
     */
    private static short hexStringToShort(String hexString) {
        byte[] bytes = new byte[2];
        for (int i = 0; i < 2; i++) {
            bytes[i] = (byte) Integer.parseInt(hexString.substring(i * 2, i * 2 + 2), 16);
        }

        ByteBuffer buffer = ByteBuffer.wrap(bytes);
        buffer.order(ByteOrder.BIG_ENDIAN);
        return buffer.getShort();
    }

    /**
     * 获取吊具类型描述
     */
    private static String getContainerTypeDescription(int containerType) {
        switch (containerType) {
            case 1: return "20尺";
            case 2: return "40尺";
            case 3: return "45尺";
            case 4: return "双箱";
            default: return "未知(" + containerType + ")";
        }
    }

    /**
     * 获取作业类型描述
     */
    private static String getWorkTypeDescription(short workType) {
        switch (workType) {
            case 0: return "未知";
            case 1: return "列车至堆场";
            case 2: return "集卡至堆场";
            case 3: return "列车至集卡";
            case 4: return "堆场至列车";
            case 5: return "堆场至集卡";
            case 6: return "集卡至列车";
            case 7: return "堆场内倒箱";
            case 8: return "列车至列车";
            case 9: return "堆场定位";
            case 10: return "火车定位";
            case 11: return "火车车头定位";
            default: return "未知类型(" + workType + ")";
        }
    }
}
