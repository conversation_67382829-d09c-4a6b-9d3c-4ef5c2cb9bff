package net.pingfang.plc;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 相关路径信息配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "plc.server")
public class PlcServerPathUtil {

    /**
     * 理货平台服务：ip
     */
    private String ip;

    /**
     * 理货平台服务:端口
     */
    private Integer port;

}
