package net.pingfang.plc;

import lombok.Getter;

/**
 * 返回状态码枚举类
 */
 public enum ResultCodeEnum {

    SUCCESS(2000, "成功"),
    UN_LOGIN(4001, "您未登录或登录已失效"),
    NO_PERMISSION(4003, "您无权限"),
    REFRESH_TOKEN_EXPIRED(4004, "Refresh Token已过期"),
    ERROR(5000, "系统异常,请联系IT处理,TraceId:"),
    FAIL(5001, "数据校验失败")
   ;



   @Getter
   private Integer code;

   @Getter
   private String msg;

    ResultCodeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }
}
