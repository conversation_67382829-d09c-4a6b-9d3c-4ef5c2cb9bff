package net.pingfang.plc;

import io.netty.bootstrap.Bootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.*;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.SocketChannel;
import io.netty.channel.socket.nio.NioSocketChannel;
import io.netty.handler.codec.bytes.ByteArrayEncoder;
import lombok.extern.slf4j.Slf4j;
import net.pingfang.config.Config;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;


/**
 * XXX
 *
 * @author: CM
 * @date: 2023/8/31
 */
@Component
@Slf4j
@EnableScheduling
public class HexDataClient {
    @Autowired
    public PlcServerPathUtil plcServerPathUtil;

    @Autowired
    public HexDataClientHandler hexDataClientHandler;

    @Autowired
    private Config config;

    public static ExecutorService executors = Executors.newCachedThreadPool();
    private static ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(2);

    private static final int RECONNECT_DELAY = 5; // 重连延迟，单位：秒
    private static final int MAX_NO_RESPONSE_COUNT = 30; // 连续无响应的最大次数

    private EventLoopGroup group;
    private ChannelHandlerContext ctx;

    // 连接状态管理
    private volatile boolean isConnected = false;
    private volatile boolean isReconnecting = false;

    // 无响应检测：发送数据后没有收到任何响应的连续次数
    private volatile int consecutiveNoResponseCount = 0;

    @PostConstruct
    public void start(){
        new Thread(new Runnable() {
            @Override
            public void run() {
                connect();
            }
        }).start();

    }


    public void connect() {
        if (isReconnecting) {
            log.info("正在重连中，跳过本次连接请求");
            return;
        }

        isReconnecting = true;
        log.info("开始连接PLC服务器...");

        if (group != null && !group.isShutdown()) {
            group.shutdownGracefully();
        }

        group = new NioEventLoopGroup();
        Bootstrap bootstrap = new Bootstrap();
        bootstrap.group(group)
                .channel(NioSocketChannel.class)
                .option(ChannelOption.TCP_NODELAY, true)
                .option(ChannelOption.SO_KEEPALIVE, true)
                .handler(new ChannelInitializer<SocketChannel>() {
                    @Override
                    protected void initChannel(SocketChannel ch) {
                        ch.pipeline().addLast(
                                new ByteArrayEncoder(),
//                                new ByteArrayDecoder(),
                                new MyDecoder(),       //自定义的解码
                                hexDataClientHandler
                        );
                    }
                });

        try {
            log.info("PLC连接ip:{},端口:{}", plcServerPathUtil.getIp(), plcServerPathUtil.getPort());
            try {
                ChannelFuture future = bootstrap.connect(plcServerPathUtil.getIp(), plcServerPathUtil.getPort()).sync();
                future.addListener((ChannelFutureListener) future1 -> {
                    if (future1.isSuccess()) {
                        log.info("PLC连接成功");
                        ctx = future1.channel().pipeline().lastContext();
                        isConnected = true;
                        isReconnecting = false;
                        consecutiveNoResponseCount = 0;

                        // 启动数据发送线程
                        executors.execute(() -> {
                            sendHexData();
                        });
                    } else {
                        log.error("PLC连接失败");
                        isConnected = false;
                        isReconnecting = false;
                        scheduleReconnect();
                    }
                });
                future.channel().closeFuture().sync();
            }catch (InterruptedException e) {
                log.error("PLC连接中断: {}", e.getMessage());
                isConnected = false;
                isReconnecting = false;
                scheduleReconnect();
            }
        } catch (Exception e) {
            log.error("PLC连接异常: {}", e.getMessage());
            isConnected = false;
            isReconnecting = false;
            scheduleReconnect();
        } finally {
//            group.shutdownGracefully();
        }
    }

    private void scheduleReconnect() {
        if (!isReconnecting) {
            log.info("{}秒后尝试重连PLC服务器", RECONNECT_DELAY);
            scheduler.schedule(this::connect, RECONNECT_DELAY, TimeUnit.SECONDS);
        }
    }



    /**
     * 处理连接丢失
     */
    public void handleConnectionLost() {
        isConnected = false;
        consecutiveNoResponseCount = 0;

        if (ctx != null) {
            ctx.close();
            ctx = null;
        }

        if (group != null && !group.isShutdown()) {
            group.shutdownGracefully();
        }

        scheduleReconnect();
    }

    /**
     * 发送数据后调用，增加无响应计数器
     */
    public void onDataSent() {
        this.consecutiveNoResponseCount++;
        log.debug("发送PLC数据，连续无响应次数: {}", consecutiveNoResponseCount);

        // 检查是否超过最大无响应次数
        if (consecutiveNoResponseCount >= MAX_NO_RESPONSE_COUNT) {
            log.error("PLC连续{}次发送数据无响应，判断对方可能重启，开始重连", MAX_NO_RESPONSE_COUNT);
            handleConnectionLost();
        }
    }

    /**
     * 收到任何PLC数据时调用（不管能否解析），重置无响应计数器
     */
    public void onDataReceived() {
        this.consecutiveNoResponseCount = 0;
        log.debug("收到PLC数据，重置无响应计数器");
    }

    /**
     * 获取连接状态
     */
    public boolean isConnected() {
        return isConnected && ctx != null && ctx.channel().isActive();
    }

    /**
     * 获取连接状态信息
     */
    public String getConnectionStatus() {
        if (isReconnecting) {
            return "重连中";
        } else if (isConnected()) {
            return "已连接";
        } else {
            return "未连接";
        }
    }

    // 旧的PLC查询指令
    // byte[] CMDBuffer = new byte[]{ 0x00, 0x01, 0x00, 0x00, 0x00, 0x06, 0x01, 0x03, 0x00, 0x00, 0x00, 0x0A };

    // 新的PLC查询指令 - GITS协议
    byte[] CMDBuffer;//PLC查询指令


    public void sendHexData() {
    	log.info("开始PLC数据发送循环");
        while (true){
            if (isConnected && ctx != null && ctx.channel().isActive()) {
                try {
                    // 生成GITS协议请求数据
                    String hexRequest = PlcProtocolParser.generateRequest(config.getCraneId(), config.getSystemType());
                    log.debug("发送GITS协议请求: 门吊{}, 系统类型{}, 请求数据: {}",
                            config.getCraneId(), config.getSystemType(), hexRequest);
                    CMDBuffer = hexStringToByteArray(hexRequest);

                    ByteBuf buffer = Unpooled.wrappedBuffer(CMDBuffer);
                    ctx.writeAndFlush(buffer);
                    log.debug("发送PLC指令成功");

                    // 发送后增加无响应计数器，如果后续收到数据会重置
                    onDataSent();

                } catch (Exception e) {
                    log.error("发送PLC指令异常: {}", e.getMessage());
                    handleConnectionLost();
                    break;
                }
            } else {
                log.debug("PLC未连接或连接不活跃，等待重连...");
            }

            try {
                Thread.sleep(1000);
            } catch (InterruptedException interruptedException) {
                log.warn("发送线程被中断");
                break;
            }
        }
        log.info("PLC数据发送循环结束");
    }

    // 辅助方法：将十六进制字符串转换为字节数组
    public static byte[] hexStringToByteArray(String hexString) {
        hexString = hexString.replace(" ", "");
        int len = hexString.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hexString.charAt(i), 16) << 4)
                    + Character.digit(hexString.charAt(i + 1), 16));
        }
        return data;
    }
}
