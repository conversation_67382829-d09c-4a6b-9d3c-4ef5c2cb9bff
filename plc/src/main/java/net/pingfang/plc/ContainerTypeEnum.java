package net.pingfang.plc;

import lombok.Getter;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 箱类型
 */
@Getter
public enum ContainerTypeEnum {
    // 0-单吊长箱  1-单吊短箱   2-孖吊 两个20尺  3-双吊 两个40尺  4-三箱吊(岸侧两个20尺,海侧一个40尺) 5-三箱吊(岸侧一个40尺,海侧两个20尺) 6-四箱吊 7-未知
    // 单吊长箱
    SL("单吊长箱", 0),

    // 单吊短箱
    SS("单吊短箱", 1),

    // 双吊 两个40尺
    T("双吊 两个40尺", 3),

    // 孖吊 两个20尺
    TD("孖吊 两个20尺", 2),

    // 三箱吊(岸侧两个20尺,海侧一个40尺)
    INVERSE_MIX("三箱吊(岸侧两个20尺,海侧一个40尺)", 4),

    // 三箱吊(岸侧一个40尺,海侧两个20尺)
    ALONG_MIX("三箱吊(岸侧一个40尺,海侧两个20尺)", 5),

    // 四箱
    TT("四箱吊", 6),

    // 未知
    UNKNOWN("未知", 7);

    private Integer value;
    private String desc;


    ContainerTypeEnum(String desc, Integer value) {
        this.value = value;
        this.desc = desc;
    }

    public static String getTypeByValue(Integer value) {
        String containerType = "";
        for (ContainerTypeEnum containerTypeEnum : ContainerTypeEnum.values()) {
            if (containerTypeEnum.getValue().equals(value)) {
                containerType = containerTypeEnum.getDesc();
            }
        }
        if(StringUtils.isEmpty(containerType)){
            return null;
        }
        return null;
    }


    public static String getDescByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (ContainerTypeEnum containerTypeEnum : ContainerTypeEnum.values()) {
            if (containerTypeEnum.getValue().equals(code)) {
                return containerTypeEnum.getDesc();
            }
        }
        return null;
    }


    public static Integer getCodeByDesc(String desc) {
        if (StringUtils.isEmpty(desc)) {
            return null;
        }
        for (ContainerTypeEnum containerTypeEnum : ContainerTypeEnum.values()) {
            if (containerTypeEnum.getDesc().equalsIgnoreCase(desc)) {
                return containerTypeEnum.getValue();
            }
        }
        return null;
    }


    public static List<Integer> changeDescToValueList(List<String> descList){
        if (CollectionUtils.isEmpty(descList)){
            return null;
        }
        List<Integer> result = new ArrayList<>();
        for (String desc : descList) {
            for (ContainerTypeEnum containerTypeEnum : ContainerTypeEnum.values()) {
                if (containerTypeEnum.getDesc().equals(desc)) {
                    result.add(containerTypeEnum.getValue());
                    break;
                }
            }
        }
        return result;
    }
}
