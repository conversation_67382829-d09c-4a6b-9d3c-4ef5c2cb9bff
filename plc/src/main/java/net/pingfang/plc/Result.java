package net.pingfang.plc;

import lombok.Data;

/**
 * @Author: CM
 * @Date: 2022/3/19 13:16
 * @Description: 接口返回对象
 */
@Data
public class Result<T> {
	/**
	 * 返回状态吗
	 */
	private Integer code;

	/**
	 * 返回信息
	 */
	private String msg;

	/**
	 * 返回数据
	 */
	private T data;

	/**
	 * 请求成功带返回值
	 * @param result 返回对象信息
	 * @return Result
	 */
	public static <T> Result<T> success(T result) {
		Result<T> response = new Result<T>();
		response.setCode(ResultCodeEnum.SUCCESS.getCode());
		response.setMsg(ResultCodeEnum.SUCCESS.getMsg());
		response.setData(result);
		return response;
	}

	/**
	 * 请求成功不带返回值
	 * @return Result
	 */
	public static <T> Result<T> success() {
		Result<T> response = new Result<T>();
		response.setCode(ResultCodeEnum.SUCCESS.getCode());
		response.setMsg(ResultCodeEnum.SUCCESS.getMsg());
		response.setData(null);
		return response;
	}

	/**
	 * 请求成功不带返回值
	 * @return Result
	 */
	public static <T> Result<T> success(String msg) {
		Result<T> response = new Result<T>();
		response.setCode(ResultCodeEnum.SUCCESS.getCode());
		response.setMsg(msg);
		response.setData(null);
		return response;
	}

	/**
	 * 请求失败带返回对象和提示信息
	 * @param result 返回对象信息
	 * @Param msg 失败提示信息
	 * @return Result
	 */
	public static <T> Result<T> fail(T result, String msg) {
		Result<T> response = new Result<T>();
		response.setCode(ResultCodeEnum.FAIL.getCode());
		response.setMsg(msg);
		response.setData(result);
		return response;
	}

	/**
	 * 请求失败只带提示信息
	 * @param msg 失败提示信息
	 * @return Result
	 */
	public static <T> Result<T> fail(String msg) {
		Result<T> response = new Result<T>();
		response.setCode(ResultCodeEnum.FAIL.getCode());
		response.setMsg(msg);
		response.setData(null);
		return response;
	}

	/**
	 * 请求失败自定义code和msg
	 * @param msg 失败提示信息
	 * @return Result
	 */
	public static <T> Result<T> fail(Integer code, String msg) {
		Result<T> response = new Result<T>();
		response.setCode(code);
		response.setMsg(msg);
		response.setData(null);
		return response;
	}

	/**
	 * 请求错误
	 * @return Result
	 */
	public static <T> Result<T> error(String errorTraceId) {
		Result<T> response = new Result<T>();
		response.setCode(ResultCodeEnum.ERROR.getCode());
		response.setMsg(ResultCodeEnum.ERROR.getMsg() + errorTraceId);
		response.setData(null);
		return response;
	}

}
