package net.pingfang.socket;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

public class EncodeUtil {
	
	/**
	 * List 添加方式
	 * 
	 * @param msg
	 * @return
	 */
	public static byte[] encodeMessage(String msg) {
		// 创建一个列表来存储字节
		List<Byte> temp = new ArrayList<>();
		// 添加头部
		byte[] head = { (byte) 0xFE, (byte) 0xEF };
		for (byte b : head) {
			temp.add(b);
		}
		// 获取消息的UTF-8编码字节
		byte[] body = msg.getBytes(StandardCharsets.UTF_8);
		// 获取信息长度并反转字节序
		byte[] size = intToByteArray(body.length);
		// 添加信息长度
		for (byte b : size) {
			temp.add(b);
		}
		// 添加解码后的消息
		for (byte b : body) {
			temp.add(b);
		}
		// 添加尾部
		byte[] end = { (byte) 0x0D, (byte) 0x0A };
		for (byte b : end) {
			temp.add(b);
		}
		// 将列表转换为字节数组
		byte[] result = new byte[temp.size()];
		for (int i = 0; i < temp.size(); i++) {
			result[i] = temp.get(i);
		}
		return result;
	}
    
	/**
	 * 封装报文拷贝方式
	 * 
	 * @param msg
	 * @return
	 */
    public static byte[] concatenateByteArrays(String msg) {
    	// 定义头
    	byte[] head = { (byte) 0xFE, (byte) 0xEF };  	
   	    // 定义报文
    	byte[] body = msg.getBytes(StandardCharsets.UTF_8);
    	// 定义长度
    	byte[] size = intToByteArray(body.length);  	
    	// 定义尾
   	    byte[] end = { (byte) 0x0D, (byte) 0x0A };
    	// 报文总长
        int totalLength = head.length + size.length + body.length + end.length;
        
        byte[] result = new byte[totalLength];
        
        System.arraycopy(head, 0, result, 0, head.length);
        System.arraycopy(size, 0, result, head.length, size.length);
        System.arraycopy(body, 0, result, head.length + size.length, body.length);
        System.arraycopy(end, 0, result, head.length + size.length + body.length, end.length);

        return result;
    }
    
   
    private static byte[] intToByteArray(int value) {
        return new byte[]{
            (byte) (value >> 24),
            (byte) (value >> 16),
            (byte) (value >> 8),
            (byte) value
        };
    }
    
    public static void main(String[] args) {
    	PlcMessage msg = new PlcMessage();
    	msg.setLockstatus(10);
    	msg.setLoadcondition(10);
    	msg.setLoadcondition(10);
    	msg.setLoadcondition(10);
    	msg.setSpreadery(10);
    	msg.setWeight(10F);
    	msg.setCartcoordinates(10);
        byte[] messageBytes = encodeMessage(msg.toString());
        // 打印结果，用于调试
       for (byte b : messageBytes) {
            System.out.format("%02X ", b);          
        }
        System.out.println("\n");
        
        byte[] byteLast = concatenateByteArrays(msg.toString());
       // System.out.println(new String(byteLast));
        for (byte b : byteLast) {
            System.out.format("%02X ", b);   
        //	System.out.println(b);
        }
    }

}
