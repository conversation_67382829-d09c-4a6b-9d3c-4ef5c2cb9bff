package net.pingfang.socket;

import com.alibaba.fastjson.JSONObject;

import lombok.Data;

/**
 * 发送应用端报文
 */
@Data
public class PlcMessage {
	/**
	 * 开闭锁状态
	 * 
	 * 0=未知、1=未锁、2=已锁
	 */
	private int lockstatus;
	/**
	 * 着箱状态
	 * 
	 * 0未知 1着箱 2未着箱
	 */
	private int loadcondition;
	/**
	 * 吊具水平坐标
	 */
	private int spreaderx;
	/**
	 * 箱类型
	 * 
	 * 0=未知、1=短箱、2=长箱40、3=长箱45、4=双箱
	 */
	private int containertype;
	/**
	 * 吊具垂直坐标
	 */
	private int spreadery;
	/**
	 * 吊具重量
	 * 
	 * 箱重, 单位:kg
	 */
	private Float weight;
	/**
	 * 大车坐标
	 */
	private int cartcoordinates;

	@Override
	public String toString() {
		return JSONObject.toJSONString(this);
	}
}
