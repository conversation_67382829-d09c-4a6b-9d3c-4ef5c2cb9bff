package net.pingfang.socket;

import java.nio.charset.Charset;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import io.netty.bootstrap.ServerBootstrap;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelInitializer;
import io.netty.channel.ChannelOption;
import io.netty.channel.ChannelPipeline;
import io.netty.channel.EventLoopGroup;
import io.netty.channel.FixedRecvByteBufAllocator;
import io.netty.channel.nio.NioEventLoopGroup;
import io.netty.channel.socket.nio.NioServerSocketChannel;
import io.netty.handler.codec.DelimiterBasedFrameDecoder;
import io.netty.handler.codec.bytes.ByteArrayEncoder;
import io.netty.handler.codec.string.StringEncoder;
import net.pingfang.config.Config;

/**
 * netty服务端，长连接
 *  
 * @title: NettyServer.java 
 * @package net.pingfang.lzl.controller 
 * @description: netty服务端，长连接。
 * @author: lzl
 * @date: 2022年3月30日 下午6:53:42 
 * @version: 1.0
 */
@Component
public class NettyServer {
	
	private static final Logger log = LoggerFactory.getLogger(NettyServer.class);
	
	@Autowired
	private ServerHandler serverHandler;
	
	@Resource
	private Config config;

	
    @PostConstruct
    public void startServer(){
        //1.定义server启动类
        ServerBootstrap serverBootstrap = new ServerBootstrap();

        //2.定义工作组:boss分发请求给各个worker:boss负责监听端口请求，worker负责处理请求（读写）
        EventLoopGroup boss = new NioEventLoopGroup();
        EventLoopGroup worker = new NioEventLoopGroup();

        //3.定义工作组
        serverBootstrap.group(boss,worker);

        //4.设置通道channel
        serverBootstrap.channel(NioServerSocketChannel.class);//A
        //serverBootstrap.channelFactory(new ReflectiveChannelFactory(NioServerSocketChannel.class));//旧版本的写法，但是此过程在A中有同样过程

        //5.添加handler，管道中的处理器，通过ChannelInitializer来构造
        serverBootstrap.childHandler(new ChannelInitializer<Channel>() {
            @Override
            protected void initChannel(Channel channel) throws Exception {
                //此方法每次客户端连接都会调用，是为通道初始化的方法
            	//获得通道channel中的管道链（执行链、handler链）
            	//byte[] end = {(byte)0x03};
            	byte[] end = { (byte) 0x0D, (byte) 0x0A};
            	ByteBuf delimiter = Unpooled.copiedBuffer(end);
            	//ByteBuf delimiter = Unpooled.copiedBuffer("$PFKJ$".getBytes()); //拆包也可以用某个字符或者字符串拆包
                ChannelPipeline pipeline = channel.pipeline();
                pipeline.addLast(new DelimiterBasedFrameDecoder(5000,delimiter)); //拆包通过DelimiterBasedFrameDecoder解码器拆包，以0x03分割。
                pipeline.addLast(new StringEncoder(Charset.forName(config.getSocketServerCoding())));
                pipeline.addLast("serverHandler",serverHandler);
                pipeline.addLast(new ByteArrayEncoder());
                log.info("success to initHandler!");
            }
        });

        //6.设置参数
        //设置参数，TCP参数
        serverBootstrap.option(ChannelOption.SO_BACKLOG, 128);//连接缓冲池的大小
        serverBootstrap.childOption(ChannelOption.RCVBUF_ALLOCATOR, new FixedRecvByteBufAllocator(5000));
        serverBootstrap.childOption(ChannelOption.SO_KEEPALIVE, true);//维持链接的活跃，清除死链接
        serverBootstrap.childOption(ChannelOption.TCP_NODELAY, false);//关闭延迟发送
        //7.绑定ip和port
        try {
            ChannelFuture channelFuture = serverBootstrap.bind(config.getSocketServerPort()).sync();//Future模式的channel对象
            log.info("socket服务已启动，端口：{}",config.getSocketServerPort());
            // 监听关闭
            channelFuture.channel().closeFuture().sync(); //等待服务关闭，关闭后应该释放资源
        } catch (InterruptedException e) {
        	log.info("socket服务启动失败："+e.getMessage());
            e.printStackTrace();
        } finally {
            //8.优雅的关闭资源
            boss.shutdownGracefully();
            worker.shutdownGracefully();
        }
    }
}
