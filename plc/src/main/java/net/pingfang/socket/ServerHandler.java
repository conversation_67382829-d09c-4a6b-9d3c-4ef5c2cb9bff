package net.pingfang.socket;

import java.io.UnsupportedEncodingException;
import java.util.concurrent.ConcurrentHashMap;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import io.netty.channel.ChannelHandler;
import io.netty.channel.ChannelHandlerContext;
import io.netty.channel.ChannelInboundHandlerAdapter;
import io.netty.util.ReferenceCountUtil;
import net.pingfang.config.Config;

/**
 * nettyServer消息处理类
 * 
 * @title: ServerHandler.java
 * @package net.pingfang.lzl.controller
 * @description: nettyServer消息处理类
 * @author: lzl
 * @date: 2022年3月30日 下午6:52:57
 * @version: 1.0
 */

@Component
@ChannelHandler.Sharable
public class ServerHandler extends ChannelInboundHandlerAdapter {

	private static final Logger log = LoggerFactory.getLogger(ServerHandler.class);

	//Map<String, ChannelHandlerContext> clientMap = new HashMap<>();
	public static final ConcurrentHashMap<String, ChannelHandlerContext> clientMap = new ConcurrentHashMap<>();

	@Resource
	private Config config;

	
	/**
	    * 注册和激活：当客户端连接时，首先会触发注册，进行一些初始化的工作，然后激活连接，就可以收发消息了。
	    * 断开和注销：当客户端断开时，反向操作，先断开，再注销。
	    * 读取消息：当收到客户端消息时，首先读取，然后触发读取完成。
	    * 发生异常：不多解释了。
	    * 用户事件：由用户触发的各种非常规事件，根据evt的类型来判断不同的事件类型，从而进行不同的处理。
	    * 可写状态变更：收到消息后，要回复消息，会先把回复内容写到缓冲区。而缓冲区大小是有一定限制的，当达到上限以后，可写状态就会变为否，不能再写。等缓冲区的内容被冲刷掉后，缓冲区又有了空间，可写状态又会变为是。
	*/

	/**
	 * 注册
	 */
	public void channelRegistered(ChannelHandlerContext ctx) throws Exception {
		log.info("Channel 注册：{} 到NioEventLoop。", ctx.channel());
		super.channelRegistered(ctx);
	}

	/**
	 * 注销
	 */
	public void channelUnregistered(ChannelHandlerContext ctx) throws Exception {
		log.info("Channel 注销：{} ，channel取消和NioEventLoop的绑定 ", ctx.channel());
		super.channelUnregistered(ctx);
	}

	/**
	 * 客户端连接—>激活
	 */
	@Override
	public void channelActive(ChannelHandlerContext ctx) throws Exception {
		clientMap.put(ctx.channel().id().toString(), ctx);
		log.info("客户端:[{}]已连接", ctx.channel().id().toString());
	}

	/**
	 * 读取数据->读取消息
	 */
	@Override
	public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
		ByteBuf buf = (ByteBuf) msg;
		String message = messageConverStr(buf);
		String sessionId = ctx.channel().id().toString();
		log.info("收到客户端：[{}] 信息:{}", sessionId,message);
		if (StringUtils.isEmpty(message)) {
			log.info("收到客户端信息错误。");
		}
		
	}

	/**
	 * 消息读取完成
	 */
	@Override
	public void channelReadComplete(ChannelHandlerContext ctx) throws Exception {
		log.info("服务端接收数据完毕..");
		ctx.flush();
	}

	/**
	 * 写出数据
	 */
	public void channelWrite(String msg) {
		log.info("发送客户端报文：{}", msg);
		if(clientMap.isEmpty()) {
			log.info("未找到客户端连接。。。");
			return;
		}
		
		clientMap.forEach((key, value) -> {
			try {
				value.writeAndFlush(msg);
				value.writeAndFlush(Unpooled.copiedBuffer(EncodeUtil.concatenateByteArrays(msg)));
				log.info("给客户端发送报文成功，发送sessionId:{}", key);
			} catch (Exception e) {
				log.info("给客户端发送报文失败：{}", e.getMessage());
			} finally {
				ReferenceCountUtil.release(msg);
			}
		});
	}

	/**
	 * 发生异常时被调用->发生异常
	 */
	@Override
	public void exceptionCaught(ChannelHandlerContext ctx, Throwable t) throws Exception {
		ctx.close();
		log.info("异常信息：{}" ,t.getMessage());
	}

	/**
	 * 关闭连接时->断开
	 */
	@Override
	public void channelInactive(ChannelHandlerContext ctx) throws Exception {
		log.info("客户端关闭连接：{}" ,ctx);
		ChannelHandlerContext oldctx = clientMap.get(ctx.channel().id().toString());
		oldctx.close();
		clientMap.remove(ctx.channel().id().toString());
		ctx.close();
		super.channelInactive(ctx);
	}

	/**
	 * 消息转化
	 */
	private String messageConverStr(ByteBuf byteBuf) {
		byte[] bytes = new byte[byteBuf.readableBytes()];
		byteBuf.readBytes(bytes);
		try {
			String msg = new String(bytes, config.getSocketServerCoding());
			return msg;
		} catch (UnsupportedEncodingException e) {
			log.error("消息转化失败：{}",e.getMessage());
			return null;
		} finally {
			byteBuf.release();
		}
	}
}
