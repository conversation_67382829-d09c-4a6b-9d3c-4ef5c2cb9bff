#server port
server.port=9704

#service name
spring.application.name=PF_PLC

project.version=@project.version@

#log setting
#log level
logging.level.root=info
logging.pattern.file=%d{yyyy/MM/dd HH:mm:ss.SSS} %clr(%p)[TraceId:%3X{traceId}] [%logger][%M][%L] : %m%n
logging.pattern.console=%d{yyyy/MM/dd HH:mm:ss.SSS} %clr(%p)[TraceId:%3X{traceId}] [%logger][%M][%L] : %m%n
#log path
logging.file.path=D:/log/plc
#log file name
logging.file.name=D:/log/plc/plc.log
#log keep day
logging.file.max-history=7
#log max size
logging.file.max-size=20MB


#upload file max size
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB


#PLC setting
#Plc port
plc.server.port=12669
#plc ip
plc.server.ip=*************
#crane id (1-é¨å1ï¼2-é¨å2ï¼3-é¨å3)
plc.crane.id=1
#system type (1-OCR, 2-CCTV)
plc.system.type=1

#application url
application.url=http://127.0.0.1:9703/plc/send

#################tcpæå¡éç½®#################
###çå¬
socket.server.port=6888
socket.server.coding=UTF-8
