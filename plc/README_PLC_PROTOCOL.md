# PLC协议解析说明

## 协议格式

### 包头结构
- 包头固定值：`47 49 54 53` ("GITS")
- 消息类型：`00 01`
- 交互门吊：`00 01` (门吊ID)
- 系统标识：`00 01` (1-OCR, 2-CCTV)
- 数据长度：4字节

### 数据体结构（按字节顺序）

| 序号 | 字段名称 | 字节数 | 数据类型 | 说明 | 示例 | 对应Java字段 |
|------|----------|--------|----------|------|------|--------------|
| 1 | 大车坐标 | 4 | Float | 大车位置坐标 | | plcX |
| 2 | 小车坐标 | 4 | Float | 小车位置坐标，目标车道8~10 | 41 08 CC CD=8.55 | plcY |
| 3 | 吊具坐标plcz | 4 | Float | 吊具高度坐标 | 41 18 CC CD=9.55 | plcZ |
| 4 | 吊具重量 | 4 | Float | 吊具当前重量 | | weight |
| 5 | 开闭锁状态 | 1 | Byte | 1-闭锁 2-开锁 | 01 | lock |
| 6 | 吊具类型 | 1 | Byte | 吊具类型(20,40,45) | 02 | containerType |
| 7 | 数据推送时间 | 8 | Long | 数据推送时间戳 | | pushTime |
| 8 | 吊具上下伸速度 | 4 | Float | 吊具垂直移动速度 | | hoistSpeed |
| 9 | 大车指令输出速度 | 4 | Float | 大车指令速度 | | gantryCommandSpeed |
| 10 | 小车速度 | 4 | Float | 小车移动速度 | | trolleySpeed |
| 11 | 电气房侧大车速度 | 4 | Float | 电气房侧大车速度 | | gantryElectricalSpeed |
| 12 | 任务状态 | 2 | Short | 0-无任务 1-任务正在作业中 | | taskStatus |
| 13 | 作业类型 | 2 | Short | 见作业类型说明 | | workType |
| 14 | 吊具距离箱面高度 | 4 | Float | 距离箱面高度，999表示未到达 | | spreaderFromContainerHeight |
| 15 | 小车位置起点 | 4 | Float | 小车起始位置 | | trolleyStartPosition |
| 16 | 大车位置起点 | 4 | Float | 大车起始位置 | | gantryStartPosition |
| 17 | 吊具位置起点 | 4 | Float | 吊具起始位置 | | hoistStartPosition |

### 作业类型说明
- 0: 未知
- 1: 列车至堆场
- 2: 集卡至堆场
- 3: 列车至集卡
- 4: 堆场至列车
- 5: 堆场至集卡
- 6: 集卡至列车
- 7: 堆场内倒箱
- 8: 列车至列车
- 9: 堆场定位
- 10: 火车定位
- 11: 火车车头定位

### 字段说明

#### 原有字段（保持不变）
- **plcX**: 大车坐标，4字节Float转为int
- **plcY**: 小车坐标，4字节Float转为int
- **plcZ**: 吊具坐标，4字节Float（新增）
- **weight**: 吊具重量，4字节Float转为int
- **lock**: 开闭锁状态，1字节（转换后：0-开锁,1-闭锁）
- **containerType**: 吊具类型，1字节

#### 新增字段
- **pushTime**: 数据推送时间，8字节Long
- **hoistSpeed**: 吊具上下伸速度，4字节Float
- **gantryCommandSpeed**: 大车指令输出速度，4字节Float
- **trolleySpeed**: 小车速度，4字节Float
- **gantryElectricalSpeed**: 电气房侧大车速度，4字节Float
- **taskStatus**: 任务状态，2字节Short
- **workType**: 作业类型，2字节Short
- **spreaderFromContainerHeight**: 吊具距离箱面高度，4字节Float
- **trolleyStartPosition**: 小车位置起点，4字节Float
- **gantryStartPosition**: 大车位置起点，4字节Float
- **hoistStartPosition**: 吊具位置起点，4字节Float

### 开闭锁状态转换规则

**原始协议值 → 系统内部lock字段**
- 1 (闭锁) → lock=1
- 2 (开锁) → lock=0

### 示例数据

```
发送: 47 49 54 53 00 01 00 01 00 01 00 00 00 00
接收: 47 49 54 53 00 01 00 01 00 00 00 00 00 2A 00 00 00 00 41 08 CC CD 41 18 CC CD 00 00 00 00 01 02 00 00 01 96 32 1E 3A 56 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
```

解析结果：
- 小车坐标: 8.55 (41 08 CC CD)
- 吊具坐标: 9.55 (41 18 CC CD)
- 开闭锁状态: 闭锁 (01)
- 吊具类型: 02

## 使用方法

```java
// 解析PLC数据
Plc plc = PlcProtocolParser.parseResponse(hexData);

// 访问原有字段
int x = plc.getPlcX();          // 大车坐标
int y = plc.getPlcY();          // 小车坐标
Float z = plc.getPlcZ();        // 吊具坐标（新增）
Integer weight = plc.getWeight(); // 吊具重量
Integer lock = plc.getLock();   // 开闭锁状态：0-开锁, 1-闭锁
Integer containerType = plc.getContainerType(); // 吊具类型

// 访问新增字段
Long pushTime = plc.getPushTime();
Float hoistSpeed = plc.getHoistSpeed();
Float gantryCommandSpeed = plc.getGantryCommandSpeed();
Float trolleySpeed = plc.getTrolleySpeed();
Float gantryElectricalSpeed = plc.getGantryElectricalSpeed();
Short taskStatus = plc.getTaskStatus();
Short workType = plc.getWorkType();
Float containerHeight = plc.getSpreaderFromContainerHeight();
Float trolleyStartPos = plc.getTrolleyStartPosition();
Float gantryStartPos = plc.getGantryStartPosition();
Float hoistStartPos = plc.getHoistStartPosition();
```

## RTSP预识别触发条件

基于新协议字段，RTSP预识别的触发条件如下：

### 基础条件
1. **功能启用**: `identity.ifRtspDetect=true`
2. **开锁状态**: `plc.getLock() == 0`
3. **有效序列号**: `seqNo != null`
4. **未在识别**: `!RtspService.isCapturing`

### 新增条件（基于新协议字段）
5. **任务状态**: `taskStatus == 1` (作业中)
6. **作业类型**: `workType >= 1 && workType <= 8` (排除定位类型9、10、11)
7. **箱面高度**: `spreaderFromContainerHeight != 999 && > 0` (吊具到达箱面)
8. **移动距离**: `|plcY - trolleyStartPosition| >= 0.5` (小车移动距离足够)

### 作业类型说明
- **1-8**: 涉及抓箱作业，触发预识别
  - 1: 列车至堆场 ✅
  - 2: 集卡至堆场 ✅
  - 3: 列车至集卡 ✅
  - 4: 堆场至列车 ✅
  - 5: 堆场至集卡 ✅
  - 6: 集卡至列车 ✅
  - 7: 堆场内倒箱 ✅
  - 8: 列车至列车 ✅

- **9-11**: 定位类型，不涉及抓箱，不触发预识别
  - 9: 堆场定位 ❌
  - 10: 火车定位 ❌
  - 11: 火车车头定位 ❌

## 注意事项

1. **向后兼容**: 保持原有字段的数据类型和含义完全不变
2. **条件解析**: 新增字段使用条件解析，如果数据长度不足会跳过
3. **无重复字段**: 移除了与原有字段重复的新字段，避免混淆
4. **新增plcZ**: 唯一新增到原有字段组的是plcZ（吊具坐标）
5. **数据类型**: 新字段使用更精确的Float/Long/Short类型
6. **精确触发**: 新的预识别条件更加精确，避免不必要的触发
