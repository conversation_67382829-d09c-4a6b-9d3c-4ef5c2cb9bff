package net.pingfang.api;

import net.pingfang.core.GpsReceiveService;
import net.pingfang.model.annotation.Log;
import net.pingfang.model.common.FixSizeLinkedList;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.GpsDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024-05-24 17:00
 */
@RestController
@RequestMapping("/gps")
@Slf4j
public class GpsApi {

    @Autowired
    private GpsReceiveService gpsReceiveService;


    /**
     * 获取对应设备的GPS数据
     * @param gpsUniqueCode
     * @return
     */
    @Log(operate = "新增/修改ISO信息")
    @GetMapping("/queryGps")
    public Result<FixSizeLinkedList<GpsDto>> queryGps(@RequestParam("gpsUniqueCode") String gpsUniqueCode){
        return gpsReceiveService.queryGps(gpsUniqueCode);
    }

//    @GetMapping("/test")
//    public Result<String> test(){
//        log.info("发送gps数据给：");
//        GpsDto gps = new GpsDto();
//        gps.setLatitude(String.valueOf(new BigDecimal(1080000).divide(BigDecimal.valueOf(1000000))));
//        gps.setLongitude(String.valueOf(new BigDecimal(22000000).divide(BigDecimal.valueOf(1000000))));
//        gps.setAltitude(27+"")
//                .setDirection(27+"")
//                .setDateTime(27+"")
//                .setExtendLatitude(27)
//                .setSeq(27+"")
//                .setInfoId(27+"")
//                .setMsgLength(27+"")
//                .setSpeed(27+"")
//                .setExtendLongitude(27)
//                .setGpsUniqueCode(27+"")
//                .setQuantityOfElectricity(27)
//                .setPositioningStatus(27)
//                .setSignalStrength(27);
//            RestTemplateConfig.getInstance().postForObject("http://192.168.1.31:9703/gps/save", gps, Result.class);
//            log.info("发送完成:"+gps);
//
//        return null;
//    }



}
