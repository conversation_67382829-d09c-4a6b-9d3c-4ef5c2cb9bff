package net.pingfang.model.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Author: CM
 * @Date: 2022/3/19 20:17
 * @Description:
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.METHOD})
@Documented
public @interface Log {
    /**
     * 日志类型0-不打印/记录到数据库 1-打印出入参/记录到数据库 2-只打印入参/记录到数据库 3-只打印出参/记录到数据库 4-不打印不记录 5-只打印出入参不记录
     */
    int value() default 1;

    /**
     * 操作名称
     */
    String operate() default "";
}
