package net.pingfang.model.common;

import java.util.LinkedList;

/**
 * @description: 固定长度的List，如果大于容量，则删除最早放进去的那个处于链表顶端的元素
 * @title: FixSizeLinkedList
 * @author: cb
 * @date: 2024-06-12 14:25:06
 * @version: 1.0
 */
public class FixSizeLinkedList<T> extends LinkedList<T> {
    private int capacity;

    public FixSizeLinkedList(int capacity){
        super();
        this.capacity = capacity;
    }

    @Override
    public boolean add(T t) {
        if (size() + 1 > capacity){
            super.removeFirst();
        }
        return super.add(t);
    }
}
