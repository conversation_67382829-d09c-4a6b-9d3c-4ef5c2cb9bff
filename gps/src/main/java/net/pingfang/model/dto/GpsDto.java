package net.pingfang.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import lombok.Data;
import lombok.ToString;

/**
 * @description: GpsDto定位数据信息
 * @title: Gps
 * @author: cb
 * @date: 2024-06-12 15:12:10
 * @version: 1.0
 */
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@ToString
public class GpsDto {
    /**
     * 消息id
     */
    private String infoId;
    /**
     * 消息体长度
     */
    private String msgLength;
    /**
     * 设备唯一号
     */
    private String gpsUniqueCode;
    /**
     * 消息流水号
     */
    private String seq;
    /**
     * 纬度
     */
    private String latitude;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 海拔高度
     */
    private String altitude;

    /**
     * 速度
     */
    private String speed;

    /**
     * 方向
     */
    private String direction;

    /**
     * 日期时间
     */
    private String dateTime;

    /**
     * 信号强度
     */
    private Integer signalStrength;

    /**
     * 电量
     */
    private Integer quantityOfElectricity;

    /**
     * 定位状态,
     * ZERO( value: 0,desc:"状态不可用")
     * ONE( value: 1,desc:"单点解")
     * TWO( value: 2,desc:"亚米级定位")
     * THREE( value: 3,desc: "PpS")
     * FOUR( value:4，desc:"RTK固定解")
     * FIVE( value:5, desc: "RTK浮点解")
     * SIX( value:6， desc:"航位推算")
     * SEVEN( value: 7，desc:"手动输入模式")
     * EIGHT( value: 8,desc:"模拟模式")
     * NINE( value: 9,desc:"未用上")
     */
    private Integer positioningStatus;

    /**
     * 经度扩展
     */
    private Integer extendLongitude;
    /**
     * 纬度扩展
     */
    private Integer extendLatitude;
}
