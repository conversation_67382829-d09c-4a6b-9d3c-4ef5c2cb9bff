package net.pingfang.util;

import java.security.MessageDigest;

/**
 * 生成16位md5加密
 *
 * @author: lzl
 * @date: 2023年2月28日 下午3:58:55
 * @version: 3.0
 */
public class MD5Util {

    public static String getMD5Str(String str) {

        MessageDigest messageDigest = null;
        try {
            messageDigest = MessageDigest.getInstance("MD5");
            messageDigest.reset();
            messageDigest.update(str.getBytes("UTF-8"));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        byte[] byteArray = messageDigest.digest();

        StringBuffer md5StrBuff = new StringBuffer();

        for (int i = 0; i < byteArray.length; i++) {
            if (Integer.toHexString(0xFF & byteArray[i]).length() == 1)
                md5StrBuff.append("0").append(Integer.toHexString(0xFF & byteArray[i]));
            else
                md5StrBuff.append(Integer.toHexString(0xFF & byteArray[i]));
        }
        //16位加密，从第9位到25位  大写
        //return md5StrBuff.substring(8, 24).toString().toUpperCase();
        //16位加密，从第9位到25位  小写
        return md5StrBuff.substring(8, 24).toString();
    }

}
