package net.pingfang.util;

import java.util.HashMap;
import java.util.Map;
import org.springframework.util.StringUtils;

/**
 * ThreadLocal工具类
 *
 * @author: CM
 * @date: 2023/4/24
 */
public class ThreadLocalUtils {

    public static final String TENANT = "TENANT";


    private static final ThreadLocal<Map<String, String>> threadLocal = ThreadLocal.withInitial(() -> new HashMap<>(10));

    public static Map<String, String> getThreadLocal() {
        return threadLocal.get();
    }

    public static String get(String key) {
        Map<String, String> map = threadLocal.get();
        return map.get(key);
    }
    public static Long getTenant() {
        Map<String, String> map = threadLocal.get();
        String tanant = map.get(TENANT);
        return StringUtils.isEmpty(tanant)?null:Long.valueOf(tanant);
    }
    public static void setTenant(String value) {
        Map<String, String> map = threadLocal.get();
        map.put(TENANT, value);
    }

    public static void set(String key, String value) {
        Map<String, String> map = threadLocal.get();
        map.put(key, value);
    }

    public static void set(Map<String, String> keyValueMap) {
        Map<String, String> map = threadLocal.get();
        map.putAll(keyValueMap);
    }

    public static void remove() {
        threadLocal.remove();
    }

    public static void remove(String key) {
        Map<String, String> map = threadLocal.get();
        map.remove(key);
    }
}
