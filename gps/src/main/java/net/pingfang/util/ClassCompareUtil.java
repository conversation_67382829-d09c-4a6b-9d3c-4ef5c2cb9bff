package net.pingfang.util;

import java.beans.Introspector;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 实体类值比对工具
 * <AUTHOR>
 * @Date 2022/5/19 15:54
 * @Description:
*/
public class ClassCompareUtil {

    /**
     * 比较两个实体属性值，返回一个boolean,true则表时两个对象中的属性值无差异
     * @param oldObject 进行属性比较的对象1
     * @param newObject 进行属性比较的对象2
     * @return 属性差异比较结果boolean
     */
    public static boolean compareObject(Object oldObject, Object newObject) {
        Map<String,Map<String,Object>> resultMap=compareFields(oldObject,newObject);
        if(resultMap.size()>0) {
            return false;
        }else {
            return true;
        }
    }

    /**
     * 比较两个实体属性值，返回一个map以有差异的属性名为key，value为一个Map分别存oldObject,newObject此属性名的值
     * @param oldObject 进行属性比较的对象1
     * @param newObject 进行属性比较的对象2
     * @return 属性差异比较结果map
     */
    @SuppressWarnings("rawtypes")
    public static Map<String,Map<String,Object>> compareFields(Object oldObject, Object newObject) {
        Map<String,Map<String,Object>> map = null;
        try{
            //只有两个对象都是同一类型的才有可比性
            if (oldObject.getClass() == newObject.getClass()) {
                map = new HashMap<>();

                Class clazz = oldObject.getClass();
                //获取object的所有属性
                PropertyDescriptor[] pds = Introspector.getBeanInfo(clazz,Object.class).getPropertyDescriptors();

                for (PropertyDescriptor pd : pds) {
                    //遍历获取属性名
                    String name = pd.getName();
                    //获取属性的get方法
                    Method readMethod = pd.getReadMethod();
                    // 在oldObject上调用get方法等同于获得oldObject的属性值
                    Object oldValue = readMethod.invoke(oldObject);
                    // 在newObject上调用get方法等同于获得newObject的属性值
                    Object newValue = readMethod.invoke(newObject);

                    //比对传入的值是否是list
                    if(oldValue instanceof List){
                        continue;
                    }
                    if(newValue instanceof List){
                        continue;
                    }
                    //比对传入的值是否为时间
                    if(oldValue instanceof Timestamp){
                        oldValue = new Date(((Timestamp) oldValue).getTime());
                    }
                    if(newValue instanceof Timestamp){
                        newValue = new Date(((Timestamp) newValue).getTime());
                    }

//                    if(oldValue == null && newValue == null){ //比对传入的值是否为空
//                        continue;
//                    }
                    if(newValue == null){//调整为，出现新值为null时不必比对，不存入map;新值不为null，旧值为null，直接存入map
                        continue;
                    }else if(oldValue == null && newValue != null){
                        Map valueMap = new HashMap();
                        valueMap.put("oldValue",oldValue);
                        valueMap.put("newValue",newValue);
                        map.put(name, valueMap);
                        continue;
                    }

                    if (!oldValue.equals(newValue)) {// 比较这两个值是否相等,不等就可以放入map了
                        Map valueMap = new HashMap();
                        valueMap.put("oldValue",oldValue);
                        valueMap.put("newValue",newValue);
                        map.put(name, valueMap);
                    }
                }
            }
        }catch(Exception e){
            e.printStackTrace();
        }
        return map;
    }

    /**
     * 根据属性名获取属性值
     * */
    public static String getFieldValueByName(String fieldName, Object o) {
        try {
            String firstLetter = fieldName.substring(0, 1).toUpperCase();
            String getter = "get" + firstLetter + fieldName.substring(1);
            Method method = o.getClass().getMethod(getter, new Class[] {});
            Object value = method.invoke(o, new Object[] {});
            return value.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
}
