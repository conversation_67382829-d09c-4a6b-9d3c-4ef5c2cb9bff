package net.pingfang.util;

public class Test {

	public static void main(String[] args) {
		// TODO Auto-generated method stub
		/**
			字段0：$GPGGA，语句ID，表明该语句为Global Positioning System Fix Data（GGA）GPS定位信息
			字段1：UTC 时间，hhmmss.sss，时分秒格式
			字段2：纬度ddmm.mmmm，度分格式（前导位数不足则补0）
			字段3：纬度N（北纬）或S（南纬）
			字段4：经度dddmm.mmmm，度分格式（前导位数不足则补0）
			字段5：经度E（东经）或W（西经）
			字段6：GPS状态，0=不可用(FIX NOT valid)，1=单点定位(GPS FIX)，2=差分定位(DGPS)，3=无效PPS，4=实时差分定位（RTK FIX），5=RTK FLOAT，6=正在估算
			字段7：正在使用的卫星数量（00 - 12）（前导位数不足则补0）
			字段8：HDOP水平精度因子（0.5 - 99.9）
			字段9：海拔高度（-9999.9 - 99999.9）
			字段10：单位：M（米）
			字段11：地球椭球面相对大地水准面的高度 WGS84水准面划分
			字段12：WGS84水准面划分单位：M（米）
			字段13：差分时间（从接收到差分信号开始的秒数，如果不是差分定位将为空）
			字段14：差分站ID号0000 - 1023（前导位数不足则补0，如果不是差分定位将为空）
			字段15：校验值
			获取到经纬度后还要公式计算出准确的经纬度
		 */
		String a = "$GNGGA,085828.000,2240.594843,N,11403.646318,E,5,23,0.59,165.905,M,-3.24,M,1,2824*65";
		String[] as = a.split("\\,");
		for (int i = 0; i < as.length; i++) {
			System.out.println(i+"=="+as[i]);
		}
	}

}
