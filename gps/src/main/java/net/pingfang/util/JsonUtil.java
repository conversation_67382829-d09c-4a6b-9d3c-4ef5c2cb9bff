package net.pingfang.util;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Author: CM
 * @Date: 2021/7/17 9:26
 * @Description:
 */
public class JsonUtil {
    private final static Logger log = LoggerFactory.getLogger(JsonUtil.class);

    public static String toJson(Object obj) {
        if (obj == null) {
            return null;
        }
        try {
            return JSONObject.toJSONString(obj, SerializerFeature.WriteMapNullValue);
        }catch (Exception e){
            return null;
        }
    }

    public static <T> T toObject(String json, Class<T> clazz){
        if (StringUtils.isEmpty(json)){
            return null;
        }
        T result = null;
        try {
            result = JSONObject.parseObject(json, clazz);
        }catch (Exception exception){
            log.error(ExceptionUtil.getStackTrace(exception));
        }
        return result;
    }

}
