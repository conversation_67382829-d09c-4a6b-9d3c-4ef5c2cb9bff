package net.pingfang.util;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: CM
 * @Date: 2022/3/19 16:25
 * @Description: 集合分割工具类
 */
public class CollectSplitUtil {

    /**

     返回处理好的list
     @param list 数据源
     @param batchCount 分批数量 例子：假如是1000 则每1000条分批一次
     @return 返回分批好的数据源
     */
    public static <T> List<List<T>> getBatchList(Collection<T> list, Integer batchCount){
        return Stream.iterate(0, n -> n + 1).limit(countStep(list.size(), batchCount)).parallel().map(a -> list.stream().skip((long) a * batchCount).limit(batchCount).parallel().collect(Collectors.toList())).collect(Collectors.toList());
    }

    /**
     * 计算切分次数
     */
    private static Integer countStep(Integer size, Integer batchCount) {
        return (size + batchCount - 1) / batchCount;
    }

}
