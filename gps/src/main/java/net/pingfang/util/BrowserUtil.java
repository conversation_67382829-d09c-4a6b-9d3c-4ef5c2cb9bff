package net.pingfang.util;

import eu.bitwalker.useragentutils.UserAgent;
import javax.servlet.http.HttpServletRequest;

/**
 * 判断浏览器类型工具类
 *
 * @Author: CM
 * @Date: 2022/6/14 17:52
 */
public class BrowserUtil {

    /**
     * 获取浏览器信息
     * @param request 请求对象
     * @return 浏览器
     */
    public static String getBrowser(HttpServletRequest request) {
        return UserAgent.parseUserAgentString(request.getHeader("User-Agent")).getBrowser().getName();
    }

    /**
     * 获取操作系统信息
     * @param request 请求对象
     * @return 浏览器
     */
    public static String getOperatingSystem(HttpServletRequest request) {
        return UserAgent.parseUserAgentString(request.getHeader("User-Agent")).getOperatingSystem().getName();
    }


}
