package net.pingfang.util;

/**
 * <AUTHOR>
 * @CreateTime 2023/7/25 14:35
 * @Description
 */

public class HexUtil {
    /**
     * Convert byte[] to hex string.这里我们可以将byte转换成int，然后利用Integer.toHexString(int)来转换成16进制字符串。
     * @param src byte[] data
     * @return hex string
     */
    public static String bytesToHexString(byte[] src){
        StringBuilder stringBuilder = new StringBuilder("");
        if (src == null || src.length <= 0) {
            return null;
        }
        for (int i = 0; i < src.length; i++) {
            int v = src[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        return stringBuilder.toString();
    }

    /**
     * Hex转byte[]，两种情况，Hex长度为奇数最后一个字符会被舍去
     */
    public static byte[] hexTobytes(String hex) {
        if (hex.length() < 1) {
            return null;
        } else {
            byte[] result = new byte[hex.length() / 2];
            int j = 0;
            for(int i = 0; i < hex.length(); i+=2) {
                result[j++] = (byte)Integer.parseInt(hex.substring(i,i+2), 16);
            }
            return result;
        }
    }


    /**
     * 输入一个16进制字符串 获取异或后的校验码
     */
    public static String getValidateCode(String[] strs) {
        int a = Integer.parseInt(strs[0],16);
        int b = Integer.parseInt(strs[1],16);
        for (int i = 1; i < strs.length; ) {
            //计算a 的新值
            a = a^b;
            i++;
            if (i >= strs.length) {
                break;
            }
            b = Integer.parseInt(strs[i],16);
        }
        return Integer.toHexString(a);
    }

    public static String calculateChecksum(String data){
        byte[] bytes= data.getBytes();
        byte checksum = 0;
        for (byte b : bytes) {
            checksum ^= b;
        }
        return String.format("%02X", checksum);

    }

    /**
     * 字符串拆分成数组
     */
    public static String[] split(String message) {
        int size = message.length()/2;
        String[] res = new String[size];
        for (int i = 0; i < size; i++) {
            res[i] = message.substring(2*i, 2*i+2);
        }
        return res;
    }



    public static void main(String[] args) {
        //String[] message = {"01","02","00","0c","29","30","59","30","87","15","00","b4","32","39","33","30","35","39","33","30","38","37","31","35"};
//        String[] message = {"01", "02", "00","0c","29","30","59","30","87","15","02","93","32","39","33","30","35","39","33","30","38","37","31","35"};
//        System.out.println(getValidateCode(message));
        System.out.println(calculateChecksum("80010005293059288966003d003d010200"));


    }
}
