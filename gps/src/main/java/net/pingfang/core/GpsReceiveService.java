package net.pingfang.core;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.ServerSocket;
import java.net.Socket;
import net.pingfang.constant.GpsConstant;
import net.pingfang.model.common.FixSizeLinkedList;
import net.pingfang.model.common.Result;
import net.pingfang.model.dto.GpsDto;
import net.pingfang.model.dto.QueryGpsResDTO;
import net.pingfang.model.vo.TosMessageVO;
import net.pingfang.util.DateUtils;
import net.pingfang.util.ExceptionUtil;
import net.pingfang.util.HexUtil;
import net.pingfang.util.JsonUtil;
import net.pingfang.util.RestTemplateUtil;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @CreateTime 2023/7/25 11:47
 * @Description
 */
@Slf4j
@Component
public class GpsReceiveService implements CommandLineRunner {


    public static final ConcurrentHashMap<String, Socket> safeOperateSocketMap = new ConcurrentHashMap<>();
    public static final String PF_SERVER_ = "PF_SERVER_";
    public static final Map<String, QueryGpsResDTO> gpsMap = new HashMap<>();
    public static final ConcurrentHashMap<String, FixSizeLinkedList<GpsDto>> locationMap = new ConcurrentHashMap<>();

    @Value("${safe.gps.socket.port:12347}")
    private Integer safeGpsSocketPort;

    @Value("${gps.send.open}")
    private Boolean sendOpen;

    @Value("${gps.send.url}")
    private String gpsUrl;

//    @Value("${gps.send.noOne}")
//    private String noOne;
//
//    @Value("${gps.send.noTwo}")
//    private String noTwo;

    @Resource
    private RestTemplateUtil restTemplateUtil;

    public static final Map<String, String> allowIPs = new HashMap<>();


    ExecutorService pool = Executors.newFixedThreadPool(8);

    @Override
    public void run(String... args) throws Exception {
        new Thread(() -> {
            try {
                //服务端启动，监听端口12345，等待客户端连接
                ServerSocket serverSocket = new ServerSocket(safeGpsSocketPort);
                serverSocket.setSoTimeout(5000);
                log.info("开始接收GPS数据服务启动成功,端口:{},开始监听连接", safeGpsSocketPort);
                while (true) {
                    Socket socket = null;
                    try {
                        //等待客户端连接，阻塞式方法
                        socket = serverSocket.accept();
                        socket.setKeepAlive(true);
                        socket.setSoTimeout(2000);
                        //判断连接的平台是否是socket
                        String hostAddress = socket.getInetAddress().getHostAddress();
                        log.info("客户端:{}已连接！", hostAddress);
                        pool.execute(new ClientHandler(socket,sendOpen,gpsUrl));
                        //gps终端的连接
                        //启动读取客户端消息的子线程
                    }catch (Exception e){
                        log.info("本次连接异常!"+e.getMessage());
                        if (socket!=null && !socket.isClosed()){
                            socket.close();
                        }
                    }
                }
            } catch (Exception exception) {
                log.error("启动监听服务异常：{}", ExceptionUtil.getStackTrace(exception));
            }
        }).start();
    }

    public Result<FixSizeLinkedList<GpsDto>> queryGps(String gpsUniqueCode){
        String gpsData = "";
        if(CollectionUtils.isEmpty(gpsMap)){
            return Result.success(gpsData);
        }
        QueryGpsResDTO gpsResDTO = new QueryGpsResDTO();
        for (Map.Entry<String,QueryGpsResDTO> key : gpsMap.entrySet()){
            if(key.getKey().equals(gpsUniqueCode)){
                gpsResDTO = key.getValue();
                gpsData = gpsResDTO.getLongitude()+","+gpsResDTO.getLatitude();
            }
        }
        return Result.success(locationMap.get(gpsUniqueCode),gpsData);
    }

    private class ClientHandler implements Runnable{
        private Socket socket;
        private Boolean sendOpen;
        private String gpsUrl;


        public ClientHandler(Socket socket,Boolean sendOpen,String gpsUrl) {
            log.info("创建ClientHandler！");
            this.socket = socket;
            this.sendOpen = sendOpen;
            this.gpsUrl = gpsUrl;
        }


        @Override
        public void run() {
            try  {
                //socket 输入流 用于接收客户端消息
                InputStream in = socket.getInputStream();
                //socket 输出流 服务端箱客户端回复消息
                OutputStream os = socket.getOutputStream();
                //接收数据的byte数组
                byte[] bys = new byte[1024];
                //存储长度
                int len;
                while ((len = in.read(bys)) != -1) {
                    log.debug("读取数据中！");
                    String bytStr = "";
                    for (Byte b : bys){
                        bytStr = bytStr + b;
                    }
                    log.debug(bytStr);

                    //转换成16进制字符串
                    String messageHex = HexUtil.bytesToHexString(bys);
                    //进行转义 7d02 --> 7e    7d01 -->7d
                    messageHex = messageHex.replaceAll(GpsConstant.d702, GpsConstant.e7);
                    messageHex = messageHex.replaceAll(GpsConstant.d701, GpsConstant.d7);
                    //长度不足20的直接丢弃
                    if (messageHex.length() <= 20) {
                        continue;
                    }
                    log.debug("收到GPS数据16进制转字符串：{}", messageHex);

                    //解析消息
                    //消息id 4位
                    String id = messageHex.substring(2, 6);
                    //消息体长度 4位
                    String msgLength = messageHex.substring(6, 10);
                    //设备唯一号 12位
                    String gpsUniqueCode = messageHex.substring(10, 22);
                    //消息流水号
                    String seq = messageHex.substring(22, 26);
                    log.info("消息id：{}， 长度：{}， 设备唯一号：{}， 消息流水号：{}", id, msgLength, gpsUniqueCode, seq);

//                    if (!noOne.equals(gpsUniqueCode) && !noTwo.equals(gpsUniqueCode)){
//                        log.info("关闭socket连接，ip：{}",socket.getInetAddress().getHostAddress());
//                        socket.close();
//                    }

                    //响应
                    String resId = null;
                    String resStr = null;
                    if (GpsConstant.registerSendId.equals(id)){
                        //注册
                        resId = GpsConstant.registerReceiveId;
                        resStr = buildRegisterResStr(resId, GpsConstant.registerMessageLength, gpsUniqueCode, seq, GpsConstant.successCode, GpsConstant.registerValidateCode);
                    }
                    if (GpsConstant.validateSendId.equals(id)){
                        //鉴权
                        resId = GpsConstant.validateReceiveId;
                        resStr = buildValidateResStr(resId, GpsConstant.validateMessageLength, gpsUniqueCode, seq, GpsConstant.validateSendId, GpsConstant.successCode);
                    }
                    if (GpsConstant.positionSendId.equals(id)){
                        //位置信息
                        resId = GpsConstant.positionReceiveId;
                        //解析出经度、维度
                        String latitude = String.valueOf(Integer.parseInt(messageHex.substring(42, 50), 16));
                        String longitude =String.valueOf(Integer.parseInt( messageHex.substring(50, 58), 16));
                        String altitude = String.valueOf(Integer.parseInt(messageHex.substring(58, 62), 16));
                        String speed = String.valueOf(Integer.parseInt(messageHex.substring(62, 66), 16));
                        String direction = String.valueOf(Integer.parseInt(messageHex.substring(66, 70), 16));
                        String dateTime = messageHex.substring(70, 82);
                        //信号强度
                        Integer signalStrength = Integer.parseInt(messageHex.substring(98, 100), 16);
                        //电量
                        Integer quantityOfElectricity = Integer.parseInt(messageHex.substring(118, 122), 16);
                        //定位状态
                        Integer positioningStatus = Integer.parseInt(messageHex.substring(128, 130));
                        //经度扩展
                        Integer extend2longitude = Integer.parseInt(messageHex.substring(130, 132));
                        //纬度扩展
                        Integer extend2latitude = Integer.parseInt(messageHex.substring(132, 134));
                        //定位状态
                        log.info("纬度：{}，经度：{}，海拔高度：{}，速度：{}，方向：{}，日期时间：{}, 信号强度：{}， 电量：{}，定位状态：{}， 经度扩展：{}， 纬度扩展：{}", latitude, longitude, altitude, speed, direction, dateTime, signalStrength, quantityOfElectricity, positioningStatus, extend2longitude, extend2latitude);
                        if (GpsConstant.zero.equals(latitude) || GpsConstant.zero.equals(longitude)) {
                            log.info("经纬度数据为0，直接丢弃");

                        }

                        //存入map
                        QueryGpsResDTO gpsResDTO = new QueryGpsResDTO();
                        gpsResDTO.setLatitude(latitude);
                        gpsResDTO.setLongitude(longitude);
                        gpsMap.put(gpsUniqueCode,gpsResDTO);


                        GpsDto gpsDto = new GpsDto();
                        gpsDto.setLatitude(String.valueOf(new BigDecimal(latitude).divide(BigDecimal.valueOf(1000000))));
                        gpsDto.setLongitude(String.valueOf(new BigDecimal(longitude).divide(BigDecimal.valueOf(1000000))));
                        gpsDto.setAltitude(altitude);
                        gpsDto.setDirection(direction);
                        gpsDto.setDateTime(dateTime);
                        gpsDto.setExtendLatitude(extend2latitude);
                        gpsDto.setSeq(seq);
                        gpsDto.setInfoId(id);
                        gpsDto.setMsgLength(msgLength);
                        gpsDto.setSpeed(speed);
                        gpsDto.setExtendLongitude(extend2longitude);
                        gpsDto.setGpsUniqueCode(gpsUniqueCode);
                        gpsDto.setQuantityOfElectricity(quantityOfElectricity);
                        gpsDto.setPositioningStatus(positioningStatus);
                        gpsDto.setSignalStrength(signalStrength);
                        if (CollectionUtils.isEmpty(locationMap.get(gpsUniqueCode))){
                            log.info("创建"+gpsUniqueCode+"的定位数据集合");
                            FixSizeLinkedList<GpsDto> locationList = new FixSizeLinkedList<>(5);
                            locationMap.put(gpsUniqueCode,locationList);
                        }
                        locationMap.get(gpsUniqueCode).add(gpsDto);

                        //发送gps数据给接收服务接口
                        if (sendOpen){
                            String result = null;
                            log.info("发送gps数据给："+gpsUrl+"  gpsDto:"+gpsDto);
                            try{
                                //数据整合
                                TosMessageVO tosMessageVO = new TosMessageVO();
                                tosMessageVO.setUn(gpsDto.getGpsUniqueCode());
                                //todo 文档说明：要用MD5加密。目前时测试未加密，不确定后面是否要加密
//                        tosMessageVO.setPsd(MD5Utils.encryptMD5(externalConfigUtil.getPassword()));
                                tosMessageVO.setPsd(gpsDto.getGpsUniqueCode()+"@FCG2024");
                                //文档：要求要12位
                                tosMessageVO.setTimestamp(DateUtils.generate12DigitTimeStamp());
                                tosMessageVO.setReqbody(gpsDto);
                                //调用第三方接口
                                String request = JsonUtil.toJson(tosMessageVO);
                                result = restTemplateUtil.post(gpsUrl,request);
                                log.info("发送完成:"+result);
                            }catch (Exception e){
                                log.info("发送失败:"+result);
                            }
                        }
                        resStr = buildPositionResStr(resId, GpsConstant.positionMessageLength, gpsUniqueCode, seq, GpsConstant.positionSendId, GpsConstant.successCode);
                    }
                    if (GpsConstant.heartbeatSendId.equals(id)){
                        //心跳
                        resId = GpsConstant.heartbeatReceiveId;
                        resStr = buildHeartbeatResStr(resId, GpsConstant.heartbeatMessageLength, gpsUniqueCode, seq, GpsConstant.heartbeatSendId, GpsConstant.successCode);
                    }
                    if (StringUtils.isEmpty(resId)) {
                        log.info("未匹配到resId，丢弃"+resId);
                        continue;
                    }
                    //消息转换成数组
                    String validateCode = HexUtil.calculateChecksum(resStr);
                    StringBuffer sb = new StringBuffer();
                    resStr = sb.append(GpsConstant.startEndFlag).append(resStr).append(validateCode).append(GpsConstant.startEndFlag).toString();
                    log.debug("回复的消息:{}", resStr);
                    byte[] resBytes = HexUtil.hexTobytes(resStr);
                    os.write(resBytes);
                    os.flush();
                }
                log.info("run 方法执行完毕");
            }catch (IOException e) {
                log.info("客户端处理异常：{}", e.getMessage());
            }
        }

        private String buildHeartbeatResStr(String resId, String heartbeatMessageLength, String gpsUniqueCode, String seq, String heartbeatSendId, String successCode) {
            StringBuffer sb = new StringBuffer();
            return sb.append(resId).append(heartbeatMessageLength).append(gpsUniqueCode).append(seq).append(seq).append(heartbeatSendId).append(successCode).toString();
        }

        private String buildPositionResStr(String resId, String positionMessageLength, String gpsUniqueCode, String seq, String positionSendId, String successCode) {
            StringBuffer sb = new StringBuffer();
            return sb.append(resId).append(positionMessageLength).append(gpsUniqueCode).append(seq).append(seq).append(positionSendId).append(successCode).toString();
        }


        private String buildValidateResStr(String resId, String validateMessageLength, String gpsUniqueCode, String seq, String validateSendId, String successCode) {
            StringBuffer sb = new StringBuffer();
            return sb.append(resId).append(validateMessageLength).append(gpsUniqueCode).append(seq).append(seq).append(validateSendId).append(successCode).toString();
        }

        private String buildRegisterResStr(String resId, String registerMessageLength, String gpsUniqueCode, String seq, String successCode, String registerValidateCode) {
            StringBuffer sb = new StringBuffer();
            return sb.append(resId).append(registerMessageLength).append(gpsUniqueCode).append(seq).append(seq).append(successCode).append(registerValidateCode).toString();
        }

    }

}
