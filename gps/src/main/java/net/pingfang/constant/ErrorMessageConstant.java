package net.pingfang.constant;

/**
 * @description 描述：提示异常信息
 * <AUTHOR>
 * @date 2022/4/1 17:52
*/
public interface ErrorMessageConstant {

    interface  CommonErrorMessage{
        String USER_NOT_FOUND = "用户未找到!";
        String MISSING_REQUIRED_PARAM = "缺失必填参数!";
        String INVALID_PARAM = "参数不合法!";
    }

    interface  UserErrorMessage{
        String FAIL = "操作失败！";
        String USER_NOT_EXIST = "用户不存在";
        String USERNAME_PASSWORD_NOT_EMPTY = "用户名/密码不能为空";
        String PASSWORD_LENGTH_LESS = "密码长度不能少于6位";
        String PASSWORD_NOT_NULL = "密码不能为空";
        String CURRENT_USER_NOT_EXIST = "当前用户不存在";
        String USERNAME_EXIST = "用户名已存在";
        String JOB_NUMBER_EXIST = "工号已存在";
        String OLD_PASSWORD_ERROR = "旧密码错误";
        String NEW_OLD_PASSWORD_EMPTY_ERROR = "新旧密码不能为空";
    }

    interface  RoleErrorMessage{
        String ROLE_NAME_EMPTY = "角色名称不能为空!";
        String ROLE_NOT_EXIST = "角色不存在";

    }

    interface  PermissionErrorMessage{
        String PERMISSION_ADD_FAIL = "权限插入失败!";
        String PERMISSION_NOT_EXIST = "权限不存在";
    }

}
