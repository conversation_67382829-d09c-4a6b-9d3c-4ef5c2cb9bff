package net.pingfang.constant;

/**
 * GPs常量类
 *
 * @Author: CM
 * @Date: 2022/7/15 16:35
 */
public interface GpsConstant {
    String zero = "0";

    String safeOperateIp = "127.0.0.1";

    //标识位
    String startEndFlag = "7e";
    //回复的消息成功值
    String successCode = "00";

    //注册相关常量
    //终端id
    String registerSendId = "0100";
    //回复的消息id
    String registerReceiveId = "8100";
    //回复的消息体长度 15个字节
    String registerMessageLength = "000F";
    //回复的鉴权码
    String registerValidateCode = "303937303430303436313635";

    //鉴权相关常量
    //终端id
    String validateSendId = "0102";
    //回复的消息id
    String validateReceiveId = "8001";
    //回复的消息体长度 5个字节
    String validateMessageLength = "0005";

    //位置信息消息
    //终端id
    String positionSendId = "0200";
    //回复的消息id
    String positionReceiveId = "8001";
    //回复的消息体长度 5个字节
    String positionMessageLength = "0005";


    //心跳信息消息
    //终端id
    String heartbeatSendId = "0002";
    String heartbeatReceiveId = "8001";
    //回复的消息体长度 5个字节
    String heartbeatMessageLength = "0005";



    //转义相关
    String d702 = "7d02";
    String d701 = "7d01";
    String e7 = "7e";
    String d7 = "7d";


}
