#server portåå°æå¡ç«¯å£(æ ¹æ®é¡¹ç®ä¿®æ¹)
server.port=13000

#service nameæå¡åç§°
spring.application.name=gps_transfer

#log setting
#æ¥å¿çº§å«,ä¸éè¦ä¿®æ¹
logging.level.root=info
logging.pattern.file=%d{yyyy/MM/dd HH:mm:ss.SSS} %clr(%p)[TraceId:%3X{traceId}] [%logger][%M][%L] : %m%n
logging.pattern.console=%d{yyyy/MM/dd HH:mm:ss.SSS} %clr(%p)[TraceId:%3X{traceId}] [%logger][%M][%L] : %m%n
#æ¥å¿å­æ¾è·¯å¾(æ ¹æ®é¡¹ç®ä¿®æ¹)
logging.file.path=D:/log/template
#æ¥å¿æé¿ä¿çå¤©æ°(æ ¹æ®é¡¹ç®ä¿®æ¹)
logging.file.max-history=7
#æ¥å¿æä»¶åå²å¤§å°,è¶è¿10Måå°çææ°çæä»¶ä¿å­
logging.file.max-size=10MB

#GPSæ°æ®æ¯å¦åé
gps.send.open=false
#GPSæ°æ®åéå°å
gps.send.url=http://192.168.1.31:9703/gps/save

#GPSæå¡ç«¯å£
safe.gps.socket.port=12345




